<view class="verify-container">
  <!-- 返回按钮 -->
  <view class="back-button" bindtap="goBack">
    <image src="/static/icon/back.png" mode="aspectFit"></image>
  </view>
  
  <!-- 验证码标题 -->
  <view class="verify-title">输入您的4位验证码</view>
  
  <!-- 验证码输入框 -->
  <view class="code-input-container">
    <view class="code-input-group">
      <view class="code-input {{activeIndex === 0 ? 'active' : ''}}" bindtap="focusInput">
        <text>{{codeArr[0] || ''}}</text>
      </view>
      <view class="code-input {{activeIndex === 1 ? 'active' : ''}}" bindtap="focusInput">
        <text>{{codeArr[1] || ''}}</text>
      </view>
      <view class="code-input {{activeIndex === 2 ? 'active' : ''}}" bindtap="focusInput">
        <text>{{codeArr[2] || ''}}</text>
      </view>
      <view class="code-input {{activeIndex === 3 ? 'active' : ''}}" bindtap="focusInput">
        <text>{{codeArr[3] || ''}}</text>
      </view>
    </view>
    
    <!-- 隐藏的输入框，用于接收键盘输入 -->
    <input 
      class="hidden-input" 
      type="number" 
      maxlength="4" 
      focus="{{isFocus}}" 
      value="{{verifyCode}}" 
      bindinput="onInput"
    />
    
    <!-- 清除按钮 -->
    <view class="clear-btn" bindtap="clearCode">
      <text>清除</text>
    </view>
  </view>
  
  <!-- 倒计时提示 -->
  <view class="countdown-tip">
    <text>{{countdown}}秒后重试</text>
  </view>
  
  <!-- 确认按钮 -->
  <button class="confirm-btn" bindtap="confirmCode">确认</button>
</view>

<!-- 添加成功弹窗 -->
  
<view class="success-dialog" wx:if="{{showSuccessDialog}}">
  <view class="success-dialog-content">
    <view class="close-icon" bindtap="closeSuccessDialog">×</view>
    <view class="success-title">注册成功</view>
    <view class="success-message">恭喜你完成注册，点击确定立即登录吧🎉</view>
    <view class="confirm-btn" bindtap="closeSuccessDialog">确认</view>
  </view>
</view>