// packageA/pages/help/smsService/smsService.js
import { getCategory } from '../../../../api/basic/index';
const util = require('../../../../utils/index');
Page({
  data: {
    activeTab: 'expenses', // 默认显示支出分类
    incomeCategories: [],
    expensesCategories: [],
    loading: {
      income: false,
      expenses: false
    },
    collapseValues: {} // 用于存储每个折叠面板的状态
  },

  /**
   * 处理TDesign Tab切换事件
   */
  onTabChange(event) {
    const tab = event.detail.value;
    if (tab !== this.data.activeTab) {
      this.setData({ activeTab: tab });
      
      // 每次切换标签都重新加载数据
      this.fetchCategories(tab);
    }
  },

  /**
   * 切换分类标签
   */
  switchTab(e) {
    const tab = e.currentTarget.dataset.tab;
    if (tab !== this.data.activeTab) {
      this.setData({ activeTab: tab });
      
      // 每次切换标签都重新加载数据
      this.fetchCategories(tab);
    }
  },

  /**
   * 处理TDesign Collapse组件的展开/收起事件
   */
  onCollapseChange(event) {
    // 获取当前点击的父分类ID
    const parentId = event.currentTarget.dataset.parentId;
    // 获取折叠状态
    const activeValue = event.detail.value;
    
    // 更新指定父分类的折叠状态
    this.setData({
      [`collapseValues.${parentId}`]: activeValue
    });
  },

  /**
   * 处理分类数据，递归处理图标路径
   */
  processCategories(categories) {
    if (!categories || !categories.length) return [];
    
    return categories.map(section => {
      // 处理部分
      let processedSection = { ...section };
      
      // 如果有image，处理image路径
      if (processedSection.image) {
        processedSection.image = util.getImageUrl(processedSection.image);
      }
      
      // 处理children字段（如果存在）
      if (processedSection.children && processedSection.children.length) {
        processedSection.children = processedSection.children.map(item => {
          let processedItem = { ...item };
          if (processedItem.image) {
            processedItem.image = util.getImageUrl(processedItem.image);
          }
          
          // 处理更深层的子分类（如果存在）
          if (processedItem.child && processedItem.child.length) {
            processedItem.child = processedItem.child.map(childItem => {
              let processedChild = { ...childItem };
              if (processedChild.image) {
                processedChild.image = util.getImageUrl(processedChild.image);
              }
              return processedChild;
            });
          }
          
          return processedItem;
        });
      }
      
      // 处理child字段（如果存在）
      if (processedSection.child && processedSection.child.length) {
        processedSection.child = processedSection.child.map(item => {
          let processedItem = { ...item };
          if (processedItem.image) {
            processedItem.image = util.getImageUrl(processedItem.image);
          }
          
          // 处理更深层的子分类（如果存在）
          if (processedItem.child && processedItem.child.length) {
            processedItem.child = processedItem.child.map(childItem => {
              let processedChild = { ...childItem };
              if (processedChild.image) {
                processedChild.image = util.getImageUrl(processedChild.image);
              }
              return processedChild;
            });
          }
          
          return processedItem;
        });
      }
      
      // 处理列表（如果存在）
      if (processedSection.list && processedSection.list.length) {
        processedSection.list = processedSection.list.map(item => {
          let processedItem = { ...item };
          if (processedItem.image) {
            processedItem.image = util.getImageUrl(processedItem.image);
          }
          
          // 处理子分类（如果存在）
          if (processedItem.child && processedItem.child.length) {
            processedItem.child = processedItem.child.map(childItem => {
              let processedChild = { ...childItem };
              if (processedChild.image) {
                processedChild.image = util.getImageUrl(processedChild.image);
              }
              return processedChild;
            });
          }
          
          return processedItem;
        });
      }
      
      return processedSection;
    });
  },

  /**
   * 获取分类数据
   */
  fetchCategories(type = 'expenses') {
    // 设置对应类型的loading状态
    const loadingStatus = { ...this.data.loading };
    loadingStatus[type] = true;
    this.setData({ loading: loadingStatus });
    
    getCategory({ type }).then(res => {
      console.log(`${type}分类数据:`, res);
      
      if (res && res.data) {
        // 处理图片路径
        const processedData = this.processCategories(res.data);
        
        // 更新对应类型的数据
        if (type === 'income') {
          this.setData({ 
            incomeCategories: processedData || []
          });
        } else {
          this.setData({ 
            expensesCategories: processedData || []
          });
        }
      }
      
      // 重置loading状态
      const newLoadingStatus = { ...this.data.loading };
      newLoadingStatus[type] = false;
      this.setData({ loading: newLoadingStatus });
      
    }).catch(err => {
      console.error(`获取${type}分类失败:`, err);
      
      // 重置loading状态
      const newLoadingStatus = { ...this.data.loading };
      newLoadingStatus[type] = false;
      this.setData({ loading: newLoadingStatus });
      
      wx.showToast({
        title: `获取${type === 'income' ? '收入' : '支出'}分类失败`,
        icon: 'none' 
      });
    });
  },

  /**
   * 生命周期函数--监听页面加载
   */
  onLoad(options) {
    // 设置导航栏标题
    wx.setNavigationBarTitle({
      title: '收支分类',
    });
    
    // 获取默认分类数据（支出）
    this.fetchCategories('expenses');
  },

  /**
   * 生命周期函数--监听页面初次渲染完成
   */
  onReady() {

  },

  /**
   * 生命周期函数--监听页面显示
   */
  onShow() {

  },

  /**
   * 生命周期函数--监听页面隐藏
   */
  onHide() {

  },

  /**
   * 生命周期函数--监听页面卸载
   */
  onUnload() {

  },

  /**
   * 页面相关事件处理函数--监听用户下拉动作
   */
  onPullDownRefresh() {
    // 刷新当前分类列表
    this.fetchCategories(this.data.activeTab);
    wx.stopPullDownRefresh();
  },

  /**
   * 页面上拉触底事件的处理函数
   */
  onReachBottom() {

  },

  /**
   * 用户点击右上角分享
   */
  onShareAppMessage() {
    return {
      title: '小猫记账 - 收支分类',
      path: '/packageA/pages/help/smsService/smsService'
    };
  }
})