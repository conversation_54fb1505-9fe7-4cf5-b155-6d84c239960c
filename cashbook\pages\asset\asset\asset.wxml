<!-- 资产页面 -->
<view class="asset-container">
  <!-- 顶部导航栏 -->
  <view class="nav-header" style="padding-top: {{statusBarHeight}}px;">
    <view class="nav-back" bindtap="navigateBack">
      <van-icon name="arrow-left" size="20px" color="#fff" />
    </view>
    <view class="nav-title">资产</view>
    <view class="nav-right">
      <van-icon name="credit-pay" size="24px" color="#fff" />
    </view>
  </view>

  <!-- 资产总计卡片 -->
  <view class="asset-total-card">
    <view class="asset-total-header">
      <view class="asset-total-title">合计</view>
      <view class="asset-add-btn" bindtap="onAddAsset">添加</view>
    </view>

    <view class="asset-total-content">
      <view class="asset-total-label">净资产(元)</view>
      <view class="asset-total-amount">¥{{netAssets}}</view>

      <view class="asset-details">
        <view class="asset-detail-item">
          <text>总资产</text>
          <text>{{totalAssets}}</text>
        </view>
        <view class="asset-detail-item">
          <text>总负债</text>
          <text>{{totalLiabilities}}</text>
        </view>
      </view>
    </view>
  </view>

  <!-- 资产变动区域 -->
  <view class="asset-changes-section" bindtap="toggleAssetChangesDetail">
    <view class="asset-changes-header">
      <view class="asset-changes-title">
        <view class="blue-bar"></view>
        <text>资产变动</text>
        <view class="asset-changes-icon">
          <van-icon name="gold-coin" size="20px" color="#FFD700" />
        </view>
      </view>
      <view class="asset-changes-crown">
        <van-icon name="crown" size="20px" color="#FFD700" />
      </view>
    </view>

    <!-- 资产变动详情 -->
    <view class="asset-changes-detail {{showAssetChangesDetail ? 'show' : ''}}">
      <view class="asset-changes-info">
        <view class="asset-changes-date">2025年5月29日 余额¥0.00</view>
        <view class="asset-changes-actions">
          <view class="asset-changes-action">
            <van-icon name="medal-o" size="20px" color="#FFD700" />
          </view>
          <view class="asset-changes-action">
            <van-icon name="setting-o" size="20px" color="#999" />
          </view>
          <view class="asset-changes-action">
            近1月
          </view>
        </view>
      </view>

      <view class="asset-changes-stats-button" catchtap="enableStatsData">
        开启数据统计
      </view>

      <view class="asset-changes-empty-tip">
        暂无资产流动数据，无法查看
      </view>
    </view>

    <!-- 引用 assetList 组件 -->
    <assetList
      show-amount="{{showAccount}}"
      asset-list="{{assetList}}"
      theme-color="{{themeColor}}"
      bind:assetclick="onAssetItemClick"
      bind:addasset="onAddAsset"
      bind:setting="onAssetSetting"
      bind:hideasset="onHideAsset"
      bind:editasset="onEditAsset"
      bind:deleteasset="onDeleteAsset"
    ></assetList>
  </view>
</view>
