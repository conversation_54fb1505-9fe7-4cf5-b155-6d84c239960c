// packageA/pages/help/agreementList/agreementList.js
import { getAgreementList } from '../../../../api/basic/index';
Page({

  /**
   * 页面的初始数据
   */
  data: {
    agreementList: [],
    loading: true,
  },

  /**
   * 获取协议列表
   */
  fetchAgreementList() {
    this.setData({ loading: true });
    
    getAgreementList().then(res => {
      console.log('协议列表:', res);
      if (res && res.data) {
        this.setData({
          agreementList: res.data,
          loading: false
        });
      } else {
        this.setData({
          agreementList: [],
          loading: false
        });
      }
    }).catch(err => {
      console.error('获取协议列表失败:', err);
      this.setData({
        loading: false
      });
      wx.showToast({
        title: '获取协议列表失败',
        icon: 'none'
      });
    });
  },

  /**
   * 查看协议详情
   */
  viewAgreementDetail(e) {
    const { title } = e.currentTarget.dataset;
    wx.navigateTo({
      url: `/packageA/pages/help/agreementDetail/agreementDetail?title=${title}`,
    });
  },

  /**
   * 生命周期函数--监听页面加载
   */
  onLoad(options) {
    // 设置导航栏标题
    wx.setNavigationBarTitle({
      title: '用户协议',
    });
    
    // 获取协议列表
    this.fetchAgreementList();
  },

  /**
   * 生命周期函数--监听页面初次渲染完成
   */
  onReady() {

  },

  /**
   * 生命周期函数--监听页面显示
   */
  onShow() {
    // 页面显示时，如果数据为空则重新获取
    if (this.data.agreementList.length === 0) {
      this.fetchAgreementList();
    }
  },

  /**
   * 生命周期函数--监听页面隐藏
   */
  onHide() {

  },

  /**
   * 生命周期函数--监听页面卸载
   */
  onUnload() {

  },

  /**
   * 页面相关事件处理函数--监听用户下拉动作
   */
  onPullDownRefresh() {
    this.fetchAgreementList();
    wx.stopPullDownRefresh();
  },

  /**
   * 页面上拉触底事件的处理函数
   */
  onReachBottom() {

  },

  /**
   * 用户点击右上角分享
   */
  onShareAppMessage() {
    return {
      title: '小猫记账 - 用户协议',
      path: '/packageA/pages/help/agreementList/agreementList'
    };
  }
})