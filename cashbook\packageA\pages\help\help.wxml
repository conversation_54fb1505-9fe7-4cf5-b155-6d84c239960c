<!-- packageA/pages/help/help.wxml -->
<view class="content">
  <!-- 头部导航栏 -->
  <view class="nav">
    <customNav isRight="{{true}}" isClose="{{true}}" isIcon="{{false}}"></customNav>
  </view>
  <!-- 导航栏占位符 -->
  <view class="nav-placeholder"></view>
  <!-- 帮助信息卡片 -->
  <view class="help-card">
    <view class="help-title">
      <view class="icon-container">
        <image class="help-icon" src="/static/cateIcon/cate1.png" mode="aspectFit"></image>
      </view>
      <text>帮助</text>
      <!-- 跳转到帮助页面 -->
      <view class="help-tag" bind:tap="goIntroduction">帮助</view>
    </view>
    <view class="help-content">
      如果你在使用小青账📝遇到一些问题，可在常见问题里查看回答，或者联系客服人员哦😊
      <!-- <image class="decoration" src="/static/icon/decoration.png" mode="aspectFit"></image> -->
    </view>
  </view>
  <!-- 功能指南区域 -->
  <view class="guide-section">
    <!-- 加载提示 -->
    <view class="loading" wx:if="{{!guideList || guideList.length === 0}}">
      <text>加载中...</text>
    </view>
    <!-- 动态生成指南卡片 - 改为每行两张卡片 -->
    <view class="guide-cards-container">
      <view class="guide-card" wx:for="{{guideList}}" wx:key="id"
            bindtap="showVideoPopup" data-id="{{item.id}}" data-title="{{item.title}}" data-video="{{item.video}}">
        <view class="guide-title">
          <view class="guide-tag"></view>
          <text>{{item.title}}</text>
        </view>
        <view class="guide-video">视频介绍</view>
        <view class="guide-dots">...</view>
      </view>
    </view>
  </view>
  <!-- 视频弹窗 - 全屏样式 -->
  <view class="fullscreen-video-popup {{fadeOutClass}}" wx:if="{{showVideoPopup}}">
    <!-- 安全区域占位符 -->
    <view class="safe-area-top"></view>
    
    <!-- 顶部导航 -->
    <view class="popup-header">
      <view class="popup-close" bindtap="hideVideoPopup">×</view>
      <view class="popup-title">{{currentVideo.title || '账单标签'}}</view>
      <!-- 右侧空白占位，保持标题居中 -->
      <view class="popup-close-placeholder"></view>
    </view>
    
    <!-- 功能标题 -->
    <!-- <view class="feature-title">
      <text class="dot"></text>
      <text class="title-text">标签功能</text>
    </view> -->
    
    <!-- 视频播放区域 -->
    <view class="popup-video-container">
      <video 
        id="popupVideo" 
        src="{{currentVideo.videoUrl}}" 
        autoplay="{{true}}"
        show-center-play-btn="{{false}}"
        show-play-btn="{{false}}"
        show-fullscreen-btn="{{false}}"
        controls="{{false}}"
        bindended="onVideoEnded"
        binderror="onVideoError"
        bindtimeupdate="onVideoTimeUpdate"
        class="popup-video-player">
      </video>
    </view>
    
    <!-- 自定义视频控制器 -->
    <view class="custom-video-controls">
      <view class="progress-bar">
        <view class="progress-bg"></view>
        <view class="progress-current" style="width: {{videoProgress}}%"></view>
      </view>
      <view class="control-buttons">
        <view class="playback-rate" bindtap="changePlaybackRate">
          {{playbackRate}}x
        </view>
        <view class="play-pause-btn" bindtap="togglePlayPause">
          <text class="{{isPlaying ? 'pause-icon' : 'play-icon'}}">{{isPlaying ? '||' : '▶'}}</text>
        </view>
      </view>
    </view>
  </view>
</view>