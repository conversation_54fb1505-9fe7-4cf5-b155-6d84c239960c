/* packageA/pages/help/videoDetail/videoDetail.scss */

.video-detail-container {
  width: 100%;
  min-height: 100vh;
  display: flex;
  flex-direction: column;
  background-color: #fee7ea; /* 粉色背景 */
  position: relative;
  padding-bottom: 100rpx;
}

/* 顶部导航 */
.nav-bar {
  width: 100%;
  height: 90rpx;
  display: flex;
  align-items: center;
  justify-content: center;
  position: relative;
  padding: 20rpx 0;
}

.close-btn {
  position: absolute;
  left: 30rpx;
  top: 50%;
  transform: translateY(-50%);
  width: 60rpx;
  height: 60rpx;
  border-radius: 50%;
  background-color: rgba(255, 255, 255, 0.8);
  display: flex;
  align-items: center;
  justify-content: center;
  box-shadow: 0 2rpx 8rpx rgba(0, 0, 0, 0.1);
}

.close-icon {
  font-size: 40rpx;
  font-weight: bold;
  color: #333;
}

.video-title {
  font-size: 36rpx;
  font-weight: bold;
  color: #333;
  text-align: center;
}

/* 功能标题 */
.feature-title {
  width: 100%;
  display: flex;
  align-items: center;
  padding: 30rpx;
}

.dot {
  width: 16rpx;
  height: 16rpx;
  border-radius: 50%;
  background-color: #ffcc00;
  margin-right: 16rpx;
}

.title-text {
  font-size: 42rpx;
  font-weight: bold;
  color: #5dd5db; /* 蓝绿色 */
  text-shadow: 0 2rpx 4rpx rgba(0, 0, 0, 0.15);
}

/* 视频播放区域 */
.video-player-container {
  width: 100%;
  height: calc(100vh - 300rpx);
  position: relative;
  margin-top: 20rpx;
}

.video-player {
  width: 100%;
  height: 100%;
  background-color: transparent;
}

/* 自定义视频控制器 */
.custom-video-controls {
  position: fixed;
  bottom: 0;
  left: 0;
  width: 100%;
  background-color: rgba(255, 255, 255, 0.5);
  padding: 20rpx 30rpx;
  box-sizing: border-box;
  backdrop-filter: blur(10rpx);
}

.progress-bar {
  width: 100%;
  height: 6rpx;
  background-color: rgba(0, 0, 0, 0.1);
  position: relative;
  border-radius: 3rpx;
  overflow: hidden;
  margin-bottom: 20rpx;
}

.progress-bg {
  position: absolute;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
  background-color: rgba(0, 0, 0, 0.1);
}

.progress-current {
  position: absolute;
  top: 0;
  left: 0;
  height: 100%;
  background-color: #97bf75; /* 绿色进度条 */
  border-radius: 3rpx;
  transition: width 0.1s linear;
}

.control-buttons {
  display: flex;
  justify-content: flex-end;
  align-items: center;
  padding: 10rpx 0;
}

.playback-rate {
  width: 80rpx;
  height: 80rpx;
  border-radius: 50%;
  background-color: #97bf75; /* 绿色播放速率按钮 */
  display: flex;
  align-items: center;
  justify-content: center;
  font-size: 28rpx;
  color: white;
  margin-right: 20rpx;
}

.play-pause-btn {
  width: 80rpx;
  height: 80rpx;
  border-radius: 50%;
  background-color: #97bf75; /* 绿色播放暂停按钮 */
  display: flex;
  align-items: center;
  justify-content: center;
  color: white;
}

.play-icon, .pause-icon {
  font-size: 32rpx;
  font-weight: bold;
}

/* 加载中 */
.loading-container {
  position: absolute;
  top: 50%;
  left: 50%;
  transform: translate(-50%, -50%);
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
}

.loading-icon {
  width: 80rpx;
  height: 80rpx;
  border: 4rpx solid rgba(151, 191, 117, 0.2);
  border-top: 4rpx solid #97bf75;
  border-radius: 50%;
  animation: spin 1s linear infinite;
  margin-bottom: 20rpx;
}

@keyframes spin {
  0% { transform: rotate(0deg); }
  100% { transform: rotate(360deg); }
}
