<view class="content">
  <view class="nav">
    <customNav isRight="{{false}}" isTitle="购物清单" isIcon="{{false}}" rightText="管理"></customNav>
  </view>

  <view class="card3">
    <view class="header3">
      <view>购物清单信息</view>
    </view>
    <!-- br -->
    <view class="gridTmeplte m10">
      <view class="avator avatorBg brr" bind:tap="chooseimages">
        <!-- <image src="/static/icon/avator.png" mode="" /> -->
        <span wx:if="{{detail.image}}">{{detail.image}}</span>
        <span wx:else>😄</span>
      </view>
      <view>
        <view><span wx:if="{{detail.name}}">{{detail.name}}</span><span wx:else>-</span></view>
        <view class="description2 noMP fs14"><span wx:if="{{detail.notes}}">{{detail.notes}}</span><span wx:else>-</span></view>
      </view>
    </view>
    <!-- br -->
    <view class="editInput">
         
      <input-field
        label="名称(最多15个字符)"
        value="{{detail.name}}"
        placeholder="名称(最多15个字符)"
        field-name="username"
        highlight-color="#59b5dd"
        bind:input="onInput"
        bind:focus="onInputFocus"
        bind:blur="onInputBlur"
      />
    </view>
    <view class="editInput">
      <input-field
        label="备注(最多150个字符)"
        value="{{detail.notes}}"
        placeholder="备注(最多150个字符)"
        field-name="username"
        highlight-color="#59b5dd"
        bind:input="onInputt"
        bind:focus="onInputFocus"
        bind:blur="onInputBlur"
      />
    </view>
  </view>

  <!-- 购物清单时间 -->
  <view class="card3" wx:if="{{isAdd}}">
    <view class="header3">
      <view>购物清单任务</view>
    </view>
    <!-- br -->
    <view class="gridTmeplte m10" bind:tap="gotime">
      <view class="avator avatorBg col2">
        <image src="/static/icon/card.png" mode="" />
      </view>
      <view>
        <view>开始时间</view>
        <view>{{starttime}}</view>
      </view>
      <view class="icon2">
        <image src="/static/icon/arrRight.png" mode="" />
      </view>
    </view>
    <!-- br -->
    <view class="gridTmeplte" bind:tap="gotime1">
      <view class="avator avatorBg col2">
        <image src="/static/icon/card.png" mode="" />
      </view>
      <view>
        <view>结束时间</view>
        <view>{{endtime}}</view>
      </view>
      <view class="icon2">
        <image src="/static/icon/arrRight.png" mode="" />
      </view>
    </view>
  </view>
</view>
<view class="botmBtn" bind:tap="addshoppingList">保存</view>
<!-- 弹出选择器 -->
<!-- datetime-picker.wxml -->
<!-- datetime-picker.wxml -->
<view class="datetime-popup" wx:if="{{showPicker}}">
  <!-- 遮罩 -->
  <view class="mask" bindtap="closePicker"></view>

  <!-- 主体弹窗 -->
  <view class="popup-body animate-up">
    <!-- 顶部 年月切换 -->
    <view class="popup-header">
      <text class="header-left" bindtap="toggleYearSelector">{{ selectedDate.year }}年{{ selectedDate.month }}月</text>
      <view class="header-right">
        <text bindtap="prevMonth"><van-icon name="arrow-left" /></text>
        <text bindtap="nextMonth"><van-icon name="arrow" /></text>
      </view>
    </view>

    <!-- 年份选择器 -->
    <view class="year-selector-overlay" wx:if="{{showYearSelector}}">
      <view class="year-grid">
        <block wx:for="{{years}}" wx:key="year">
          <view class="year-cell {{selectedDate.year == item ? 'active' : ''}}" bindtap="selectYear" data-year="{{item}}">{{item}}年</view>
        </block>
      </view>
    </view>

    <!-- 日历部分：swiper 月份滑动 -->
    <swiper current="{{swiperCurrent}}" class="calendar-swiper" bindchange="onSwiperChange">
      <block wx:for="{{monthsData}}" wx:key="month" wx:for-item="monthItem">
        <swiper-item>
          <view class="calendar">
            <view class="calendar-month">{{monthItem.month}}月</view>

            <view class="week-header">
              <block wx:for="{{weekDays}}" wx:key="index">
                <text>{{item}}</text>
              </block>
            </view>

            <view class="days">
              <block wx:for="{{monthItem.days}}" wx:key="dayIndex" wx:for-item="dayItem">
                <text class="day-item {{dayItem.selected ? 'selected' : ''}}" bindtap="selectDay" data-day="{{dayItem.day}}"> {{dayItem.day}} </text>
              </block>
            </view>
          </view>
        </swiper-item>
      </block>
    </swiper>

    <!-- 时间选择 -->
    <view class="time-ctrl" wx:if="{{showTimeSelector}}">
      <picker mode="time" value="{{timeString}}" bindchange="onTimeChange">
        <view class="time-display">当前时间：{{ timeString }}</view>
      </picker>
    </view>

    <!-- 底部按钮 -->
    <view class="popup-footer">
      <text class="btn" bindtap="toggleTimeSelector">选择时间</text>
      <button type="primary" bindtap="confirmDateTime" style="margin: 0">确定</button>
    </view>
  </view>
</view>
<customPopup visible="{{chooseimage}}" title="选择图标" position="bottom" round="{{true}}" bind:close="closeSettingsPopup" closeButtonPosition="left" maxHeight="70%">
  <view style="width: 93%; display: flex; justify-content: space-between; margin: 20rpx 0; align-items: center; flex-wrap: wrap">
    <view class="avator avatorBg brr" bind:tap="chooseimages" style="margin-bottom: 20rpx">
      <!-- <image src="/static/icon/avator.png" mode="" /> -->
      <span wx:if="{{detail.image}}">{{detail.image}}</span>
      <span wx:else>😄</span>
    </view>
    <view style="width: 70%">
      <input-field
        label="emoji"
        value="{{detail.image}}"
        placeholder="请输入表情或字符"
        field-name="username"
        highlight-color="#59b5dd"
        bind:input="onInputimage"
        bind:focus="onInputFocus"
        bind:blur="onInputBlur"
        style="margin-bottom: 0 !important"
      />
    </view>
  </view>
  <view style="width: 100%; height: 100rpx"></view>
  <view class="botmBtn" bind:tap="addShop">保存</view>
</customPopup>
