.content {
  background-color: #f5f5f5;
  min-height: 100vh;
  padding-bottom: 60px;
  position: relative;
}

/* 头部图标和描述 */
.header-section {
  background-color: white;
  padding: 20px;
  display: flex;
  flex-direction: column;
  align-items: center;
  margin-bottom: 10px;
  border-radius: 0 0 10px 10px;
}

.icon-container {
  width: 60px;
  height: 60px;
  background-color: #f5f5f5;
  border-radius: 50%;
  display: flex;
  justify-content: center;
  align-items: center;
  margin-bottom: 10px;
}

.header-icon {
  width: 30px;
  height: 30px;
}

.header-text {
  color: #999;
  font-size: 14px;
}

/* 分区标题 */
.section-title {
  display: flex;
  align-items: center;
  padding: 15px;
  font-size: 14px;
  color: #333;
}

.section-indicator {
  width: 3px;
  height: 16px;
  background-color: #4cd964;
  margin-right: 8px;
  border-radius: 3px;
}

/* 设置项 */
.setting-item {
  display: flex;
  justify-content: space-between;
  align-items: center;
  background-color: white;
  padding: 15px;
  margin-bottom: 1px;
}

.setting-left {
  display: flex;
  align-items: center;
}

.setting-icon {
  width: 24px;
  height: 24px;
  margin-right: 10px;
  display: flex;
  justify-content: center;
  align-items: center;
}

.setting-icon image {
  width: 20px;
  height: 20px;
}

.setting-right {
  display: flex;
  align-items: center;
  color: #999;
  font-size: 14px;
}

.arrow-icon {
  width: 16px;
  height: 16px;
  margin-left: 5px;
}

.follow-system {
  font-size: 14px;
  color: #999;
}

/* 底部导航栏 */
.bottom-tabbar {
  position: fixed;
  bottom: 0;
  left: 0;
  right: 0;
  height: 50px;
  background-color: white;
  display: flex;
  justify-content: space-around;
  align-items: center;
  border-top: 1px solid #eee;
}

.tab-item {
  display: flex;
  justify-content: center;
  align-items: center;
}

.tab-item image {
  width: 24px;
  height: 24px;
}
.box {
  width: 100px;
  height: 100px;
  margin: 10px;
}