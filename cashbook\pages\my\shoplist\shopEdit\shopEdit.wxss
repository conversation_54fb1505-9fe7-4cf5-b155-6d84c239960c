/* pages/my/shoplist/shopEdit/shopEdit.wxss */
/* 日历 */
.datetime-popup {
    position: fixed;
    bottom: 0;
    left: 0;
    right: 0;
    z-index: 1000;
  }
  .mask {
    position: fixed;
    top: 0;
    left: 0;
    right: 0;
    bottom: 0;
    background: rgba(0, 0, 0, 0.5);
  }
  .popup-body {
    background: #fff;
    border-top-left-radius: 16rpx;
    border-top-right-radius: 16rpx;
    padding: 20rpx;
    position: relative;
    max-height: 80vh;
    overflow: hidden;
    z-index: 9999;
  }
  .popup-header {
    display: flex;
    justify-content: space-between;
    align-items: center;
    font-size: 32rpx;
    margin-bottom: 20rpx;
  }
  .year-selector-overlay {
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    background: #fff;
    z-index: 20;
    padding: 20rpx;
  }
  .year-grid {
    display: flex;
    flex-wrap: wrap;
    justify-content: space-between;
  }
  .year-cell {
    width: 30%;
    text-align: center;
    margin-bottom: 20rpx;
    padding: 20rpx 0;
    font-size: 28rpx;
    border-radius: 12rpx;
    background-color: #f5f5f5;
  }
  .year-cell.active {
    background-color: #1890ff;
    color: #fff;
  }
  .calendar-swiper {
    height: 600rpx;
  }
  .calendar {
    padding: 20rpx 0;
  }
  .calendar-month {
    font-size: 30rpx;
    font-weight: bold;
    margin: 20rpx 0 10rpx;
    text-align: center;
  }
  .week-header, .days {
    display: grid;
    grid-template-columns: repeat(7, 1fr);
    text-align: center;
  }
  .day-item {
    padding: 20rpx 0;
    width: 70rpx;
    height: 35rpx;
  }
  .day-item.selected {
    background-color: #1890ff;
    color: white;
    border-radius: 50%;
    width: 70rpx;
    height: 35rpx;
    display: flex;
    align-items: center;
    justify-content: center;
  }
  .time-ctrl {
    margin-top: 20rpx;
  }
  .popup-footer {
    display: flex;
    justify-content: space-between;
    align-items: center;
    margin-top: 30rpx;
    width: 100%;
  }
  .btn {
    color: #1890ff;
  }
  
  @keyframes slideUpFadeIn {
    from {
      transform: translateY(100%);
      opacity: 0;
    }
    to {
      transform: translateY(0);
      opacity: 1;
    }
  }
  
  .animate-up {
    animation: slideUpFadeIn 0.3s ease-out;
  }
  .day-item {
    padding: 20rpx 0;
    text-align: center;
    line-height: 1;
    border-radius: 50%;
    transition: all 0.25s ease;
  }
  
  .day-item.selected {
    background-color: #d4f5d1; /* 淡绿色 */
    color: black;
    font-weight: bold;
    border: 2rpx solid black;
  }
  @keyframes scaleSelected {
    0% {
      transform: scale(1);
    }
    50% {
      transform: scale(1.2);
    }
    100% {
      transform: scale(1);
    }
  }
  
  .day-item {
    padding: 20rpx 0;
    text-align: center;
    line-height: 1;
    border-radius: 50%;
    transition: all 0.25s ease;
  }
  
  .day-item.selected {
    background-color: #d4f5d1; /* 淡绿色 */
    color: black;
    font-weight: bold;
    border: 2rpx solid black;
    animation: scaleSelected 0.25s ease;
  }
  
  