<view class="register-container">
  <!-- 返回按钮 -->
  <view class="back-button" bindtap="goBack">
    <image src="/static/icon/back.png" mode="aspectFit"></image>
  </view>
  
  <!-- 注册表单 -->
  <view class="register-form">
    <view class="form-title">注册你的账号吧</view>
    
    <!-- 昵称输入框 -->
    <view class="input-group">
      <input class="form-input" type="text" placeholder="昵称" bindinput="onNicknameInput" value="{{nickname}}"/>
    </view>
    
    <!-- 手机号输入框 -->
    <view class="input-group">
      <input class="form-input" type="number" placeholder="手机号" bindinput="onPhoneInput" value="{{phone}}"/>
    </view>
    
    <!-- 密码输入框 -->
    <view class="input-group">
      <input class="form-input" type="{{showPassword ? 'text' : 'password'}}" placeholder="密码" bindinput="onPasswordInput" value="{{password}}"/>
      <view class="password-toggle" bindtap="togglePasswordVisibility">
        <image src="{{showPassword ? '/static/icon/eye_open.png' : '/static/icon/eye_close.png'}}" mode="aspectFit"></image>
      </view>
    </view>
    
    <!-- 重复密码输入框 -->
    <view class="input-group">
      <input class="form-input" type="{{showConfirmPassword ? 'text' : 'password'}}" placeholder="重复密码" bindinput="onConfirmPasswordInput" value="{{confirmPassword}}"/>
      <view class="password-toggle" bindtap="toggleConfirmPasswordVisibility">
        <image src="{{showConfirmPassword ? '/static/icon/eye_open.png' : '/static/icon/eye_close.png'}}" mode="aspectFit"></image>
      </view>
    </view>
    
    <!-- 下一步按钮 -->
    <button class="next-btn" bindtap="register">下一步</button>
  </view>
  
  <!-- 隐私政策 -->
  <view class="privacy-policy">
    <checkbox-group bindchange="checkboxChange">
      <label class="checkbox">
        <checkbox value="agree" checked="{{isAgree}}" color="#8dc63f" />
        <text>我已阅读并同意</text>
        <text class="policy-link" bindtap="showPrivacyPolicy">《隐私政策》</text>
      </label>
    </checkbox-group>
  </view>
</view>