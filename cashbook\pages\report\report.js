//js
const wxCharts = require("../../utils/wxcharts-min")
 
let areaChart;
// 获取应用实例
const app = getApp()

Page({
  data: {
    // 图表类型
    chartType: 'ring',
    // 数据类型
    activeTab: 'expense',
    // 饼图配置
    pieOpts: {
      type: "ring",
      color: ["#FF5722", "#FF9800", "#FFEB3B"],
      padding: [5, 5, 5, 5],
      legend: {
        show: false
      },
      title: {
        name: '支出大类',
        fontSize: 15,
        color: '#333333'
      },
      subtitle: {
        name: '轻点刷新',
        fontSize: 12,
        color: '#666666'
      },
      extra: {
        ring: {
          ringWidth: 40,
          activeOpacity: 0.9,
          activeRadius: 10,
          border: false,
          borderColor: "#FFFFFF",
          linearType: 'custom',
          customColor: [
            '#FF5722',
            '#FF7043',
            '#FF9800',
            '#FFA726'
          ]
        },
        pie: {
          offsetAngle: 0,
          activeOpacity: 1,
          activeRadius: 10,
          offsetRadius: 0,
          labelWidth: 15,
          border: false,
          circular: false
        },
        tooltip: {
          showBox: false
        },
        labelLine: {
          show: true,
          length: 15,
          lineWidth: 1,
          color: "#666666"
        }
      },
      disablePieStroke: false,
      dataLabel: true,
      dataPointShape: true,
      dataPointShapeType: 'solid',
      animation: true,
      duration: 1000,
      rotateLock: false,
      fontSize: 12,
      textColor: "#333333",
      rotate: false
    },
    // 饼图数据
    pieChartData: {
      categories: ["餐饮", "柴米油盐", "水果"],
      series: [{
        name: "占比",
        format: function(val) {
          return val.toFixed(2) + '%';
        },
        data: [
          {
            name: "餐饮",
            value: 32.07,
            labelText: '餐饮\n32.07%',
            labelAlign: 'left'
          },
          {
            name: "柴米油盐",
            value: 54.88,
            labelText: '柴米油盐\n54.88%',
            labelAlign: 'right'
          },
          {
            name: "水果",
            value: 13.05,
            labelText: '水果',
            labelAlign: 'center'
          }
        ]
      }]
    },
    
    // 年月选择弹窗相关数据
    showDatePicker: false,
    datePickerTab: 'month',
    selectedYear: 2025,
    selectedMonth: 3,
    tempYear: 2025,
    tempMonth: 3,
    // 面积图配置
    treemapOpts: {
      type: "column",
      color: ["#4FC3F7"],
      padding: [15, 15, 15, 15],
      legend: {
        show: false
      },
      xAxis: {
        disableGrid: true,
        fontColor: "#666666"
      },
      yAxis: {
        gridType: "dash",
        dashLength: 2,
        data: [],
        fontColor: "#666666"
      },
      extra: {
        column: {
          type: "group",
          width: 30,
          activeBgColor: "#000000",
          activeBgOpacity: 0.08,
          linearType: "custom",
          customColor: [
            '#4FC3F7',
            '#29B6F6'
          ],
          barBorderRadius: [5, 5, 0, 0]
        }
      },
      dataLabel: true,
      animation: true
    },
    // 面积图数据
    treemapData: {
      categories: ["能本油盐", "餐饮", "零食", "手续费", "调料"],
      series: [{
        name: "占比",
        data: [
          {
            name: "能本油盐",
            value: 34.78
          },
          {
            name: "餐饮",
            value: 26.09
          },
          {
            name: "零食",
            value: 21.57
          },
          {
            name: "手续费",
            value: 13.48
          },
          {
            name: "调料",
            value: 4.0
          }
        ]
      }]
    }
  },
  
  onLoad: function() {
    // 页面加载时的逻辑
    const date = new Date();
    this.setData({
      selectedYear: date.getFullYear(),
      selectedMonth: date.getMonth() + 1,
      tempYear: date.getFullYear(),
      tempMonth: date.getMonth() + 1
    });
    
    // 初始化图表数据
    this.updateChartData('expense');
    this.updateTreemapData('expense');
  },
  
  onReady: function() {
    // 页面初次渲染完成时的逻辑
    // 初始化图表
    this.updateChart();
  },
  
  // 打开日期选择器
  openDatePicker: function() {
    this.setData({
      showDatePicker: true,
      tempYear: this.data.selectedYear,
      tempMonth: this.data.selectedMonth
    });
  },
  
  // 关闭日期选择器
  closeDatePicker: function() {
    this.setData({
      showDatePicker: false
    });
  },
  
  // 切换日期选择器的标签页
  switchDatePickerTab: function(e) {
    const tab = e.currentTarget.dataset.tab;
    this.setData({
      datePickerTab: tab
    });
  },
  
  // 改变年份
  changeYear: function(e) {
    const direction = e.currentTarget.dataset.direction;
    let year = this.data.tempYear;
    
    if (direction === 'up') {
      year++;
    } else {
      year--;
    }
    
    this.setData({
      tempYear: year
    });
  },
  
  // 选择月份
  selectMonth: function(e) {
    
    const month = parseInt(e.currentTarget.dataset.month);
    
    this.setData({
      tempMonth: month
    });
    this.setData({
      selectedMonth: month
    })
  },
  
  // 确认日期选择
  confirmDatePicker: function() {
    this.setData({
      selectedYear: this.data.tempYear,
      selectedMonth: this.data.tempMonth,
      showDatePicker: false
    });
    
    // 这里可以添加获取所选日期的数据的逻辑
    this.getReportData(this.data.selectedYear, this.data.selectedMonth);
  },
  
  // 获取报表数据
  getReportData: function(year, month) {
    // 这里添加获取报表数据的逻辑
    console.log(`获取${year}年${month}月的报表数据`);
    
    // 模拟数据更新
    // 实际应用中，这里应该是从服务器获取数据
  },
  
// 设置图表类型
setChartType: function(e) {
  const type = e.currentTarget.dataset.type;
  this.setData({
    chartType: type
  });
  
  // 根据类型重新渲染图表
  this.updateChart();
},

// 更新图表
updateChart: function() {
  const type = this.data.chartType;
  if (type === 'ring') {
    // 更新环形图数据
    this.updateChartData(this.data.activeTab);
  } else if (type === 'area') {
    // 更新面积图数据
    this.updateTreemapData(this.data.activeTab);
  }
},

// 更新环形图数据
updateChartData: function(tab) {
  // 模拟不同标签的数据
  let chartData = {};
  
  if (tab === 'expense') {
    chartData = {
      categories: ["餐饮", "柴米油盐", "水果"],
      series: [{
        name: "占比",
        format: function(val) {
          return val.toFixed(2) + '%';
        },
        data: [
          {
            name: "餐饮",
            value: 32.07,
            labelText: '餐饮',
            labelAlign: 'left'
          },
          {
            name: "柴米油盐",
            value: 54.88,
            labelText: '柴米油盐',
            labelAlign: 'right'
          },
          {
            name: "水果",
            value: 13.05,
            labelText: '水果',
            labelAlign: 'center'
          }
        ]
      }]
    };
  } else if (tab === 'income') {
    chartData = {
      categories: ["工资", "兼职"],
      series: [{
        name: "占比",
        format: function(val) {
          return val.toFixed(2) + '%';
        },
        data: [
          {
            name: "工资",
            value: 80,
            labelText: '工资',
            labelAlign: 'left'
          },
          {
            name: "兼职",
            value: 20,
            labelText: '兼职',
            labelAlign: 'right'
          }
        ]
      }]
    };
  } else {
    chartData = {
      categories: ["借入", "借出"],
      series: [{
        name: "占比",
        format: function(val) {
          return val.toFixed(2) + '%';
        },
        data: [
          {
            name: "借入",
            value: 45,
            labelText: '借入',
            labelAlign: 'left'
          },
          {
            name: "借出",
            value: 55,
            labelText: '借出',
            labelAlign: 'right'
          }
        ]
      }]
    };
  }
  
  this.setData({
    pieChartData: chartData
  });
},

// 更新面积图数据
updateTreemapData: function(tab) {
  // 模拟山峰图数据
  let chartData = {};
  
  if (tab === 'expense') {
    chartData = {
      categories: ["能本油盐", "餐饮", "零食", "手续费", "调料"],
      series: [{
        name: "占比",
        data: [
          {
            name: "能本油盐",
            value: 34.78
          },
          {
            name: "餐饮",
            value: 26.09
          },
          {
            name: "零食",
            value: 21.57
          },
          {
            name: "手续费",
            value: 13.48
          },
          {
            name: "调料",
            value: 4.0
          }
        ]
      }]
    };
  } else if (tab === 'income') {
    chartData = {
      categories: ["工资", "兼职"],
      series: [{
        name: "占比",
        data: [
          {
            name: "工资",
            value: 85
          },
          {
            name: "兼职",
            value: 15
          }
        ]
      }]
    };
  } else {
    chartData = {
      categories: ["借入", "借出"],
      series: [{
        name: "占比",
        data: [
          {
            name: "借入",
            value: 60
          },
          {
            name: "借出",
            value: 40
          }
        ]
      }]
    };
  }
  
  this.setData({
    treemapData: chartData
  });
},

// 切换支出/收入/其他标签
switchTab: function(e) {
  const tab = e.currentTarget.dataset.tab;
  this.setData({
    activeTab: tab
  });
  
  // 根据选中的标签更新图表数据
  this.updateChartData(tab);
},

// 刷新图表
refreshChart: function() {
  // 根据当前图表类型刷新数据
  if (this.data.chartType === 'ring') {
    this.updateChartData(this.data.activeTab);
  } else if (this.data.chartType === 'area') {
    this.updateTreemapData(this.data.activeTab);
  }
  
  wx.showToast({
    title: '图表已刷新',
    icon: 'success',
    duration: 1000
  });
},

// 切换分类尺寸（大类/小类）
toggleCategorySize: function() {
  const isShowingSmallCategories = this.data.isShowingSmallCategories || false;
  
  this.setData({
    isShowingSmallCategories: !isShowingSmallCategories
  });
  
  wx.showToast({
    title: isShowingSmallCategories ? '已切换为大类' : '已切换为小类',
    icon: 'none',
    duration: 1000
  });
  
  // 这里可以根据大类/小类的选择更新图表数据
  this.updateChartData(this.data.activeTab);
}
})

 
 