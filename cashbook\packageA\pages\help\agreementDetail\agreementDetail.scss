/* packageA/pages/help/agreementDetail/agreementDetail.wxss */
.agreement-detail-container {
  min-height: 100vh;
  background-color: #ffffff;
  padding: 30rpx;
  position: relative;
  
  // 加载状态
  .loading-container {
    display: flex;
    flex-direction: column;
    align-items: center;
    justify-content: center;
    padding: 100rpx 0;
    
    .loading-spinner {
      width: 80rpx;
      height: 80rpx;
      border: 6rpx solid #f3f3f3;
      border-top: 6rpx solid #97bf75;
      border-radius: 50%;
      animation: spin 1s linear infinite;
      margin-bottom: 20rpx;
    }
    
    @keyframes spin {
      0% { transform: rotate(0deg); }
      100% { transform: rotate(360deg); }
    }
    
    .loading-text {
      font-size: 28rpx;
      color: #999999;
    }
  }
  
  // 错误状态
  .error-container {
    display: flex;
    flex-direction: column;
    align-items: center;
    justify-content: center;
    padding: 100rpx 0;
    
    .error-icon {
      width: 120rpx;
      height: 120rpx;
      margin-bottom: 20rpx;
    }
    
    .error-text {
      font-size: 28rpx;
      color: #999999;
      margin-bottom: 30rpx;
    }
    
    .retry-btn {
      background-color: #97bf75;
      color: #ffffff;
      font-size: 28rpx;
      padding: 15rpx 40rpx;
      border-radius: 40rpx;
      border: none;
      box-shadow: 0 4rpx 10rpx rgba(0, 0, 0, 0.1);
    }
  }
  
  // 协议内容区域
  .agreement-content {
    padding: 20rpx 0 100rpx;
    
    .agreement-header {
      margin-bottom: 40rpx;
      text-align: center;
      
      .agreement-title {
        font-size: 40rpx;
        font-weight: bold;
        color: #333333;
        display: block;
        margin-bottom: 20rpx;
      }
      
      .divider {
        width: 80rpx;
        height: 6rpx;
        background-color: #97bf75;
        margin: 0 auto;
        border-radius: 3rpx;
      }
    }
    
    // 富文本内容样式
    .rich-content {
      font-size: 30rpx;
      line-height: 1.8;
      color: #333333;
      
      // WxParse 样式覆盖
      .wxParse-p {
        margin-bottom: 20rpx;
      }
      
      .wxParse-h1, .wxParse-h2, .wxParse-h3 {
        font-weight: bold;
        margin: 30rpx 0 20rpx;
      }
      
      .wxParse-h1 {
        font-size: 36rpx;
      }
      
      .wxParse-h2 {
        font-size: 34rpx;
      }
      
      .wxParse-h3 {
        font-size: 32rpx;
      }
      
      .wxParse-a {
        color: #4a90e2;
        text-decoration: underline;
      }
      
      .wxParse-ul, .wxParse-ol {
        padding-left: 20rpx;
        margin-bottom: 20rpx;
      }
      
      .wxParse-li {
        margin-bottom: 10rpx;
      }
    }
    
    // 文本内容样式
    .text-content {
      font-size: 30rpx;
      line-height: 1.8;
      color: #333333;
      
      rich-text {
        display: block;
      }
    }
  }
  
  // 底部公司信息
  .footer {
    position: fixed;
    bottom: 0;
    left: 0;
    right: 0;
    text-align: center;
    padding: 30rpx 0;
    background-color: #ffffff;
    border-top: 1rpx solid #f0f0f0;
    
    .company-name {
      font-size: 24rpx;
      color: #999999;
    }
  }
}

// 适配小屏幕
@media (max-width: 320px) {
  .agreement-detail-container {
    padding: 20rpx;
    
    .agreement-content {
      .agreement-header {
        .agreement-title {
          font-size: 36rpx;
        }
      }
      
      .rich-content, .text-content {
        font-size: 28rpx;
      }
    }
  }
}