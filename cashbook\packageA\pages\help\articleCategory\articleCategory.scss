/* packageA/pages/help/articleCategory/articleCategory.scss */
.article-category-container {
  min-height: 100vh;
  background-color: #f8f9fa;
  padding-bottom: 30rpx;
  
  /* 顶部导航栏 */
  .nav-bar {
    position: fixed;
    top: 0;
    left: 0;
    right: 0;
    height: 88rpx;
    background-color: #fff;
    display: flex;
    align-items: center;
    padding: 0 30rpx;
    z-index: 100;
    box-shadow: 0 2rpx 10rpx rgba(0, 0, 0, 0.05);
    
    .back-btn {
      width: 40rpx;
      height: 40rpx;
      padding: 10rpx;
      margin-right: 10rpx;
      
      image {
        width: 100%;
        height: 100%;
      }
    }
    
    .category-title {
      flex: 1;
      text-align: center;
      font-size: 34rpx;
      font-weight: 500;
      color: #333;
      padding-right: 60rpx; /* 平衡左边的返回按钮 */
    }
  }
  
  /* 文章列表样式 */
  .article-list {
    
    /* 分类说明 */
    .category-header {
      background-color: #fff;
      border-radius: 12rpx;
      padding: 24rpx 20rpx;
      margin-bottom: 20rpx;
      box-shadow: 0 2rpx 8rpx rgba(0, 0, 0, 0.03);
      
      .category-description {
        font-size: 28rpx;
        color: #666;
        line-height: 1.5;
      }
    }
    
    .article-item {
      display: flex;
      background-color: #fff;
      border-radius: 12rpx;
      padding: 24rpx 20rpx;
      margin-bottom: 20rpx;
      box-shadow: 0 2rpx 8rpx rgba(0, 0, 0, 0.03);
      transition: all 0.2s ease;
      
      &:active {
        transform: scale(0.98);
        opacity: 0.9;
      }
      
      .article-left {
        flex: 1;
        padding-right: 20rpx;
        
        .article-title {
          font-size: 32rpx;
          font-weight: 500;
          color: #333;
          line-height: 1.4;
          margin-bottom: 12rpx;
          display: -webkit-box;
          -webkit-line-clamp: 2;
          -webkit-box-orient: vertical;
          overflow: hidden;
        }
        
        .article-desc {
          font-size: 26rpx;
          color: #666;
          line-height: 1.4;
          margin-bottom: 16rpx;
          display: -webkit-box;
          -webkit-line-clamp: 2;
          -webkit-box-orient: vertical;
          overflow: hidden;
        }
        
        .article-meta {
          display: flex;
          justify-content: space-between;
          font-size: 24rpx;
          color: #999;
          
          .article-date {
            
          }
          
          .article-views {
            
          }
        }
      }
      
      .article-right {
        width: 160rpx;
        height: 120rpx;
        border-radius: 8rpx;
        overflow: hidden;
        
        image {
          width: 100%;
          height: 100%;
        }
        
        &.no-image {
          background-color: #f5f7f9;
          display: flex;
          align-items: center;
          justify-content: center;
          
          .article-icon {
            width: 60rpx;
            height: 60rpx;
            opacity: 0.5;
            
            image {
              width: 100%;
              height: 100%;
            }
          }
        }
      }
    }
  }
  
  /* 加载更多样式 */
  .load-more, .no-more {
    text-align: center;
    padding: 30rpx 0;
    color: #999;
    font-size: 26rpx;
    
    .loading-icon {
      display: inline-block;
      width: 30rpx;
      height: 30rpx;
      border: 4rpx solid #f3f3f3;
      border-top: 4rpx solid #3498db;
      border-radius: 50%;
      animation: spin 1s linear infinite;
      margin-right: 10rpx;
      vertical-align: middle;
    }
    
    @keyframes spin {
      0% { transform: rotate(0deg); }
      100% { transform: rotate(360deg); }
    }
  }
  
  /* 加载中样式 */
  .loading-container {
    display: flex;
    flex-direction: column;
    justify-content: center;
    align-items: center;
    height: 500rpx;
    
    .loading-icon {
      width: 60rpx;
      height: 60rpx;
      border: 4rpx solid #f3f3f3;
      border-top: 4rpx solid #3498db;
      border-radius: 50%;
      animation: spin 1s linear infinite;
      margin-bottom: 20rpx;
    }
    
    text {
      font-size: 26rpx;
      color: #999;
    }
  }
  
  /* 空状态样式 */
  .empty-container {
    display: flex;
    flex-direction: column;
    justify-content: center;
    align-items: center;
    height: 500rpx;
    padding-top: 100rpx;
    
    image {
      width: 180rpx;
      height: 180rpx;
      margin-bottom: 20rpx;
    }
    
    text {
      font-size: 28rpx;
      color: #999;
    }
  }
} 