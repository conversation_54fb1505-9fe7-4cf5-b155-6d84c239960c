// packageA/pages/introduction/introduction.js
const app = getApp();
import { getIntroduction, getArticle_category, getArticlelist, getVideos, getKefu } from '../../../api/basic/index';
const util = require('../../../utils/index.js');

Page({

  /**
   * 页面的初始数据
   */
  data: {
    // 应用版本
    version: 'v1.0.0',
    // 加载状态
    loading: true,
    // 功能模块数据
    sections: [
      {
        title: "使用文档",
        items: [
          { name: "文章分类", desc: "查看所有文章分类", icon: "/static/icon/cate1.png", type: "articleCategory" },
          { name: "文章列表", desc: "浏览所有帮助文章", icon: "/static/icon/cate2.png", type: "articleList" },
          { name: "文章详情", desc: "查看文章详细内容", icon: "/static/icon/cate3.png", type: "articleDetail" }
        ]
      },
      {
        title: "帮助中心",
        items: [
          { name: "视频列表", desc: "观看教学视频，快速上手", icon: "/static/icon/cate4.png", type: "videoList" },
          { name: "客服信息", desc: "联系我们获取帮助", icon: "/static/icon/jr.png", type: "customerService" }
        ]
      },
      {
        title: "协议与条款",
        items: [
          { name: "协议列表", desc: "所有用户协议与条款", icon: "/static/icon/hz.png", type: "agreementList" },
        ]
      },
      {
        title: "其他功能",
        items: [
          { name: "收支分类", desc: "收支分类", icon: "/static/icon/sr.png", type: "smsService" },
          // { name: "发送短信", desc: "接收发送短信", icon: "/static/icon/sr.png", type: "smsService" },
          // { name: "校验验证码", desc: "验证账户安全", icon: "/static/icon/card.png", type: "verifyCode" },
          // { name: "上传图片预算类型", desc: "管理您的预算图片", icon: "/static/icon/avator.png", type: "imageUpload" },
          // { name: "各类型图片", desc: "浏览所有图片类型", icon: "/static/icon/pic.png", type: "imageTypes" },
          // { name: "主题列表", desc: "个性化您的记账本", icon: "/static/icon/bb.png", type: "themeList" },
          // { name: "账户分类", desc: "查看所有账户类型", icon: "/static/icon/bb2.png", type: "accountTypes" },
          // { name: "银行列表", desc: "所有支持的银行", icon: "/static/icon/card2.png", type: "bankList" }
        ]
      }
    ],
    // API获取的数据
    apiData: {
      introduction: {},
      articleCategories: [],
      articles: [],
      videos: [],
      kefuInfo: {}
    }
  },

  /**
   * 生命周期函数--监听页面加载
   */
  onLoad(options) {
    console.log(util, 'util');

    // 获取小程序版本信息
    if (app && app.globalData && app.globalData.version) {
      this.setData({
        version: app.globalData.version
      });
    }

    // 获取帮助页面配置信息
    this.fetchHelpPageData();

    // 显示加载提示
    wx.showLoading({
      title: '加载中...',
    });

    // 获取介绍信息
    this.fetchIntroductionData();
  },

  /**
   * 获取介绍页面数据（从API）
   */
  async fetchIntroductionData() {
    try {
      // 获取帮助引言数据
      const introRes = await getIntroduction({});

      // 获取文章分类数据
      const categoryRes = await getArticle_category({});

      // 获取第一个分类的ID（如果有的话）
      let firstCategoryId = null;
      // if (categoryRes.data && categoryRes.data.length > 0) {
      //   firstCategoryId = categoryRes.data[0].id;
      // }

      // 获取文章列表数据（使用第一个分类的ID）
      let articleList = [];
      if (firstCategoryId) {
        const articleRes = await getArticlelist({
          category_id: firstCategoryId
        });
        articleList = articleRes.data || [];
        console.log('文章列表:', articleRes.data);
      }

      // 获取视频列表数据
      const videoRes = await getVideos({});

      // 获取客服信息数据
      const kefuRes = await getKefu({});
      console.log(introRes.data, 'introRes.data');

      // 处理接口返回的introduction字段
      const introductionData = {
        content: introRes.data && introRes.data.introduction ? introRes.data.introduction : '',
        title: '帮助中心',
        subtitle: '小猫记账使用指南'
      };

      // 更新数据
      this.setData({
        'apiData.introduction': introductionData,
        'apiData.articleCategories': categoryRes.data || [],
        'apiData.articles': articleList,
        'apiData.videos': videoRes.data || [],
        'apiData.kefuInfo': kefuRes.data || {},
        loading: false
      });

      // 根据API返回的数据更新页面内容
      this.updateSectionsFromApi();

    } catch (error) {
      console.error('获取数据失败:', error);
      wx.showToast({
        title: '获取数据失败',
        icon: 'none'
      });
    } finally {
      wx.hideLoading();
    }
  },

  /**
   * 根据API数据更新sections
   */
  updateSectionsFromApi() {
    const { introduction, articleCategories, articles, videos, kefuInfo } = this.data.apiData;

    console.log('Introduction content:', introduction.content);
    console.log('Article categories:', articleCategories && articleCategories.length);

    // 检查introduction内容是否为空
    if (!introduction.content) {
      console.warn('Introduction content is empty');
    }

    // 如果有介绍数据，更新页面标题
    if (introduction && introduction.title) {
      wx.setNavigationBarTitle({
        title: introduction.title
      });
    }

    // 准备更新的sections数组
    const updatedSections = [...this.data.sections];

    // 优化文章分类显示 - 根据分类数量调整布局
    if (articleCategories && articleCategories.length > 0) {
      // 清空现有的使用文档部分（第一个section）
      if (updatedSections[0]) {
        // 如果分类数量小于等于6个，仍使用列表展示
        if (articleCategories.length <= 6) {
          const docItems = articleCategories.map(category => ({
            name: category.name || '文章分类',
            desc: category.description || '查看相关文章',
            icon: category.icon || "/static/icon/cate1.png",
            type: "articleCategory",
            id: category.id
          }));

          updatedSections[0].items = docItems;
        } else {
          // 如果分类数量大于6个，分为多个section来展示

          // 第一组：保留前4个最主要的分类
          const mainCategories = articleCategories.slice(0, 4);
          updatedSections[0].items = mainCategories.map(category => ({
            name: category.name || '文章分类',
            desc: category.description || '查看相关文章',
            icon: category.icon || "/static/icon/cate1.png",
            type: "articleCategory",
            id: category.id
          }));

          // 创建更多文章分类section
          const moreCategories = {
            title: "更多文章分类",
            items: articleCategories.slice(4).map(category => ({
              name: category.name || '文章分类',
              desc: category.description || '查看相关文章',
              icon: category.icon || "/static/icon/cate1.png",
              type: "articleCategory",
              id: category.id
            }))
          };

          // 在第二个位置插入更多分类section
          updatedSections.splice(1, 0, moreCategories);
        }
      }

      // 若分类太多，可以添加"查看全部分类"按钮
      if (articleCategories.length > 10 && updatedSections[0]) {
        updatedSections[0].items.push({
          name: "查看全部分类",
          desc: `共${articleCategories.length}个分类`,
          icon: "/static/icon/more.png",
          type: "allCategories",
          id: ""
        });
      }
    }

    // 使用几个精选分类的文章组成"推荐文章"部分
    // if (articleCategories && articleCategories.length > 0) {
    //   // 找到或创建推荐文章section
    //   let recommendedSection = updatedSections.find(section => section.title === "推荐文章");

    //   if (!recommendedSection) {
    //     // 创建一个新的推荐文章section
    //     recommendedSection = {
    //       title: "推荐文章",
    //       items: []
    //     };

    //     // 将新section添加到sections数组
    //     const insertIndex = articleCategories.length > 6 ? 2 : 1;
    //     updatedSections.splice(insertIndex, 0, recommendedSection);
    //   }

    //   // 收集推荐文章
    //   let recommendedArticles = [];

    //   // 从每个分类中选择1-2篇文章（如果有的话）
    //   const maxCategories = Math.min(3, articleCategories.length);
    //   for (let i = 0; i < maxCategories; i++) {
    //     const categoryId = articleCategories[i].id;

    //     // 这里假设我们已经有了文章数据，实际使用时可能需要额外请求
    //     if (articles && articles.length > 0) {
    //       // 查找属于当前分类的文章
    //       const categoryArticles = articles.filter(article => 
    //         article.category_id === categoryId
    //       ).slice(0, 2); // 每个分类最多取2篇

    //       recommendedArticles = [...recommendedArticles, ...categoryArticles];
    //     }
    //   }

    //   // 如果有文章，更新推荐文章section
    //   if (recommendedArticles.length > 0) {
    //     recommendedSection.items = recommendedArticles.map(article => ({
    //       name: article.title || '文章标题',
    //       desc: article.description || '查看文章详情',
    //       icon: article.image || "/static/icon/article.png",
    //       type: "articleDetail",
    //       id: article.id
    //     }));
    //   } else {
    //     // 如果没有文章，使用默认项
    //     recommendedSection.items = [
    //       {
    //         name: "暂无推荐文章",
    //         desc: "请稍后查看",
    //         icon: "/static/icon/article.png",
    //         type: "articleList",
    //         id: ""
    //       }
    //     ];
    //   }
    // }

    // 更新视频列表部分 - 保持原有逻辑
    if (videos && videos.length > 0) {
      // 查找帮助中心section
      const helpSectionIndex = updatedSections.findIndex(section => section.title === "帮助中心");

      if (helpSectionIndex !== -1) {
        // 从视频列表中获取视频项
        const videoItems = videos.map(video => ({
          name: video.title || '视频教程',
          desc: video.description || '观看视频教程',
          icon: video.image || "/static/icon/cate4.png",
          type: "videoDetail",
          id: video.id
        }));

        // 更新视频列表项
        if (videoItems.length > 0 && updatedSections[helpSectionIndex].items.length > 0) {
          // 保留原始的其他项（如客服信息等）
          updatedSections[helpSectionIndex].items = [
            videoItems[0],  // 只添加第一个视频
            ...updatedSections[helpSectionIndex].items.slice(1)  // 保留其他项
          ];
        }
      }
    }

    // 更新UI
    this.setData({
      sections: updatedSections
    });
  },

  /**
   * 获取帮助页面数据
   */
  fetchHelpPageData() {
    // 如果有API接口，可以通过网络请求获取最新的帮助页面数据
    // wx.request({
    //   url: 'https://api.example.com/help/config',
    //   success: (res) => {
    //     if (res.data && res.data.sections) {
    //       this.setData({
    //         sections: res.data.sections
    //       });
    //     }
    //   }
    // });
  },

  /**
   * 生命周期函数--监听页面初次渲染完成
   */
  onReady() {

  },

  /**
   * 生命周期函数--监听页面显示
   */
  onShow() {

  },

  /**
   * 生命周期函数--监听页面隐藏
   */
  onHide() {

  },

  /**
   * 生命周期函数--监听页面卸载
   */
  onUnload() {

  },

  /**
   * 页面相关事件处理函数--监听用户下拉动作
   */
  onPullDownRefresh() {
    // 下拉刷新时重新获取数据
    this.fetchIntroductionData().then(() => {
      wx.stopPullDownRefresh();
    });
  },

  /**
   * 页面上拉触底事件的处理函数
   */
  onReachBottom() {

  },

  /**
   * 导航到对应功能页面
   */
  navigateToFunction(e) {
    const functionType = e.currentTarget.dataset.type;
    const itemId = e.currentTarget.dataset.id;
    console.log('Navigate to function:', functionType, 'ID:', itemId);

    // 根据功能类型导航到不同页面
    switch (functionType) {
      case 'articleCategory':
        // 导航到文章分类页，并传递分类ID
        wx.navigateTo({
          url: `/packageA/pages/help/articleCategory/articleCategory?id=${itemId || ''}`
        });
        break;
      case 'articleList':
        // 导航到文章列表页，并传递分类ID
        wx.navigateTo({
          url: `/packageA/pages/help/articleList/articleList?category_id=${itemId || ''}`
        });
        break;
      case 'articleDetail':
        // 导航到文章详情页，并传递文章ID
        wx.navigateTo({
          url: `/packageA/pages/help/articleDetail/articleDetail?id=${itemId || ''}`
        });
        break;
      case 'videoList':
        // 导航到视频列表页
        wx.navigateTo({
          url: `/packageA/pages/help/videoList/videoList`
        });
        break;
      case 'videoDetail':
        // 导航到视频详情页，并传递视频ID
        wx.navigateTo({
          url: `/packageA/pages/help/videoDetail/videoDetail?id=${itemId || ''}`
        });
        break;
      case 'allCategories':
        // 导航到全部分类页面
        wx.navigateTo({
          url: `/packageA/pages/help/allCategories/allCategories`
        });
        break;
      case 'customerService':
        wx.navigateTo({
          url: `/packageA/pages/help/customerService/customerService`
        });
        break;
      case 'agreementList':
        wx.navigateTo({
          url: `/packageA/pages/help/agreementList/agreementList`
        });
        break;
      // 收支分类
      case 'smsService':
        wx.navigateTo({
          url: `/packageA/pages/help/smsService/smsService`
        });
        break;
      default:
        // 默认导航回到帮助主页
        wx.navigateTo({ url: '/packageA/pages/help/help' });
    }
  },

  /**
   * 分享页面
   */
  onShareAppMessage() {
    const introTitle = this.data.apiData.introduction.title || '小猫记账帮助中心';

    return {
      title: introTitle,
      path: '/packageA/pages/introduction/introduction'
    }
  },

  /**
   * 拨打客服电话
   */
  makePhoneCall(e) {
    const phoneNumber = e.currentTarget.dataset.phone;
    if (phoneNumber) {
      wx.makePhoneCall({
        phoneNumber: phoneNumber,
        success: () => {
          console.log('拨打电话成功');
        },
        fail: (err) => {
          console.error('拨打电话失败:', err);
        }
      });
    }
  },

  /**
   * 复制微信号
   */
  copyWechat(e) {
    const wechat = e.currentTarget.dataset.wechat;
    if (wechat) {
      wx.setClipboardData({
        data: wechat,
        success: () => {
          wx.showToast({
            title: '微信号已复制',
            icon: 'success'
          });
        }
      });
    }
  },
})