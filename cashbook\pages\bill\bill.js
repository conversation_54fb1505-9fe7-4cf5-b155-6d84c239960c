// pages/bill/bill.js
Page({

  /**
   * 页面的初始数据
   */
  data: {
     time:[
       {id:1,name:'4月1日'},
       {id:2,name:'15日'},
       {id:3,name:'29日'}
     ],
     randomHeights: [],
     chartData: {},
     //您可以通过修改 config-ucharts.js 文件中下标为 ['column'] 的节点来配置全局默认参数，如都是默认参数，此处可以不传 opts 。实际应用过程中 opts 只需传入与全局默认参数中不一致的【某一个属性】即可实现同类型的图表显示不同的样式，达到页面简洁的需求。
     opts: {
         color: ["#1d9b6b","#fb4050"],
         padding: [15,15,0,5],
         enableScroll: false,
         legend: {},
         xAxis: {
           disableGrid: true
         },
         yAxis: {
           data: [
             {
               min: 0
             }
           ]
         },
         extra: {
           column: {
             type: "group",
             width: 3,
             activeBgColor: "#000000",
             activeBgOpacity: 0.08,
             barBorderCircle: true

           }
         }
       },
     list: [
      {
        isTransfer: true,
        time: '5月8日',
        istoday: '昨天',
        desc: '【资产初始化】初期余额*****************',
        amount: '100.00',
        remark: '人人认可阿斯达啊大大撒阿萨大大',
        payAccount: '100.00',
        date: '2023-05-08'
      },
      {
        isTransfer: true,
        time: '5月8日',
        istoday: '昨天',
        desc: '【资产初始化】初期余额*****************',
        amount: '100.00',
        remark: '人人认可阿斯达啊大大撒阿萨大大',
        payAccount: '100.00',
        date: '2023-05-08'
      },

  ],
    isSort:true,
    
   
    
  },

    getServerData() {
      //模拟从服务器获取数据时的延时
      setTimeout(() => {
        //模拟服务器返回数据，如果数据格式和标准格式不同，需自行按下面的格式拼接
        let res = {
            categories: ["2018","2019","2020","2021","2022","2023"],
            series: [
              {
                name: "月结余",
                data: [35,36,31,33,13,34]
              },
              {
                name: "日均支出",
                data: [18,27,21,24,6,28]
              }
            ]
          };
        this.setData({ chartData: JSON.parse(JSON.stringify(res)) });
      }, 500);
    },

  toShit(){
    console.log('dddd');
    wx.navigateTo({
      url: '/pages/bill/sift/sift',
    })
  },
  onLoad(options) {
    const heights = [];
    for (let i = 0; i < 30; i++) {
      // 生成 1 到 100 之间的随机整数
      const randomHeight = Math.floor(Math.random() * 100) + 1; 
      heights.push(randomHeight);
    }
    this.setData({
      randomHeights: heights
    });
  
  },
  change(){
    this.setData({
      isSort:!this.data.isSort
    })
  },
  // 获取子组件传递的参数
  getcateID(e){
    // console.log('子组件传递的数据',e.detail);
},
  onReady() {
    this.getServerData();
  },

  /**
   * 生命周期函数--监听页面显示
   */
  onShow() {

  },

  /**
   * 生命周期函数--监听页面隐藏
   */
  onHide() {

  },

  /**
   * 生命周期函数--监听页面卸载
   */
  onUnload() {

  },

  /**
   * 页面相关事件处理函数--监听用户下拉动作
   */
  onPullDownRefresh() {

  },

  /**
   * 页面上拉触底事件的处理函数
   */
  onReachBottom() {

  },
  
  /**
   * 用户点击右上角分享
   */
  onShareAppMessage() {

  }
})