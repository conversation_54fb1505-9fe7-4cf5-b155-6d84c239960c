// packageA/pages/help/videoDetail/videoDetail.js
import { getVideos } from '../../../../api/basic/index';
const util = require('../../../../utils/index');
Page({
  /**
   * 页面的初始数据
   */
  data: {
    videoId: null,
    video: '',
    title: '',
    relatedVideos: [],
    loading: true,
    videoContext: null,
    isPlaying: true,
    videoProgress: 0,
    playbackRate: '1.0',
    currentTime: 0,
    duration: 0
  },

  /**
   * 生命周期函数--监听页面加载
   */
  onLoad(options) {
    // 从链接参数中获取视频ID
    const { id, title } = options;
    
    if (id) {
      this.setData({ 
        videoId: id,
        title: title || '账单标签'
      });
      this.fetchVideoDetail(id);
    } else {
      this.setData({ loading: false });
      wx.showToast({
        title: '视频ID不存在',
        icon: 'none'
      });
    }

    // 设置导航栏标题
    wx.setNavigationBarTitle({
      title: title || '视频教程'
    });
  },

  /**
   * 获取视频详情
   */
  fetchVideoDetail(id) {
    this.setData({ loading: true });

    // 调用获取视频列表API
    getVideos()
      .then(res => {
        if (res && res.data && Array.isArray(res.data)) {
          // 找到对应ID的视频
          const videoItem = res.data.find(item => item.id == id);
          console.log(videoItem, 'videoItem=================');
          if (videoItem && videoItem.video) {
            // 使用util.getImageUrl处理视频路径
            const videoUrl = util.getImageUrl(videoItem.video);
            
            this.setData({
              // 直接设置处理后的视频URL
              video: videoUrl,
              title: videoItem.title || this.data.title,
              loading: false
            });
            
            // 设置相关视频（除了当前视频外的其他视频）
            if (res.data.length > 1) {
              this.setData({
                relatedVideos: res.data.filter(item => item.id != id).map(item => ({
                  ...item,
                  thumbnail: item.thumbnail ? util.getImageUrl(item.thumbnail) : ''
                }))
              });
            }
          } else {
            this.setData({ loading: false });
            wx.showToast({
              title: '未找到视频',
              icon: 'none'
            });
          }
        } else {
          this.setData({ loading: false });
          wx.showToast({
            title: '获取视频失败',
            icon: 'none'
          });
        }
      })
      .catch(err => {
        console.error('获取视频详情失败:', err);
        this.setData({ loading: false });
        wx.showToast({
          title: '获取视频失败',
          icon: 'none'
        });
      });
  },

  /**
   * 生命周期函数--监听页面初次渲染完成
   */
  onReady() {
    // 获取视频上下文
    this.videoContext = wx.createVideoContext('myVideo', this);
    
    // 设置定时器更新进度
    this.progressTimer = setInterval(() => {
      this.updateVideoProgress();
    }, 1000);
  },

  /**
   * 更新视频进度
   */
  updateVideoProgress() {
    if (!this.videoContext) return;
    
    // 直接使用视频绑定的事件来更新进度，而不是通过API调用
    const videoNode = wx.createSelectorQuery().in(this).select('#myVideo');
    if (!videoNode) return;
    
    try {
      videoNode.fields({
        properties: ['currentTime', 'duration'],
      }, (res) => {
        if (res && res.currentTime !== undefined && res.duration) {
          const progress = (res.currentTime / res.duration) * 100;
          this.setData({
            videoProgress: progress.toFixed(1),
            currentTime: res.currentTime.toFixed(1),
            duration: res.duration.toFixed(1)
          });
        }
      }).exec();
    } catch (e) {
      console.error('获取视频进度失败', e);
    }
  },

  /**
   * 视频播放事件
   */
  onTimeUpdate(e) {
    const { currentTime, duration } = e.detail;
    if (duration > 0) {
      const progress = (currentTime / duration) * 100;
      this.setData({
        videoProgress: progress.toFixed(1),
        currentTime: currentTime.toFixed(1),
        duration: duration.toFixed(1)
      });
    }
  },

  /**
   * 切换播放/暂停
   */
  togglePlayPause() {
    if (this.data.isPlaying) {
      this.videoContext.pause();
    } else {
      this.videoContext.play();
    }
    
    this.setData({
      isPlaying: !this.data.isPlaying
    });
  },
  
  /**
   * 切换播放速率
   */
  changePlaybackRate() {
    // 播放速率选项
    const rates = ['0.5', '0.8', '1.0', '1.25', '1.5', '2.0'];
    const currentIndex = rates.indexOf(this.data.playbackRate);
    const nextIndex = (currentIndex + 1) % rates.length;
    const newRate = rates[nextIndex];
    
    this.videoContext.playbackRate(parseFloat(newRate));
    this.setData({
      playbackRate: newRate
    });
  },

  /**
   * 视频播放结束事件
   */
  onVideoEnded() {
    console.log('视频播放结束');
    this.setData({
      isPlaying: false,
      videoProgress: 100
    });
  },

  /**
   * 视频播放错误事件
   */
  onVideoError(e) {
    console.error('视频播放错误:', e.detail.errMsg);
    wx.showToast({
      title: '视频播放出错',
      icon: 'none'
    });
  },

  /**
   * 查看相关视频
   */
  viewRelatedVideo(e) {
    const { id } = e.currentTarget.dataset;
    
    if (id) {
      // 跳转到相关视频
      wx.redirectTo({
        url: `/packageA/pages/help/videoDetail/videoDetail?id=${id}`
      });
    }
  },

  /**
   * 返回上一页
   */
  goBack() {
    wx.navigateBack();
  },

  /**
   * 生命周期函数--监听页面显示
   */
  onShow() {
    // 如果视频上下文存在，恢复播放
    if (this.videoContext) {
      // this.videoContext.play();
    }
  },

  /**
   * 生命周期函数--监听页面隐藏
   */
  onHide() {
    // 如果视频上下文存在，暂停播放
    if (this.videoContext) {
      this.videoContext.pause();
      this.setData({
        isPlaying: false
      });
    }
  },

  /**
   * 生命周期函数--监听页面卸载
   */
  onUnload() {
    // 如果视频上下文存在，停止播放
    if (this.videoContext) {
      this.videoContext.stop();
    }
    
    // 清除进度定时器
    if (this.progressTimer) {
      clearInterval(this.progressTimer);
    }
  },

  /**
   * 页面相关事件处理函数--监听用户下拉动作
   */
  onPullDownRefresh() {

  },

  /**
   * 页面上拉触底事件的处理函数
   */
  onReachBottom() {

  },

  /**
   * 用户点击右上角分享
   */
  onShareAppMessage() {
    const { videoId, title } = this.data;
    return {
      title: title || '视频教程',
      path: `/packageA/pages/help/videoDetail/videoDetail?id=${videoId}`
    };
  }
})