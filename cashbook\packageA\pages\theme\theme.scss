/* packageA/pages/theme/theme.wxss */

/* 主题页面样式 */
.theme-container {
  background-color: #f5f5f5;
  min-height: 100vh;
  padding-bottom: 60px;
  position: relative;
}

/* 顶部导航栏 */
.nav-header {
  display: flex;
  align-items: center;
  height: 44px;
  padding: 0 15px;
  background-color: #fff;
  position: relative;

  .back-icon {
    font-size: 20px;
    color: #333;
  }

  .title {
    position: absolute;
    left: 50%;
    transform: translateX(-50%);
    font-size: 17px;
    font-weight: 500;
    color: #333;
  }
}

/* 分区样式 */
.section {
  display: flex;
  align-items: center;
  padding: 15px 15px 5px;
}

.section-indicator {
  width: 3px;
  height: 16px;
  background-color: #8DC2B5;
  margin-right: 8px;
  border-radius: 3px;
}

.section-title {
  font-size: 14px;
  color: #333;
}

.section-desc {
  padding: 0 15px 15px 26px;
  font-size: 14px;
  color: #666;
}

/* 自定义颜色部分 */
.custom-color-section {
  margin-top: 10px;
}

/* 选择颜色按钮 */
.color-select-btn {
  margin: 10px 15px;
  height: 40px;
  background-color: #8DC2B5;
  border-radius: 20px;
  display: flex;
  align-items: center;
  justify-content: center;
  color: #fff;
  font-size: 14px;
  position: relative;
}

.check-icon {
  position: absolute;
  left: 15px;
  font-size: 16px;
  color: #fff;
}

/* 颜色图标 */
.color-icon {
  position: absolute;
  right: 15px;
  top: 240px;
  width: 100px;
  height: 80px;
}

.color-icon image {
  width: 100%;
  height: 100%;
}

/* 颜色网格 */
.color-grid {
  padding: 0 15px;
}

.color-row {
  display: flex;
  justify-content: flex-start;
  margin-bottom: 10px;
}

.color-item {
  width: 30%;
  height: 70px;
  border-radius: 10px;
  display: flex;
  align-items: flex-end;
  padding: 10px;
  position: relative;
  box-sizing: border-box;
  margin-right: 5%;
}

.color-item:nth-child(3n) {
  margin-right: 0;
}

.color-name {
  color: #fff;
  font-size: 14px;
  text-shadow: 0 1px 2px rgba(0, 0, 0, 0.3);
}

.color-item.selected {
  border: 2px solid #fff;
  box-shadow: 0 0 0 2px #8DC2B5;
}

/* 加载状态 */
.loading-wrapper {
  padding: 30px 0;
  display: flex;
  align-items: center;
  justify-content: center;
}

.loading-text {
  color: #999;
  font-size: 14px;
  text-align: center;
}

/* 空状态 */
.empty-state {
  padding: 50px 0;
  display: flex;
  align-items: center;
  justify-content: center;
}

.empty-text {
  color: #999;
  font-size: 14px;
  text-align: center;
}

/* 底部导航栏 */
.bottom-tabbar {
  position: fixed;
  bottom: 0;
  left: 0;
  right: 0;
  height: 50px;
  background-color: #fff;
  display: flex;
  align-items: center;
  justify-content: space-around;
  border-top: 1px solid #eee;
  z-index: 100;
}

.tab-item {
  flex: 1;
  display: flex;
  align-items: center;
  justify-content: center;
  height: 100%;
}

.tab-item .iconfont {
  font-size: 24px;
  color: #8DC2B5;
}