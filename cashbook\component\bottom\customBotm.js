// component/bottom/customBotm.js
Component({

  /**
   * 组件的属性列表
   */
  properties: {
    current: {
      type: Number
    }
  },

  /**
   * 组件的初始数据
   */
  data: {
    list: [{
        id: 1,
        name: '主页',
        icon:'/static/icon/home.png',
        aicon:'/static/icon/home2.png',
      },
      {
        id: 2,
        name: '账单',
        icon:'/static/icon/card.png',
        aicon:'/static/icon/card2.png',
      },
      {
        id: 3,
        name: '报表',
        icon:'/static/icon/bb.png',
        aicon:'/static/icon/bb2.png',
      },
      {
        id: 4,
        name: '我的',
        icon:'/static/icon/my.png',
        aicon:'/static/icon/my2.png',
      },
    ],
    showCard: false,
  },

  /**
   * 组件的方法列表
   */
  methods: {
    sendID(e) {
      const id = e.currentTarget.dataset.id;
      this.triggerEvent('getCateId', id);
      console.log('id', id);
      if (id == 1) {
        wx.redirectTo({
          url: '/pages/index2/index2',
        })
        return;
      }
      if (id == 2) {
        wx.redirectTo({
          url: '/pages/bill/bill',
        })
        return;
      }
      if (id == 3) {
        wx.redirectTo({
          url: '/pages/report/report',
        })
        return;
      }
      if (id == 4) {
        wx.redirectTo({
          url: '/pages/my/my',
        })
        return;
      }
    },
    showCard() {
      this.setData({
        showCard: !this.data.showCard
      })
      this.triggerEvent('showMask',!this.data.showCard);
    },
    close(){
      this.setData({
        showCard:false
      })
      this.triggerEvent('showMask',!this.data.showCard);
    }
  }
})