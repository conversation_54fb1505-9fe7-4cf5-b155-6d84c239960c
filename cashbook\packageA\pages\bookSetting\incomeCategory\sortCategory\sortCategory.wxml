<!--packageA/pages/bookSetting/incomeCategory/sortCategory/sortCategory.wxml-->
<view class="sort-container">
  <!-- 顶部导航栏 -->
  <!-- <view class="nav-bar" style="padding-top: {{statusBarHeight}}px; height: {{navBarHeight}}px;">
    <view class="back-icon" bindtap="goBack">
      <van-icon name="arrow-left" size="20px" color="#333" />
    </view>
    <view class="page-title">分类排序</view>
  </view> -->

  <!-- 提示文本 -->
  <view class="tip-text">长按拖动排序</view>

  <!-- 分类列表 -->
  <view class="category-list">
    <block wx:for="{{categories}}" wx:key="id">
      <view class="category-item {{currentIndex === index ? 'dragging' : ''}}"
        data-id="{{item.id}}"
        data-index="{{index}}"
        bindtouchstart="touchStart"
        bindtouchmove="touchMove"
        bindtouchend="touchEnd"
        style="transform: translateY({{item.translateY}}px); transition: {{(isDragging && currentIndex !== index) ? 'transform 0.2s ease-out' : (isDropping ? 'transform 0.3s cubic-bezier(0.18, 0.89, 0.32, 1.28)' : 'none')}}; z-index: {{currentIndex === index ? 100 : 10}};">
        <view class="item-content {{targetIndex === index && currentIndex !== index ? 'target-position' : ''}}">
          <view class="drag-handle">
            <van-icon name="wap-nav" size="20px" color="#ccc" />
          </view>
          <view class="category-icon">
            <!-- 根据图标类型显示不同内容 -->
            <block wx:if="{{item.imageType === 'text'}}">
              <view class="text-icon">{{item.iconContent}}</view>
            </block>
            <block wx:elif="{{item.imageType === 'emoji'}}">
              <view class="emoji-icon">{{item.iconContent}}</view>
            </block>
            <block wx:else>
              <image src="{{item.image}}" mode="aspectFit"></image>
            </block>
          </view>
          <view class="category-name">{{item.name}}</view>
        </view>
      </view>
    </block>
  </view>

  <!-- 底部保存按钮 -->
  <view class="save-button" bindtap="saveSort">保存</view>
</view>