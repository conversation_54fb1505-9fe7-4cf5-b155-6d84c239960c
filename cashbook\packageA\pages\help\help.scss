/* packageA/pages/help/help.scss */
.content {
  background-color: #f5f5f5;
  min-height: 100vh;
  padding: 0 0 30rpx 0;
  position: relative;
  padding-top: 20rpx;

  .header {
    display: flex;
    justify-content: space-between;
    align-items: center;
    padding: 20rpx 30rpx;
    background-color: #ffffff;

    .close-btn {
      width: 60rpx;
      height: 60rpx;
      display: flex;
      align-items: center;
      justify-content: center;
      background-color: #f0f0f0;
      border-radius: 50%;

      image {
        width: 30rpx;
        height: 30rpx;
      }
    }

    .more-btn {
      font-size: 28rpx;
      color: #333;
    }
  }

  .nav {
    margin-bottom: 20rpx;
    position: fixed;
    top: 0;
    left: 0;
    right: 0;
    z-index: 200;
  }

  // 导航栏占位符，防止内容被遮挡
  .nav-placeholder {
    height: 180rpx; // 根据您的导航栏实际高度调整
  }

  .help-card {
    margin: 20rpx;
    background-color: #ffffff;
    background-image: url('../../assets/images/help.png');
    border-radius: 40rpx;
    padding: 30rpx;
    position: fixed;
    top: 180rpx; // 调整顶部距离，确保在导航栏下方
    left: 0;
    right: 0;
    z-index: 100;
    overflow: hidden;
    box-shadow: 0 2rpx 10rpx rgba(0, 0, 0, 0.05);

    .help-title {
      display: flex;
      align-items: center;
      margin-bottom: 20rpx;

      .icon-container {
        width: 100rpx;
        height: 100rpx;
        background-color: #f5f5f5;
        border-radius: 30%;
        display: flex;
        align-items: center;
        justify-content: center;
        margin-right: 20rpx;
        position: relative;
        overflow: hidden;

        .help-icon {
          width: 60rpx;
          height: 60rpx;
          border-radius: 50%;
        }

        // &::after {
        //   content: '';
        //   position: absolute;
        //   top: 0;
        //   left: 0;
        //   right: 0;
        //   bottom: 0;
        //   border: 2rpx solid rgba(0, 0, 0, 0.05);
        //   border-radius: 50%;
        //   pointer-events: none;
        // }
      }

      text {
        font-size: 36rpx;
        font-weight: bold;
        color: #333;
      }

      .help-tag {
        margin-left: auto;
        font-size: 26rpx;
        color: #888;
        background-color: #f5f5f5;
        padding: 6rpx 20rpx;
        border-radius: 30rpx;
      }
    }

    .help-content {
      font-size: 30rpx;
      color: #666;
      line-height: 1.6;
      position: relative;
      // padding-right: 150rpx;

      .decoration {
        position: absolute;
        right: -30rpx;
        bottom: -30rpx;
        width: 180rpx;
        height: 180rpx;
        opacity: 0.8;
      }
    }
  }

  .guide-section {
    padding: 0 20rpx;
    margin-top: 290rpx; // 增加顶部空间，为固定的导航栏和help-card留出足够空间

    // 加载提示
    .loading {
      text-align: center;
      padding: 30rpx 0;
      color: #999;
    }

    // 新的卡片容器样式
    .guide-cards-container {
      display: flex;
      flex-wrap: wrap;
      justify-content: space-between;
    }

    // 修改卡片样式
    .guide-card {
      width: 43%;
      background-color: #ffffff;
      border-radius: 16rpx;
      padding: 30rpx 20rpx;
      position: relative;
      box-shadow: 0 2rpx 10rpx rgba(0, 0, 0, 0.05);
      margin-bottom: 30rpx;

      .guide-title {
        display: flex;
        align-items: flex-start;
        margin-bottom: 30rpx;

        .guide-tag {
          width: 8rpx;
          height: 40rpx;
          background: linear-gradient(to bottom, #aee05d, #cae4a0, #fff);
          border-radius: 30%;
          margin-right: 15rpx;
        }

        text {
          font-size: 28rpx;
          font-weight: 500;
        }
      }

      .guide-video {
        font-size: 26rpx;
        background-color: #f5f5f5;
        color: #333;
        padding: 8rpx 20rpx;
        border-radius: 30rpx;
        display: inline-block;
      }

      .guide-dots {
        position: absolute;
        bottom: 20rpx;
        left: 0;
        right: 0;
        text-align: center;
        color: #ccc;
      }
    }
  }
}

// 旧的视频弹窗样式(移除)
// .video-popup {
//   position: fixed;
//   top: 0;
//   left: 0;
//   width: 100%;
//   height: 100%;
//   z-index: 999;
//   display: flex;
//   align-items: center;
//   justify-content: center;
// }

// .video-popup-mask {
//   position: absolute;
//   top: 0;
//   left: 0;
//   width: 100%;
//   height: 100%;
//   background-color: rgba(0, 0, 0, 0.7);
//   z-index: 1;
// }

// .video-popup-content {
//   width: 90%;
//   max-width: 650rpx;
//   background-color: #fee7ea;
//   border-radius: 20rpx;
//   overflow: hidden;
//   position: relative;
//   z-index: 2;
//   box-shadow: 0 0 20rpx rgba(0, 0, 0, 0.2);
// }

// 全屏视频弹窗样式
.fullscreen-video-popup {
  position: fixed;
  top: 0;
  left: 0;
  width: 100%;
  height: 100vh;
  z-index: 999;
  background-color: #ffffff;
  display: flex;
  flex-direction: column;
  padding-top: env(safe-area-inset-top); // 添加安全区域顶部内边距
  
  // 添加淡入淡出动画效果
  opacity: 0; // 初始透明度为0
  animation: fadeIn 0.3s ease forwards; // 0.3秒淡入动画
}

// 淡入动画
@keyframes fadeIn {
  from { opacity: 0; }
  to { opacity: 1; }
}

// 淡出动画
@keyframes fadeOut {
  from { opacity: 1; }
  to { opacity: 0; }
}

// 添加淡出效果的类
.fade-out {
  animation: fadeOut 0.3s ease forwards;
}

// 顶部安全区域占位
.safe-area-top {
  height: 44px; // 状态栏默认高度，适用于大多数设备
}

// 顶部导航
.popup-header {
  width: 100%;
  height: 90rpx;
  display: flex;
  align-items: center;
  justify-content: space-between; // 更改为space-between布局
  position: relative;

}

.popup-close {
  margin-left: 20px;
  width: 60rpx;
  height: 60rpx;
  border-radius: 50%;
  background-color: rgba(255, 255, 255, 0.8);
  display: flex;
  align-items: center;
  justify-content: center;
  box-shadow: 0 2rpx 8rpx rgba(0, 0, 0, 0.1);
  font-size: 40rpx;
  font-weight: bold;
  color: #333;
  z-index: 10;
}

// 用于保持标题居中的空白占位元素
.popup-close-placeholder {
  width: 60rpx;
  height: 60rpx;
}

.popup-title {
  font-size: 32rpx;
  font-weight: bold;
  color: #333;
  text-align: center;
}

// 功能标题
.feature-title {
  width: 100%;
  display: flex;
  align-items: center;
  padding: 30rpx;
}

.dot {
  width: 16rpx;
  height: 16rpx;
  border-radius: 50%;
  background-color: #ffcc00;
  margin-right: 16rpx;
}

.title-text {
  font-size: 42rpx;
  font-weight: bold;
  color: #5dd5db; // 蓝绿色
  text-shadow: 0 2rpx 4rpx rgba(0, 0, 0, 0.15);
}

// 视频播放区域
.popup-video-container {
  flex: 1;
  width: 100%;
  position: relative;
}

.popup-video-player {
  width: 100%;
  height: 100%;
  background-color: transparent;
}

// 自定义视频控制器
.custom-video-controls {
  position: fixed;
  bottom: 0;
  left: 0;
  width: 100%;
  background-color: rgba(255, 255, 255, 0.5);
  padding: 20rpx 30rpx;
  box-sizing: border-box;
  backdrop-filter: blur(10rpx);
  padding-bottom: env(safe-area-inset-bottom); // 添加安全区域底部内边距
}

.progress-bar {
  width: 100%;
  height: 6rpx;
  background-color: rgba(0, 0, 0, 0.1);
  position: relative;
  border-radius: 3rpx;
  overflow: hidden;
  margin-bottom: 20rpx;
}

.progress-bg {
  position: absolute;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
  background-color: rgba(0, 0, 0, 0.1);
}

.progress-current {
  position: absolute;
  top: 0;
  left: 0;
  height: 100%;
  background-color: #97bf75; // 绿色进度条
  border-radius: 3rpx;
  transition: width 0.1s linear;
}

.control-buttons {
  display: flex;
  justify-content: flex-end;
  align-items: center;
  padding: 10rpx 0;
}

.playback-rate {
  width: 80rpx;
  height: 80rpx;
  border-radius: 50%;
  background-color: #97bf75; // 绿色播放速率按钮
  display: flex;
  align-items: center;
  justify-content: center;
  font-size: 28rpx;
  color: white;
  margin-right: 20rpx;
}

.play-pause-btn {
  width: 80rpx;
  height: 80rpx;
  border-radius: 50%;
  background-color: #97bf75; // 绿色播放暂停按钮
  display: flex;
  align-items: center;
  justify-content: center;
  color: white;
}

.play-icon,
.pause-icon {
  font-size: 32rpx;
  font-weight: bold;
}