.list{
 margin: 0 10px;
 background-color: #fbfbfb;
}
.oneColonm{
  display: grid;
  grid-template-columns: 1fr 1fr;
  align-items: center;
}
.time{
  background-color: #a1b386;
  padding: 3px 5px;
  border-radius: 10px;
  color: white;
   justify-self: end;
  width: fit-content;
}

/* 列表 */
.info{
  display: grid;
  grid-template-columns: auto auto auto 1fr;
  margin-top: 10px;
  color: #8e8e8e;
  gap: 10px;
}
.info view:nth-child(4){
  justify-self: end;
}

.item2{
  display: grid;
  grid-template-columns: 55px auto 1fr;
  align-self: center;
  align-items: center;
  gap: 10px;
  margin: 0 10px ;
  border-bottom: 1px solid #dddddd;
  padding: 10px 0;
}
.item:last-child .item2{
  border-bottom: 0px solid;
}
.avator{
  width: 50px;
  height: 50px;
  border-radius: 15px;
  overflow: hidden;
  display: grid;
  align-items: center;
  justify-content: center;
  background-color: #f2f2f2;
}
.share image{
  width: 20px;
  height: 20px;
}
.avator image{
  width: 45px;
  height: 45px;
  object-fit: cover;
}

.acount{
   justify-self: end;
  color: red;
  font-size: 42rpx;
}