.list{
 margin: 0 10px;
 background-color: transparent;
}
.oneColonm{
  display: grid;
  grid-template-columns: 1fr 1fr;
  align-items: center;
}
.time{
  background-color: #a1b386;
  padding: 3px 5px;
  border-radius: 10px;
  color: white;
   justify-self: end;
  width: fit-content;
}

/* 日期标题栏样式 - 按时间排序 */
.date-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 10px 5px;
  border-bottom: 1px solid #f0f0f0;
  margin-top: 10px;
}

.date-left {
  display: flex;
  align-items: center;
}

.date-day {
  font-size: 15px;
  color: #333;
  font-weight: 500;
  margin-right: 8px;
}

.date-today {
  font-size: 12px;
  color: #888;
  margin-right: 8px;
}

.date-refresh {
  width: 18px;
  height: 18px;
}

.date-income {
  font-size: 13px;
  color: #888;
}

/* 列表项共用样式 */
.info{
  display: grid;
  grid-template-columns: auto auto auto 1fr;
  margin-top: 10px;
  color: #8e8e8e;
  gap: 10px;
  padding: 5px 0;
  border-bottom: 1px solid #f0f0f0;
}
.info view:nth-child(4){
  justify-self: end;
}

.item2{
  display: grid;
  grid-template-columns: 60px auto 1fr;
  align-self: center;
  align-items: center;
  gap: 10px;
  margin: 0 10px;
  border-bottom: 1px solid #f0f0f0;
  padding: 12px 0;
  background-color: #fff;
}
.item:last-child .item2{
  border-bottom: 0px solid;
}
.avator{
  width: 50px;
  height: 50px;
  border-radius: 50%;
  overflow: hidden;
  display: flex;
  align-items: center;
  justify-content: center;
  background-color: #ff9966;
}
.share image{
  width: 20px;
  height: 20px;
}
.avator image{
  width: 30px;
  height: 30px;
  object-fit: cover;
}

.acount{
  justify-self: end;
  color: #ff3333;
  font-size: 16px;
  font-weight: 500;
}

/* 内部转账样式 */
.transfer-item {
  display: flex;
  position: relative;
  padding: 10px;
  /* background-color: #fff; */
  border-radius: 0;
  margin: 0;
  align-items: center;
  box-shadow: none;
  border-bottom: 1px solid #f5f5f5;
  justify-content: space-between;
  overflow: hidden; /* 防止内容溢出 */
}

.transfer-icon {
  width: 55px;
  height: 55px;
  background-color: #f5f7f6;
  border-radius: 30%;
  display: flex;
  justify-content: center;
  align-items: center;
  border: 1px solid #eef0ef;
  flex-shrink: 0;
  margin-right: 10px; /* 添加右边距，避免与中间内容过近 */
}

.transfer-icon image {
  width: 25px;
  height: 25px;
  opacity: 0.7;
}

.transfer-content {
  flex: 1;
  display: flex;
  flex-direction: column;
  justify-content: center;
  max-width: 200px;
  overflow: hidden; /* 防止内容溢出 */
  white-space: nowrap; /* 文本不换行 */
  text-overflow: ellipsis; /* 溢出显示省略号 */
  margin-right: 10px; /* 与右侧金额保持距离 */
}

.transfer-title {
  font-size: 12px;
  color: #333;
  font-weight: 500;
  margin-bottom: 5px;
  line-height: 1.2;
}

.transfer-desc-text {
  font-size: 12px;
  color: #999;
  white-space: nowrap;
  overflow: hidden;
  text-overflow: ellipsis;
  max-width: 220px;
  line-height: 1.2;
}

.transfer-right {
  width: 120px; /* 固定宽度 */
  min-width: 100px; /* 最小宽度 */
  max-width: 80px; /* 最大宽度限制 */
  display: flex;
  flex-direction: column;
  justify-content: center;
  align-items: flex-end;
  text-align: right;
  flex-shrink: 0; /* 防止被压缩 */
  overflow: hidden; /* 防止内容溢出 */
}

.transfer-amount {
  font-size: 16px;
  font-weight: 500;
  color: #333;
  text-align: right;
  margin-bottom: 5px;
  line-height: 1.2;
  width: 100%;
  overflow: hidden; /* 防止内容溢出 */
  text-overflow: ellipsis; /* 溢出显示省略号 */
  white-space: nowrap; /* 文本不换行 */
}

.transfer-remark {
  font-size: 12px;
  color: #999;
  text-align: right;
  line-height: 1.2;
  width: 100%;
  overflow: hidden; /* 防止内容溢出 */
  text-overflow: ellipsis; /* 溢出显示省略号 */
  white-space: nowrap; /* 文本不换行 */
}