// pages/my/shoplist/shoplist.js
import { shoppinglist } from '../../../api/user/index'
Page({
  /**
   * 页面的初始数据
   */
  data: {
    lists: [],
    tablist: [
      { id: 1, name: '进行中' },
      { id: 2, name: '已暂停' },
      { id: 3, name: '已购清单' }
    ],
    iconpath: '/static/icon/help.png',
    status: 'normal',
    limit: 5,
    selectedId: null,
    more: false
  },
  onGetTitleId(e) {
    console.log('当前选中的 tab id 是：', e.detail)
    if (e.detail == 1) {
      this.setData({
        limit: 5,
        status: 'normal'
      })
      this.shoppinglists()
    } else if (e.detail == 2) {
      this.setData({
        limit: 5,
        status: 'pause'
      })
      this.shoppinglists()
    } else {
      this.setData({
        limit: 5,
        status: 'over'
      })
      this.shoppinglists()
    }
    this.setData({
      selectedId: e.detail
    })
  },
  shoppinglists() {
    wx.showLoading({
      title: '保存中...'
    })

    // 调用更新用户信息接口
    shoppinglist({
      status: this.data.status,
      page: 1,
      limit: this.data.limit
    })
      .then((res) => {
        wx.hideLoading()
        console.log(res, 'res-----------')
        if (res && res.code === 1) {
          this.setData({
            lists: res.data.data
          })
          if (res.data.data.length >= res.data.total) {
            this.setData({
              more: true
            })
          } else {
            this.setData({
              more: false
            })
          }
          // 更新本地显示
        } else {
        }
      })
      .catch((err) => {})
  },

  toEdit(e) {
    wx.navigateTo({
      url: '/pages/my/shoplist/shopEdit/shopEdit?id=' + e.currentTarget.dataset.id.id
    })
  },
  addShop() {
    wx.navigateTo({
      url: '/pages/my/shoplist/shopEdit/shopEdit?isAdd=' + 1
    })
  },
  toDetail(event) {
    const id = event.currentTarget.dataset.id.id
    console.log(id, 'id')
    wx.navigateTo({
      url: `/pages/my/shoplist/shopDetail/shopDetail?id=${id}`
    })
  },
  onLoad(options) {},

  /**
   * 生命周期函数--监听页面初次渲染完成
   */
  onReady() {},

  /**
   * 生命周期函数--监听页面显示
   */
  onShow() {
    this.shoppinglists()
  },

  /**
   * 生命周期函数--监听页面隐藏
   */
  onHide() {},

  /**
   * 生命周期函数--监听页面卸载
   */
  onUnload() {},

  /**
   * 页面相关事件处理函数--监听用户下拉动作
   */
  onPullDownRefresh() {},

  /**
   * 页面上拉触底事件的处理函数
   */
  onReachBottom() {
    if (!this.data.more) {
      this.setData({
        limit: this.data.limit + 5
      })
      this.shoppinglists()
    }
  },

  /**
   * 用户点击右上角分享
   */
  onShareAppMessage() {}
})
