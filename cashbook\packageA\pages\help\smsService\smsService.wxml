<!--packageA/pages/help/smsService/smsService.wxml-->
<view class="category-container">
  <!-- 使用TDesign Tab组件 -->
  <t-tabs value="{{ activeTab }}" bind:change="onTabChange" t-class="custom-tabs">
    <t-tab-panel label="支出" value="expenses">
      <!-- 支出分类列表 -->
  <view class="category-content">
      <!-- 加载中 -->
      <view class="loading-container" wx:if="{{loading.expenses}}">
          <t-loading theme="circular" size="48rpx" loading />
      </view>
      
      <!-- 空状态 -->
      <view class="empty-state" wx:elif="{{!expensesCategories || expensesCategories.length === 0}}">
          <t-empty description="暂无支出分类" />
      </view>
      
      <!-- 分类项目 -->
      <block wx:else>
        <view class="category-section" wx:for="{{expensesCategories}}" wx:key="id" wx:for-item="section">
          <!-- 如果有分组 -->
          <view class="section-header" wx:if="{{section.name}}">
            <view class="section-title">{{section.name}}</view>
          </view>
          
          <view class="category-grid">
            <view class="category-item" wx:for="{{section.children || section.list || [section]}}" wx:key="id" wx:if="{{!item.child || !item.child.length}}">
              <view class="category-icon" style="background-color: {{item.background || '#f0f0f0'}}">
                <image src="{{item.image}}" mode="aspectFit" wx:if="{{item.image}}"></image>
                <text wx:else>{{item.name[0]}}</text>
              </view>
              <text class="category-name">{{item.name}}</text>
            </view>
          </view>
          
          <!-- 使用Collapse组件显示带有子项的分类 -->
          <view class="collapse-container" wx:for="{{section.children || section.list || [section]}}" wx:key="id" wx:for-item="parentItem" wx:if="{{parentItem.child && parentItem.child.length}}">
              <t-collapse value="{{ collapseValues[parentItem.id] }}" bind:change="onCollapseChange" data-parent-id="{{parentItem.id}}">
                <t-collapse-panel header="{{parentItem.name}}" name="{{parentItem.id}}">
                <view class="collapse-content">
                  <view class="category-grid">
                    <view class="category-item" wx:for="{{parentItem.child}}" wx:key="id" wx:for-item="childItem">
                      <view class="category-icon" style="background-color: {{childItem.background || '#f0f0f0'}}">
                        <image src="{{childItem.image}}" mode="aspectFit" wx:if="{{childItem.image}}"></image>
                        <text wx:else>{{childItem.name[0]}}</text>
                      </view>
                      <text class="category-name">{{childItem.name}}</text>
                    </view>
                  </view>
                </view>
                </t-collapse-panel>
              </t-collapse>
          </view>
        </view>
      </block>
    </view>
    </t-tab-panel>
    
    <t-tab-panel label="收入" value="income">
    <!-- 收入分类列表 -->
      <view class="category-content">
      <!-- 加载中 -->
      <view class="loading-container" wx:if="{{loading.income}}">
          <t-loading theme="circular" size="48rpx" loading />
      </view>
      
      <!-- 空状态 -->
      <view class="empty-state" wx:elif="{{!incomeCategories || incomeCategories.length === 0}}">
          <t-empty description="暂无收入分类" />
      </view>
      
      <!-- 分类项目 -->
      <block wx:else>
        <view class="category-section" wx:for="{{incomeCategories}}" wx:key="id" wx:for-item="section">
          <!-- 如果有分组 -->
          <view class="section-header" wx:if="{{section.name}}">
            <view class="section-title">{{section.name}}</view>
          </view>
          
          <view class="category-grid">
            <view class="category-item" wx:for="{{section.children || section.list || [section]}}" wx:key="id" wx:if="{{!item.child || !item.child.length}}">
              <view class="category-icon" style="background-color: {{item.background || '#f0f0f0'}}">
                <image src="{{item.image}}" mode="aspectFit" wx:if="{{item.image}}"></image>
                <text wx:else>{{item.name[0]}}</text>
              </view>
              <text class="category-name">{{item.name}}</text>
            </view>
          </view>
          
          <!-- 使用Collapse组件显示带有子项的分类 -->
          <view class="collapse-container" wx:for="{{section.children || section.list || [section]}}" wx:key="id" wx:for-item="parentItem" wx:if="{{parentItem.child && parentItem.child.length}}">
              <t-collapse value="{{ collapseValues[parentItem.id] }}" bind:change="onCollapseChange" data-parent-id="{{parentItem.id}}">
                <t-collapse-panel header="{{parentItem.name}}" name="{{parentItem.id}}">
                <view class="collapse-content">
                  <view class="category-grid">
                    <view class="category-item" wx:for="{{parentItem.child}}" wx:key="id" wx:for-item="childItem">
                      <view class="category-icon" style="background-color: {{childItem.background || '#f0f0f0'}}">
                        <image src="{{childItem.image}}" mode="aspectFit" wx:if="{{childItem.image}}"></image>
                        <text wx:else>{{childItem.name[0]}}</text>
                      </view>
                      <text class="category-name">{{childItem.name}}</text>
                    </view>
                  </view>
                </view>
                </t-collapse-panel>
              </t-collapse>
          </view>
        </view>
      </block>
    </view>
    </t-tab-panel>
  </t-tabs>
</view>