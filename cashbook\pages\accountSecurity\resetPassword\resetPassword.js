// pages/accountSecurity/resetPassword/resetPassword.js
Page({
  data: {
    password: '',
    confirmPassword: '',
    showPassword: false,
    showConfirmPassword: false
  },

  // 密码输入
  onPasswordInput: function(e) {
    this.setData({
      password: e.detail.value
    });
  },

  // 确认密码输入
  onConfirmPasswordInput: function(e) {
    this.setData({
      confirmPassword: e.detail.value
    });
  },

  // 切换密码可见性
  togglePasswordVisibility: function() {
    this.setData({
      showPassword: !this.data.showPassword
    });
  },

  // 切换确认密码可见性
  toggleConfirmPasswordVisibility: function() {
    this.setData({
      showConfirmPassword: !this.data.showConfirmPassword
    });
  },

  // 重置密码
  resetPassword: function() {
    const { password, confirmPassword } = this.data;
    
    // 验证密码
    if (!password) {
      wx.showToast({
        title: '请输入密码',
        icon: 'none'
      });
      return;
    }
    
    if (password.length < 6) {
      wx.showToast({
        title: '密码长度不能少于6位',
        icon: 'none'
      });
      return;
    }
    
    if (password !== confirmPassword) {
      wx.showToast({
        title: '两次密码输入不一致',
        icon: 'none'
      });
      return;
    }
    
    // 这里应该调用API修改密码
    // 模拟修改成功
    wx.showToast({
      title: '密码修改成功',
      icon: 'success',
      duration: 2000,
      success: () => {
        setTimeout(() => {
          // 返回账号安全页面
          wx.navigateBack({
            delta: 2 // 返回两层，跳过验证码页面
          });
        }, 2000);
      }
    });
  },

  // 返回上一页
  goBack: function() {
    wx.navigateBack();
  }
});