// pages/accountSecurity/accountSecurity.js
import { getUserInfo, updateUserInfo } from '../../api/user/index';

Page({
  data: {
    userInfo: {
      mobile: '',
      hasPassword: false
    },
    wechatBound: false
  },

  onLoad: function(options) {
    // 页面加载时获取用户信息
    this.getUserInfo();
  },

  // 获取用户信息
  getUserInfo: function() {
    wx.showLoading({
      title: '加载中...',
    });
    
    // 调用封装的用户信息接口
    getUserInfo().then(res => {
      wx.hideLoading();
      
      if (res && res.code === 1) {
        const userData = res.data;
        
        // 更新用户信息
        this.setData({
          userInfo: {
            mobile: userData.mobile || '',
            hasPassword: !!userData.has_password
          },
          wechatBound: !!userData.wechat_bound
        });
      } else {
        wx.showToast({
          title: '获取用户信息失败',
          icon: 'none'
        });
      }
    }).catch(err => {
      wx.hideLoading();
      wx.showToast({
        title: '网络请求失败',
        icon: 'none'
      });
      console.error('获取用户信息失败:', err);
    });
  },

  // 修改手机号
  modifyPhone: function() {
    wx.navigateTo({
      url: '/pages/accountSecurity/verifyPhone/verifyPhone?phone=' + this.data.userInfo.mobile
    });
  },

  // 修改密码
  modifyPassword: function() {
    wx.navigateTo({
      url: '/pages/accountSecurity/resetPassword/resetPassword?phone=' + this.data.userInfo.mobile
    });
  },

  // 绑定/解绑微信
  toggleWechatBind: function() {
    if (this.data.wechatBound) {
      // 已绑定，提示是否解绑
      wx.showModal({
        title: '解除绑定',
        content: '确定要解除微信绑定吗？',
        success: (res) => {
          if (res.confirm) {
            this.unbindWechat();
          }
        }
      });
    } else {
      // 未绑定，进行绑定
      this.bindWechat();
    }
  },

  // 绑定微信
  bindWechat: function() {
    wx.showLoading({
      title: '绑定中...',
    });
    
    // 这里应该调用实际的绑定微信API
    // 示例代码
    setTimeout(() => {
      wx.hideLoading();
      
      this.setData({
        wechatBound: true
      });
      
      wx.showToast({
        title: '绑定成功',
        icon: 'success'
      });
    }, 1000);
  },

  // 解绑微信
  unbindWechat: function() {
    wx.showLoading({
      title: '解绑中...',
    });
    
    // 这里应该调用实际的解绑微信API
    // 示例代码
    setTimeout(() => {
      wx.hideLoading();
      
      this.setData({
        wechatBound: false
      });
      
      wx.showToast({
        title: '解绑成功',
        icon: 'success'
      });
    }, 1000);
  },

  // 注销账号
  deleteAccount: function() {
    wx.showModal({
      title: '注销账号',
      content: '注销后无法恢复，确定要注销账号吗？',
      success: (res) => {
        if (res.confirm) {
          wx.navigateTo({
            url: '/pages/accountSecurity/deleteAccount/deleteAccount',
          });
        }
      }
    });
  },

  // 返回上一页
  goBack: function() {
    wx.navigateBack();
  }
});