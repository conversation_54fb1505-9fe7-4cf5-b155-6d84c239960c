<wxs module="utils">
  function formatPercentage(remaining, total) {
    if (!total) return "0.0";
    var percentage = remaining / total * 100;
    // 保留一位小数
    return percentage.toFixed(1);
  }
  
  function formatAmount(amount) {
    if (amount === undefined || amount === null) return "0.00";
    return parseFloat(amount).toFixed(2);
  }

  module.exports = {
    formatPercentage: formatPercentage,
    formatAmount: formatAmount
  };
</wxs>
<view class="container">
  <!-- 自定义导航栏 -->
  <view class="nav">
    <view class="back-btn" bind:tap="back">×</view>
    <view class="title">{{currentTitle}}</view>
    <view class="calendar-btn" bindtap="showMonthPicker">
      <image src="/static/icon/calendar.png" mode="aspectFit" />
    </view>
  </view>
  <!-- 内容区域 - 使用swiper -->
  <swiper class="content-swiper" current="{{bottomId}}" bindchange="swperChange" duration="300">
    <swiper-item>
      <!-- 月预算内容 -->
      <scroll-view scroll-y class="content-scroll">
        <!-- 账本卡片 -->
        <view class="budget-card">
          <view class="card-header">
            <view class="account-info">
              <view class="account-name">默认账本</view>
              <view class="date-range">{{selectedMonthDisplay}}</view>
            </view>
            <view class="header-actions">
              <view class="share-btn" bindtap="showShareCard">
                <van-icon name="share-o" size="20px" color="#666" />
              </view>
              <view class="setting-btn" bind:tap="goToSetting">设置</view>
            </view>
          </view>
          <!-- 预算金额 -->
          <view class="budget-amount">
            <view class="amount-wrapper">
              <view>
                <view class="amount-label">剩余月预算 (元)</view>
                <view class="amount-value">
                  <number-scroll value="{{monthBudget.remaining}}" duration="1000" decimals="2" />
                </view>
              </view>
              <view class="progress-circle-container">
                <view class="progress-circle month-circle" style="background: conic-gradient(#a4cfa0 {{utils.formatPercentage(monthBudget.remaining, monthBudget.total)}}%, #f0f0f0 0%);">
                  <view class="progress-circle-inner"></view>
                </view>
              </view>
            </view>
          </view>
          <!-- 预算统计 -->
          <view class="budget-stats">
            <view class="stat-item">
              <view class="stat-label">本月消费</view>
              <view class="stat-value">
                ¥
                <number-scroll value="{{monthBudget.spent}}" duration="1000" decimals="2" />
              </view>
            </view>
            <view class="stat-item">
              <view class="stat-label">月预算</view>
              <view class="stat-value">
                ¥
                <number-scroll value="{{monthBudget.total}}" duration="1000" decimals="2" />
              </view>
            </view>
            <view class="stat-item">
              <view class="stat-label">预算剩余</view>
              <view class="stat-value">
                <number-scroll value="{{utils.formatPercentage(monthBudget.remaining, monthBudget.total)}}" duration="1000" decimals="1" />
                %
              </view>
            </view>
            <view class="stat-item">
              <view class="stat-label">剩余天数</view>
              <view class="stat-value">剩{{monthBudget.remainingDays}}天</view>
            </view>
          </view>
          <!-- 分割线 -->
          <view class="divider"></view>
          <!-- 日均统计 -->
          <view class="daily-stats">
            <view class="daily-item">
              <view class="dot orange"></view>
              <view class="daily-label">本月日均消费</view>
              <view class="daily-value">¥ {{utils.formatAmount(monthBudget.dailySpent)}}</view>
            </view>
            <view class="daily-item">
              <view class="dot purple"></view>
              <view class="daily-label">日均预算</view>
              <view class="daily-value">¥ {{utils.formatAmount(monthBudget.dailyBudget)}}</view>
            </view>
            <view class="daily-item">
              <view class="dot blue"></view>
              <view class="daily-label">剩余每日可消费金额</view>
              <view class="daily-value">
                ¥ {{utils.formatAmount(monthBudget.remainingDailyAmount)}}
              </view>
            </view>
          </view>
        </view>
        <!-- 分类预算 -->
        <view class="category-budget">
          <view class="category-header">
            <view class="category-title">分类预算</view>
            <view class="add-btn" bind:tap="addCategoryBudget">添加</view>
          </view>
          <!-- 空状态 -->
          <view class="empty-state">
            <view class="empty-icon">
              <image src="/static/icon/card.png" mode="aspectFit" />
            </view>
            <view class="empty-text">没有发现分类预算哦，试着添加一个～</view>
          </view>
        </view>
      </scroll-view>
    </swiper-item>
    <swiper-item>
      <!-- 年预算内容 -->
      <scroll-view scroll-y class="content-scroll">
        <!-- 账本卡片 -->
        <view class="budget-card">
          <view class="card-header">
            <view class="account-info">
              <view class="account-name">默认账本</view>
              <view class="date-range">{{selectedYearDisplay}}</view>
            </view>
            <view class="header-actions">
              <view class="share-btn" bindtap="showShareCard">
                <van-icon name="share-o" size="20px" color="#666" />
              </view>
              <view class="setting-btn" bind:tap="goToSetting">设置</view>
            </view>
          </view>
          <!-- 预算金额 -->
          <view class="budget-amount">
            <view class="amount-wrapper">
              <view>
                <view class="amount-label">剩余年预算 (元)</view>
                <view class="amount-value">
                  <number-scroll value="{{yearBudget.remaining}}" duration="1000" decimals="2" />
                </view>
              </view>
              <view class="progress-circle-container">
                <view class="progress-circle year-circle" style="background: conic-gradient(#a4cfa0 {{utils.formatPercentage(yearBudget.remaining, yearBudget.total)}}%, rgba(255,255,255,0.3) 0%);">
                  <view class="progress-circle-inner"></view>
                </view>
              </view>
            </view>
          </view>
          <!-- 预算统计 -->
          <view class="budget-stats">
            <view class="stat-item">
              <view class="stat-label">本年消费</view>
              <view class="stat-value">
                ¥
                <number-scroll value="{{yearBudget.spent}}" duration="1000" decimals="2" />
              </view>
            </view>
            <view class="stat-item">
              <view class="stat-label">年预算</view>
              <view class="stat-value">
                ¥
                <number-scroll value="{{yearBudget.total}}" duration="1000" decimals="2" />
              </view>
            </view>
            <view class="stat-item">
              <view class="stat-label">预算剩余</view>
              <view class="stat-value">
                <number-scroll value="{{utils.formatPercentage(yearBudget.remaining, yearBudget.total)}}" duration="1000" decimals="1" />
                %
              </view>
            </view>
            <view class="stat-item">
              <view class="stat-label">剩余月数</view>
              <view class="stat-value">剩{{yearBudget.remainingMonths}}月</view>
            </view>
          </view>
          <!-- 分割线 -->
          <view class="divider"></view>
          <!-- 日均统计 -->
          <view class="daily-stats">
            <view class="daily-item">
              <view class="dot orange"></view>
              <view class="daily-label">本年月均消费</view>
              <view class="daily-value">¥ {{utils.formatAmount(yearBudget.monthlySpent)}}</view>
            </view>
            <view class="daily-item">
              <view class="dot purple"></view>
              <view class="daily-label">月均预算</view>
              <view class="daily-value">¥ {{utils.formatAmount(yearBudget.monthlyBudget)}}</view>
            </view>
            <view class="daily-item">
              <view class="dot blue"></view>
              <view class="daily-label">剩余每月可消费金额</view>
              <view class="daily-value">
                ¥ {{utils.formatAmount(yearBudget.remainingMonthlyAmount)}}
              </view>
            </view>
          </view>
        </view>
        <!-- 分类预算 -->
        <view class="category-budget">
          <view class="category-header">
            <view class="category-title">分类预算</view>
            <view class="add-btn" bind:tap="addCategoryBudget">添加</view>
          </view>
          <!-- 空状态 -->
          <view class="empty-state">
            <view class="empty-icon">
              <image src="/static/icon/card.png" mode="aspectFit" />
            </view>
            <view class="empty-text">没有发现分类预算哦，试着添加一个～</view>
          </view>
        </view>
      </scroll-view>
    </swiper-item>
  </swiper>
  <!-- 底部切换按钮 - 使用图片中的样式 -->
  <view class="tab-toggle-container">
    <view class="tab-toggle">
      <view class="toggle-option {{bottomId==0?'active':''}}" bindtap="changeBudgetType" data-type="month" data-index="0">
        月预算
      </view>
      <view class="toggle-option {{bottomId==1?'active':''}}" bindtap="changeBudgetType" data-type="year" data-index="1">
        年预算
      </view>
    </view>
  </view>
  <!-- 月份选择器组件 -->
  <month-picker visible="{{showPicker}}" initYear="{{pickerYear}}" initMonth="{{pickerMonth}}" mode="{{bottomId === 1 ? 'year' : 'month'}}" bind:cancel="hideMonthPicker" bind:confirm="onMonthSelected"></month-picker>
  <!-- 添加分享卡片组件到页面底部 -->
  <share-card visible="{{showSharePopup}}" buttonPosition="right" budgetData="{{shareBudgetData}}" bind:close="hideShareCard"></share-card>
</view>