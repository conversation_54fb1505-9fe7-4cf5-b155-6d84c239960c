 <view class="container">

  <!-- 自定义导航栏 -->
  <view class="nav">
    <view class="back-btn" bind:tap="back">×</view>
    <view class="title">{{currentTitle}}</view>
    <view class="calendar-btn" bindtap="showMonthPicker">
      <image src="/static/icon/calendar.png" mode="aspectFit"/>
    </view>
  </view>

  <!-- 内容区域 - 使用swiper -->
  <swiper class="content-swiper" current="{{bottomId}}" bindchange="swperChange" duration="300">
    <swiper-item>
      <!-- 月预算内容 -->
      <scroll-view scroll-y class="content-scroll"> 
        <!-- 账本卡片 -->
        <view class="budget-card">
          <view class="card-header">
            <view class="account-info">
              <view class="account-name">默认账本</view>
              <view class="date-range">5月1日-5月31日</view>
            </view>
            <view class="setting-btn" bind:tap="goToSetting">设置</view>
          </view>

          <!-- 预算金额 -->
          <view class="budget-amount">
            <view class="amount-wrapper">
              <view>
                <view class="amount-label">剩余月预算 (元)</view>
                <view class="amount-value">{{monthBudget.remaining}}</view>
              </view>
              <view class="progress-circle-container">
                <view class="progress-circle month-circle" style="--progress: 0;">
                  <view class="progress-circle-inner"></view>
                </view>
              </view>
            </view>
          </view>

          <!-- 预算统计 -->
          <view class="budget-stats">
            <view class="stat-item">
              <view class="stat-label">本月消费</view>
              <view class="stat-value">¥ 248.00</view>
            </view>
            <view class="stat-item">
              <view class="stat-label">月预算</view>
              <view class="stat-value">¥ 0.0</view>
            </view>
            <view class="stat-item">
              <view class="stat-label">预算剩余</view>
              <view class="stat-value">100.0%</view>
            </view>
            <view class="stat-item">
              <view class="stat-label">剩余天数</view>
              <view class="stat-value">剩15天</view>
            </view>
          </view>

          <!-- 分割线 -->
          <view class="divider"></view>

          <!-- 日均统计 -->
          <view class="daily-stats">
            <view class="daily-item">
              <view class="dot orange"></view>
              <view class="daily-label">本月日均消费</view>
              <view class="daily-value">¥ 14.59</view>
            </view>
            <view class="daily-item">
              <view class="dot purple"></view>
              <view class="daily-label">日均预算</view>
              <view class="daily-value">¥ 0.00</view>
            </view>
            <view class="daily-item">
              <view class="dot blue"></view>
              <view class="daily-label">剩余每日可消费金额</view>
              <view class="daily-value">¥ 0.00</view>
            </view>
          </view>
        </view>

        <!-- 分类预算 -->
        <view class="category-budget">
          <view class="category-header">
            <view class="category-title">分类预算</view>
            <view class="add-btn" bind:tap="addCategoryBudget">添加</view>
          </view>
          
          <!-- 空状态 -->
          <view class="empty-state">
            <view class="empty-icon">
              <image src="/static/icon/card.png" mode="aspectFit"/>
            </view>
            <view class="empty-text">没有发现分类预算哦，试着添加一个～</view>
          </view>
        </view>
      </scroll-view>
    </swiper-item>

    <swiper-item>
      <!-- 年预算内容 -->
      <scroll-view scroll-y class="content-scroll">
        <!-- 账本卡片 -->
        <view class="budget-card">
          <view class="card-header">
            <view class="account-info">
              <view class="account-name">默认账本</view>
              <view class="date-range">2023年度</view>
            </view>
            <view class="setting-btn" bind:tap="goToSetting">设置</view>
          </view>

          <!-- 预算金额 -->
          <view class="budget-amount">
            <view class="amount-wrapper">
              <view>
                <view class="amount-label">剩余年预算 (元)</view>
                <view class="amount-value">{{yearBudget.remaining}}</view>
              </view>
              <view class="progress-circle-container">
                <view class="progress-circle year-circle" style="--progress: {{yearBudget.progress}};">
                  <view class="progress-circle-inner"></view>
                </view>
              </view>
            </view>
          </view>

          <!-- 预算统计 -->
          <view class="budget-stats">
            <view class="stat-item">
              <view class="stat-label">本年消费</view>
              <view class="stat-value">¥ 248.00</view>
            </view>
            <view class="stat-item">
              <view class="stat-label">年预算</view>
              <view class="stat-value">¥ 0.0</view>
            </view>
            <view class="stat-item">
              <view class="stat-label">预算剩余</view>
              <view class="stat-value">100.0%</view>
            </view>
            <view class="stat-item">
              <view class="stat-label">剩余月数</view>
              <view class="stat-value">剩8月</view>
            </view>
          </view>

          <!-- 分割线 -->
          <view class="divider"></view>

          <!-- 日均统计 -->
          <view class="daily-stats">
            <view class="daily-item">
              <view class="dot orange"></view>
              <view class="daily-label">本年月均消费</view>
              <view class="daily-value">¥ 60.00</view>
            </view>
            <view class="daily-item">
              <view class="dot purple"></view>
              <view class="daily-label">月均预算</view>
              <view class="daily-value">¥ 0.00</view>
            </view>
            <view class="daily-item">
              <view class="dot blue"></view>
              <view class="daily-label">剩余每月可消费金额</view>
              <view class="daily-value">¥ 0.00</view>
            </view>
          </view>
        </view>

        <!-- 分类预算 -->
        <view class="category-budget">
          <view class="category-header">
            <view class="category-title">分类预算</view>
            <view class="add-btn" bind:tap="addCategoryBudget">添加</view>
          </view>
          
          <!-- 空状态 -->
          <view class="empty-state">
            <view class="empty-icon">
              <image src="/static/icon/card.png" mode="aspectFit"/>
            </view>
            <view class="empty-text">没有发现分类预算哦，试着添加一个～</view>
          </view>
        </view>
      </scroll-view>
    </swiper-item>
  </swiper>

  <!-- 底部切换按钮 - 使用图片中的样式 -->
  <view class="tab-toggle-container">
    <view class="tab-toggle">
      <view 
        class="toggle-option {{bottomId==0?'active':''}}" 
        bindtap="changeBudgetType" 
        data-type="month" 
        data-index="0">
        月预算
      </view>
      <view 
        class="toggle-option {{bottomId==1?'active':''}}" 
        bindtap="changeBudgetType" 
        data-type="year" 
        data-index="1">
        年预算
      </view>
    </view>
  </view>

  <!-- 月份选择器组件 -->
  <month-picker 
    visible="{{showPicker}}" 
    initYear="{{pickerYear}}" 
    initMonth="{{pickerMonth}}"
    bind:cancel="hideMonthPicker"
    bind:confirm="onMonthSelected">
  </month-picker>

</view>