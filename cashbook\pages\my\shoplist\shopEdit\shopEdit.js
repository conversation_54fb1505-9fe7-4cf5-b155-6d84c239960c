// pages/my/shoplist/shopEdit/shopEdit.js
import { addlist, detaillist, editlist } from '../../../../api/user/index'
Page({
  /**
   * 页面的初始数据
   */
  data: {
    list_id: '',
    isAdd: false, //新增的话显示时间模块
    detail: {
      name: '',
      notes: '',
      image: ''
    },
    chooseimage: false,
    // 日历
    showPicker: false,
    showYearSelector: false,
    showTimeSelector: false,
    selectedDate: {},
    timeString: '',
    weekDays: ['日', '一', '二', '三', '四', '五', '六'],
    monthsData: [],
    years: [],
    swiperCurrent: 0,
    timetype: '',
    starttime: '',
    endtime: ''
  },
  onInput(e) {
    this.setData({
      'detail.name': e.detail.value
    })
  },
  onInputt(e) {
    this.setData({
      'detail.notes': e.detail.value
    })
  },
  addShop() {
    this.setData({
      chooseimage: false
    })
  },
  /**
   * 生命周期函数--监听页面加载
   */
  onLoad(options) {
    let isAdd = options.isAdd
    if (isAdd == 1) {
      this.setData({
        isAdd: true
      })
    } else {
      this.setData({
        isAdd: false,
        list_id: options.id
      })
      this.detail()
    }
    // 日历
    const now = new Date()
    const year = now.getFullYear()
    const month = now.getMonth() + 1
    const day = now.getDate()
    const time = `${now.getHours().toString().padStart(2, '0')}:${now.getMinutes().toString().padStart(2, '0')}`

    const years = []
    for (let i = 2000; i <= year + 2; i++) years.push(i)

    const selectedDate = { year, month, day }
    this.setData({ selectedDate, timeString: time, years })
    this.updateMonthData(year, month)
  },
  detail() {
    detaillist({
      list_id: this.data.list_id
    })
      .then((res) => {
        wx.hideLoading()
        console.log(res, 'res-----------')
        if (res && res.code === 1) {
          // 更新本地显示
          this.setData({
            'detail.image': res.data.image,
            'detail.name': res.data.name,
            'detail.notes': res.data.notes
          })
        } else {
        }
      })
      .catch((err) => {})
  },
  gotime() {
    this.setData({
      showPicker: true,
      timetype: '1'
    })
  },
  onInputimage(e) {
    console.log(e.detail.value.charAt(0))
    this.setData({
      'detail.image': e.detail.value.charAt(0)
    })
  },
  gotime1() {
    this.setData({
      showPicker: true,
      timetype: '2'
    })
  },
  closeSettingsPopup() {
    this.setData({
      chooseimage: false
    })
  },
  chooseimages() {
    this.setData({
      chooseimage: true
    })
    console.log(this.data.chooseimage, '999999')
  },
  //   日历
  updateMonthData(centerYear, centerMonth, selectedDay = null) {
    const getPrev = (y, m) => (m === 1 ? [y - 1, 12] : [y, m - 1])
    const getNext = (y, m) => (m === 12 ? [y + 1, 1] : [y, m + 1])

    const [prevY, prevM] = getPrev(centerYear, centerMonth)
    const [nextY, nextM] = getNext(centerYear, centerMonth)

    const monthsData = [
      { month: prevM, days: this.generateCalendar(prevY, prevM, null) },
      { month: centerMonth, days: this.generateCalendar(centerYear, centerMonth, selectedDay) },
      { month: nextM, days: this.generateCalendar(nextY, nextM, null) }
    ]

    this.setData({
      monthsData,
      swiperCurrent: 1, // 中间是当前月
      selectedDate: {
        ...this.data.selectedDate,
        year: centerYear,
        month: centerMonth,
        day: selectedDay
      }
    })
  },

  generateCalendar(year, month, selectedDay) {
    const firstDay = new Date(year, month - 1, 1).getDay()
    const lastDate = new Date(year, month, 0).getDate()
    const days = []

    // 前置空白占位
    for (let i = 0; i < firstDay; i++) {
      days.push({ day: '', selected: false })
    }

    // 正常日期
    for (let i = 1; i <= lastDate; i++) {
      days.push({
        day: i,
        selected: i === selectedDay // ✔ 标记选中项
      })
    }

    return days
  },

  toggleYearSelector() {
    this.setData({ showYearSelector: !this.data.showYearSelector })
  },

  toggleTimeSelector() {
    this.setData({ showTimeSelector: !this.data.showTimeSelector })
  },
  addshoppingList() {
    if (this.data.detail.image == '') {
      this.setData({
        'detail.image': '😄'
      })
    }
    if (this.data.isAdd == false) {
      editlist({
        image: this.data.detail.image,
        name: this.data.detail.name,
        notes: this.data.detail.notes,
        list_id: this.data.list_id
      })
        .then((res) => {
          wx.hideLoading()
          console.log(res, 'res-----------')
          if (res.code == 1) {
            wx.showToast({ title: '编辑成功', icon: 'none' })
            setTimeout(() => {
              wx.navigateBack()
            }, 1500)
          } else {
            wx.showToast({
              title: res.msg,
              icon: 'none'
            })
          }
        })
        .catch((err) => {
          wx.showToast({
            title: err.msg,
            icon: 'none'
          })
        })
      return
    }
    addlist({
      image: this.data.detail.image,
      name: this.data.detail.name,
      notes: this.data.detail.notes,
      starttime: this.data.starttime,
      endtime: this.data.endtime
    })
      .then((res) => {
        wx.hideLoading()
        console.log(res, 'res-----------')
        if (res.code == 1) {
          wx.showToast({ title: '添加成功', icon: 'none' })
          setTimeout(() => {
            wx.navigateBack()
          }, 1500)
        } else {
          wx.showToast({
            title: res.msg,
            icon: 'none'
          })
        }
      })
      .catch((err) => {
        wx.showToast({
          title: err.msg,
          icon: 'none'
        })
      })
  },
  //
  selectYear(e) {
    const year = e.currentTarget.dataset.year
    const { selectedDate } = this.data
    this.setData({
      'selectedDate.year': year,
      showYearSelector: false
    })
    this.updateMonthData(year, selectedDate.month)
  },

  selectDay(e) {
    const selectedDay = e.currentTarget.dataset.day
    if (!selectedDay) return

    const { year, month } = this.data.selectedDate
    this.updateMonthData(year, month, selectedDay) // ✔ 重建日历并标记选中项
    this.setData({
      'selectedDate.day': selectedDay,
      showTimeSelector: true
    })
  },

  prevMonth() {
    let { month, year } = this.data.selectedDate
    if (month === 1) {
      year--
      month = 12
    } else {
      month--
    }
    this.setData({ 'selectedDate.month': month, 'selectedDate.year': year })
    this.updateMonthData(year, month)
  },

  nextMonth() {
    let { month, year } = this.data.selectedDate
    if (month === 12) {
      year++
      month = 1
    } else {
      month++
    }
    this.setData({ 'selectedDate.month': month, 'selectedDate.year': year })
    this.updateMonthData(year, month)
  },

  onSwiperChange(e) {
    const direction = e.detail.current - this.data.swiperCurrent
    let { year, month, day } = this.data.selectedDate

    if (direction === 1) {
      // 向右滑，下一月
      if (month === 12) {
        month = 1
        year++
      } else {
        month++
      }
    } else if (direction === -1) {
      // 向左滑，上一月
      if (month === 1) {
        month = 12
        year--
      } else {
        month--
      }
    } else {
      return // 没变化
    }

    this.updateMonthData(year, month, day)
  },

  onTimeChange(e) {
    this.setData({ timeString: e.detail.value })
  },

  confirmDateTime() {
    const { year, month, day } = this.data.selectedDate
    const fullDate = `${year}-${month.toString().padStart(2, '0')}-${day.toString().padStart(2, '0')}`
    const time = this.data.timeString
    const dateTime = `${fullDate} ${time}`
    // wx.showToast({ title: '选择时间：' + dateTime, icon: 'none' })
    if (this.data.timetype == 1) {
      this.setData({
        starttime: dateTime
      })
    } else {
      this.setData({
        endtime: dateTime
      })
    }
    this.setData({ showPicker: false })
  },

  closePicker() {
    this.setData({ showPicker: false })
  },
  /**
   * 生命周期函数--监听页面初次渲染完成
   */
  onReady() {},

  /**
   * 生命周期函数--监听页面显示
   */
  onShow() {},

  /**
   * 生命周期函数--监听页面隐藏
   */
  onHide() {},

  /**
   * 生命周期函数--监听页面卸载
   */
  onUnload() {},

  /**
   * 页面相关事件处理函数--监听用户下拉动作
   */
  onPullDownRefresh() {},

  /**
   * 页面上拉触底事件的处理函数
   */
  onReachBottom() {},

  /**
   * 用户点击右上角分享
   */
  onShareAppMessage() {}
})
