/* 整体容器 */
.container {
  display: flex;
  flex-direction: column;
  min-height: 100vh;
  background-color: #f5f5f5;
  position: relative;
}

/* 顶部导航 */
.nav-header {
  display: flex;
  align-items: center;
  justify-content: space-between;
  padding: 10px 15px;
  background-color: #ffffff;
  position: sticky;
  top: 0;
  z-index: 100;
  box-shadow: 0 1px 3px rgba(0, 0, 0, 0.05);
}

.nav-back {
  width: 24px;
  height: 24px;
  display: flex;
  align-items: center;
  justify-content: center;
}

.nav-back image {
  width: 20px;
  height: 20px;
}

.nav-title {
  font-size: 16px;
  font-weight: 500;
}

.nav-placeholder {
  width: 24px;
}

/* 内容区域 */
.content {
  flex: 1;
  padding: 10px 0;
}

/* 分区样式 */
.section {
  margin: 10px 15px;
  background-color: #ffffff;
  border-radius: 12px;
  overflow: hidden;
  box-shadow: 0 1px 3px rgba(0, 0, 0, 0.05);
}

.section-title {
  padding: 12px 15px;
  font-size: 14px;
  color: #999999;
  border-bottom: 1px solid #f0f0f0;
}

/* 信息项样式 */
.info-item {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 15px 15px 5px;
}

.info-value-row {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 0 15px 15px 49px; /* 左侧padding与图标对齐 */
  border-bottom: 1px solid #f0f0f0;
}

.info-item:last-of-type {
  border-bottom: none;
}

.info-left {
  display: flex;
  align-items: center;
}

.info-icon {
  width: 24px;
  height: 24px;
  margin-right: 10px;
}

.info-label {
  font-size: 15px;
  color: #333333;
}

.info-right {
  display: flex;
  align-items: center;
}

.info-value {
  font-size: 14px;
  color: #999999;
  margin-right: 10px;
}

.modify-btn {
  font-size: 14px;
  color: #a5ddb9;
}

.arrow-icon {
  width: 16px;
  height: 16px;
}

/* 开关样式 */
.toggle-switch {
  margin-left: 10px;
}

/* 第三方账号绑定项样式 */
.info-item.third-party {
  padding: 15px;
}

/* 注销账号项样式 */
.info-item.delete-account {
  padding: 15px;
}