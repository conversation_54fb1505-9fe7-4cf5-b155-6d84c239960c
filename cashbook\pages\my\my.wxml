<view class="content">
  <!-- 自定义导航栏 -->
  <!-- <view style="height: 300px;display: block;"></view> -->
  <view class="box">
    <view class="scanMolds">
      <view class="scan icon2" bind:tap="scanCode">
        <image src="/static/icon/scan.png" mode="" />
      </view>
      <view class="rightIcons">
        <view class="crown icon2" bind:tap="toVip">
          <image src="/static/icon/crown.png" mode="" />
        </view>
        <view class="set icon2" bind:tap="toSet">
          <image src="/static/icon/set.png" mode="" />
        </view>
      </view>
    </view>

    <!-- 未登录状态 -->
    <view class="userCard" wx:if="{{!isLoggedIn}}">
      <view class="loginCard" bind:tap="toLogin">
        <view class="loginAvatar">
          <image src="/static/icon/default_avatar.png" mode="" />
        </view>
        <view class="loginText">
          <text>登录/注册</text>
        </view>
        <view class="loginArrow">
          <image src="/static/icon/arrow.png" mode="" />
        </view>
      </view>
    </view>

    <!-- 已登录状态 -->
    <view class="userCard" wx:else>
      <view class="cardHeader">
        <text class="cardTitle">账单日{{billDay || '1'}}号</text>
      </view>
      <view class="userInfo">
        <view bind:tap="toUserInfo" class="userAvator">
          <image src="{{userInfo.avatar || '/static/icon/default_avatar.png'}}" mode="aspectFill" />
        </view>
        <view bind:tap="toUserInfo" class="info">
          <view class="userName">Hi {{userInfo.nickname || '用户'}}</view>
          <view>今天是你记账的第{{userInfo.accountDays || '0'}}天啦 😎</view>
        </view>
        <view class="userRightInfo">
          <view class="signIn" bind:tap="signIn">
            <image src="/static/icon/calendar.png" mode="" />
            <text>签到打卡</text>
          </view>
          <view class="vipCenter" bind:tap="toVipCenter">
            <image src="/static/icon/sun.png" mode="" />
            <text>达人中心</text>
          </view>
          <view class="vipTry" bind:tap="toVip">
            <text>{{userInfo.isVip ? 'VIP 会员' : 'VIP 体验会员'}}</text>
          </view>
        </view>
      </view>
      <view class="divider"></view>
      <!-- 活动卡片 -->
      <view class="activityCard">
        <view class="activityLeft">
          <image src="/static/icon/ticket.png" mode="" />
          <text>限时活动立即体验</text>
        </view>
        <view class="hotActivity" bind:tap="toActivity">
          <text>热门活动</text>
          <image src="/static/icon/arrow.png" mode="" />
        </view>
      </view>
    </view>
  </view>

  <!-- 会员中心和帮助中心 -->
  <view class="headerMouds">
    <view class="mygrid" bind:tap="toVip">
      <view class="mygriditem">
        <view class="seting1">会员中心</view>
        <view class="seting2">尊享特别功能</view>
      </view>
      <view class="gridIcon">
        <image src="/static/icon/vip_crown.png" mode="" />
      </view>
    </view>
    <view class="mygrid" bind:tap="toHelp">
      <view class="mygriditem">
        <view class="seting1">帮助中心</view>
        <view class="seting2">疑难解答这里找</view>
      </view>
      <view class="gridIcon">
        <image src="/static/icon/help.png" mode="" />
      </view>
    </view>
  </view>

  <!-- 功能列表 -->
  <view class="cateModls">
    <!-- 常用功能 -->
    <view class="functionSection">
      <view class="sectionTitle">常用功能</view>
      <view class="functionGrid">
        <view class="functionItem" bind:tap="toCategory">
          <view class="itemIcon">
            <image src="/static/icon/category.png" mode="" />
          </view>
          <view class="itemText">收支分类</view>
        </view>
        <view class="functionItem" bind:tap="toAccounts">
          <view class="itemIcon">
            <image src="/static/icon/books.png" mode="" />
          </view>
          <view class="itemText">多账本</view>
        </view>
        <view class="functionItem" bind:tap="toBudget">
          <view class="itemIcon">
            <image src="/static/icon/budget.png" mode="" />
          </view>
          <view class="itemText">预算设置</view>
        </view>
        <view class="functionItem" bind:tap="toSaving">
          <view class="itemIcon">
            <image src="/static/icon/saving.png" mode="" />
          </view>
          <view class="itemText">存钱</view>
        </view>
        <view class="functionItem" bind:tap="togobuylist">
          <view class="itemIcon">
            <image src="/static/icon/shoplist.png" mode="" />
          </view>
          <view class="itemText">购物清单</view>
        </view>
        <view class="functionItem" bind:tap="toTags">
          <view class="itemIcon">
            <image src="/static/icon/tags.png" mode="" />
          </view>
          <view class="itemText">标签</view>
        </view>
      </view>
    </view>
    <!-- 账单/资产 -->
    <view class="functionSection">
      <view class="sectionTitle">账单/资产</view>
      <view class="functionGrid">
        <view class="functionItem" bind:tap="toBillManage">
          <view class="itemIcon">
            <image src="/static/icon/bill_manage.png" mode="" />
          </view>
          <view class="itemText">账单管理</view>
        </view>
        <view class="functionItem" bind:tap="toTimedRecord">
          <view class="itemIcon">
            <image src="/static/icon/timed_record.png" mode="" />
          </view>
          <view class="itemText">定时记账</view>
        </view>
        <view class="functionItem" bind:tap="toBillReport">
          <view class="itemIcon">
            <image src="/static/icon/bill_report.png" mode="" />
          </view>
          <view class="itemText">账单报告</view>
        </view>
        <view class="functionItem" bind:tap="toAssets">
          <view class="itemIcon">
            <image src="/static/icon/assets.png" mode="" />
          </view>
          <view class="itemText">资产</view>
        </view>
      </view>
    </view>
    <!-- 偏好部分 -->
    <view class="functionSection preference">
      <view class="sectionTitle">偏好</view>
      <view class="preferenceGrid">
        <view class="preferenceItem" bindtap="toBookSettings">
          <view class="preferenceIcon">
            <image src="/static/icon/preference.png" mode="aspectFit"></image>
          </view>
          <view class="preferenceText">记账偏好</view>
        </view>
        <view class="preferenceItem" bindtap="toPersonalization">
          <view class="preferenceIcon">
            <image src="/static/icon/personalization.png" mode="aspectFit"></image>
          </view>
          <view class="preferenceText">个性化</view>
        </view>
      </view>
    </view>
    <!-- VIP提示 -->
    <view class="vipTips">
      <view class="vipIcon">
        <image src="/static/icon/crown.png" mode="" />
      </view>
      <view class="vipText">购买定会员送好礼</view>
      <view class="vipCheck">
        <image src="/static/icon/check.png" mode="" />
      </view>
    </view>
    <!-- 其他 -->
    <view class="otherSection">
      <view class="otherTitle">其他</view>
      <view class="helpItem" bind:tap="toHelp">帮助</view>
    </view>
  </view>
  <customBotm current="4"></customBotm>
</view>
