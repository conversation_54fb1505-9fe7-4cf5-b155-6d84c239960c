// packageA/pages/myBooks/myBooks.js
// 导入用户信息API
import { getUserInfo } from '../../../api/user/index';
// 导入获取账本列表API
import { getAccountBookList } from '../../../api/book/index';
const util = require('../../../utils/index.js');

Page({
  data: {
    bookList: [],
    isVip: false, // 默认非会员状态
    showAddBookPopup: false,
    bookName: '',
    bookRemark: '',
    bookCoverImage: ''
  },

  onLoad: function (options) {
    // 检查会员状态
    this.checkVipStatus();
    // 获取账本列表
    this.getBookList();
  },

  // 获取账本列表
  getBookList: function () {
    wx.showLoading({
      title: '加载中...',
    });

    getAccountBookList().then(res => {
      wx.hideLoading();

      if (res && res.code === 1) {
        res.data.forEach(item => {
          item.image = util.getImageUrl(item.image);
        });
        this.setData({
          bookList: res.data || []
        });
      } else {
        wx.showToast({
          title: '获取账本列表失败',
          icon: 'none'
        });
      }
    }).catch(err => {
      wx.hideLoading();
      wx.showToast({
        title: '网络请求失败',
        icon: 'none'
      });
      console.error('获取账本列表失败:', err);
    });
  },

  // 检查会员状态
  checkVipStatus: function () {
    wx.showLoading({
      title: '加载中...',
    });

    // 调用用户信息API
    getUserInfo().then(res => {
      wx.hideLoading();

      if (res && res.code === 1) {
        const userData = res.data;

        // 更新会员状态 - is_vip为"1"表示是会员
        this.setData({
          isVip: userData.is_vip === "1"
        });
      } else {
        wx.showToast({
          title: '获取用户信息失败',
          icon: 'none'
        });
      }
    }).catch(err => {
      wx.hideLoading();
      wx.showToast({
        title: '网络请求失败',
        icon: 'none'
      });
      console.error('获取用户信息失败:', err);
    });
  },

  // 返回上一页
  goBack: function () {
    wx.navigateBack();
  },

  // 选择账本
  selectBook: function (e) {
    const bookId = e.currentTarget.dataset.id;
    // 处理账本选择逻辑
    wx.showToast({
      title: '选择了账本: ' + bookId,
      icon: 'none'
    });
  },

  // 添加新账本
  addNewBook() {
    this.setData({
      showAddBookPopup: true
    });
  },

  closeAddBookPopup() {
    this.setData({
      showAddBookPopup: false
    });
  },

  onAddBookPopupChange(e) {
    if (!e.detail.visible) {
      this.closeAddBookPopup();
    }
  },

  refreshBookCover() {
    // 这里可以随机切换封面图片
    const covers = [
      '/static/images/shanghai.jpg',
      '/static/images/beijing.jpg',
      '/static/images/guangzhou.jpg'
    ];
    const randomIndex = Math.floor(Math.random() * covers.length);
    this.setData({
      bookCoverImage: covers[randomIndex]
    });
  },

  onBookNameInput(e) {
    this.setData({
      bookName: e.detail.value
    });
  },

  onBookRemarkInput(e) {
    this.setData({
      bookRemark: e.detail.value
    });
  },

  saveNewBook() {
    // 保存新账本的逻辑
    if (!this.data.bookName) {
      wx.showToast({
        title: '请输入账本名称',
        icon: 'none'
      });
      return;
    }

    // 这里添加保存账本的逻辑
    // ...

    // 关闭弹窗
    this.closeAddBookPopup();

    // 重置表单
    this.setData({
      bookName: '',
      bookRemark: '',
      bookCoverImage: ''
    });
  },

  // 升级会员
  upgradeToVip: function () {
    // 跳转到会员购买页面
    wx.navigateTo({
      url: '/pages/vip/vip'
    });
  }
});