// packageA/pages/myBooks/myBooks.js
// 导入用户信息API
import { getUserInfo } from '../../../api/user/index';
// 导入获取账本列表APIgetAccountBookList
// 导入添加账本APIaddAccountBook
import { getAccountBookList,addAccountBook } from '../../../api/book/index';
import uploadFile from '../../../api/upload';
const util = require('../../../utils/index.js');
const app = getApp();

Page({
  data: {
    needConfirm: true,
    bookList: [], // 实际账本列表，从API获取
    showBookSelector: false, // 是否显示账本选择器
    demoBook: {
      id: 1,
      name: '默认账本',
      isDefault: true,
      bgColor: '#A5D6B7', // 浅绿色
      image: 'http://www.youcai.com/assets/img/qrcode.png' // 添加默认背景图片
    },
    selectedColor: '',
    isVip: false, // 默认非会员状态
    showAddBookPopup: false,
    showMorePopup: false, // 是否显示更多弹出层
    showCategoryInitPopup: false, // 是否显示分类初始化弹出层
    hideDefaultBook: false, // 是否隐藏默认账本
    bookName: '',
    bookRemark: '',
    bookCoverImage: '',
    bookColors: ['#A5D6B7', '#4DBBEB', '#A4B8B9', '#85C1A9', '#D4A5A5', '#FFD275'], // 可选颜色
    menuButtonInfo: app.globalData.menuButtonInfo,
    inputFocusStates: {
      name: false,
      remark: false
    },
    selectedCategoryId: '', // 选中的分类ID
    selectedCategoryName: '', // 选中的分类名称
    selectedCategoryDesc: '', // 选中的分类描述
    categoryList: [
      { id: 0, name: '不需要分类', desc: '不需要分类，自行添加' },
      { id: 1, name: '默认类型', desc: '账号初始化相同类型分类模板' },
      { id: 2, name: '复制其他账本分类', desc: '选择其他账本分类' },
    ],
    showCoverSelectPopup: false, // 是否显示封面选择弹出层
    coverList: [
      { url: 'http://www.youcai.com/uploads/20250521/33d689db4a567affa3feed6fbf6c1e5f.jpg' },
      { url: 'http://www.youcai.com/uploads/20250521/95558f205788c0f7b982998dacb7c396.png' },
      { url: 'http://www.youcai.com/uploads/20250521/68895a2754bc71645af9e903d75b0755.png' },
      { url: 'http://www.youcai.com/uploads/20250521/c38ad9a5c6fbb9ee0978d036a068394b.png' },
      { url: 'http://www.youcai.com/uploads/20250521/11b8eb8aecb3fb1e59e6dd9cf651f1de.jpg' },
      { url: 'http://www.youcai.com/uploads/20250521/889f24a0c8f5413f8f7bef54f368cc00.jpg' }
    ],
    showErrorTip: false,   // 是否显示错误提示
    categoryValue: '',  // 分类选择器的值
  },

  onLoad: function (options) {
    console.log(app.globalData.menuButtonInfo, 'app.globalData.menuButtonInfo');
    this.setData({
      selectedColor: app.globalData.selectedColor
    })
    // 检查会员状态
    this.checkVipStatus();
  },

  onShow: function() {
    this.checkVipStatus();
    // 获取账本列表 
    this.getBookList();
  },

  // 获取账本列表
  getBookList: function () {
    wx.showLoading({
      title: '加载中...',
    });

    getAccountBookList().then(res => {
      wx.hideLoading();

      if (res && res.code === 1) {
        const bookList = res.data || [];
        
        // 处理数据，为每个账本添加背景色
        bookList.forEach((item, index) => {
          // 设置图片路径
          item.image = item.image ? util.getImageUrl(item.image) : '';
          
          // 如果没有指定背景色，从颜色数组中循环选择
          // if (!item.bgColor) {
          //   item.bgColor = this.data.bookColors[index % this.data.bookColors.length];
          // }
          
          // 设置默认账本标识
          item.isDefault = item.is_default === "1";
        });
        
        this.setData({
          bookList: bookList
        });
      } else {
        // API请求失败，使用示例数据
        console.error('获取账本列表失败:', res);
        // 非会员状态下不提示错误
        if (this.data.isVip) {
        wx.showToast({
          title: '获取账本列表失败',
          icon: 'none'
        });
        }
      }
    }).catch(err => {
      wx.hideLoading();
      // 非会员状态下不提示错误
      if (this.data.isVip) {
      wx.showToast({
        title: '网络请求失败',
        icon: 'none'
      });
      }
      console.error('获取账本列表失败:', err);
    });
  },

  // 检查会员状态
  checkVipStatus: function () {
    wx.showLoading({
      title: '加载中...',
    });

    // 调用用户信息API
    getUserInfo().then(res => {
      wx.hideLoading();

      if (res && res.code === 1) {
        const userData = res.data;

        // 使用terminatetime来判断VIP状态
        // terminatetime是VIP到期时间
        let isVip = false;
        
        if (userData.terminatetime) {
          // 将terminatetime转为时间戳
          const terminateTime = new Date(userData.terminatetime).getTime();
          const currentTime = new Date().getTime();
          
          // 如果终止时间大于当前时间，用户仍是VIP
          isVip = terminateTime > currentTime;
        }
        
        this.setData({
          isVip: isVip
        });
        
        console.log("VIP状态:", isVip, "到期时间:", userData.terminatetime);
      } else {
        wx.showToast({
          title: '获取用户信息失败',
          icon: 'none'
        });
      }
    }).catch(err => {
      wx.hideLoading();
      wx.showToast({
        title: '网络请求失败',
        icon: 'none'
      });
      console.error('获取用户信息失败:', err);
    });
  },

  // 显示会员提示
  showVipTip: function() {
    wx.showToast({
      title: '请先升级会员',
      icon: 'none'
    });
  },

  // 返回上一页
  goBack: function () {
    wx.navigateBack();
  },

  // 显示更多选项菜单
  showMoreOptions: function() {
    // 非会员状态下提示升级
    // if (!this.data.isVip) {
    //   this.showVipTip();
    //   return;
    // }

    this.setData({
      showMorePopup: true
    });
  },

  // 分类初始化
  initCategory: function() {
    this.setData({
      showCategoryInitPopup: true
    });
  },

  // 选择分类
  selectCategory: function(e) {
    const categoryId = e.currentTarget.dataset.id;
    const selectedCategory = this.data.categoryList.find(item => item.id === categoryId);
    // 根据ID确定不同的值
    if (categoryId === 0) {
      this.setData({
        categoryValue: 0 // 不添加分类
      });
    } else if (categoryId === 1) {
      this.setData({
        categoryValue: 'default' // 添加默认分类
      });
    } else {
      this.setData({
        showBookSelector: true
      })
    }
    
    if (selectedCategory) {
      this.setData({
        selectedCategoryId: categoryId,
        selectedCategoryName: selectedCategory.name,
        selectedCategoryDesc: selectedCategory.desc,
        showCategoryInitPopup: false
      });
    }
  },
  onBookSelect(e) {
    console.log(111);
    
    const bookId = e.detail.bookId;
    this.setData({
      selectedCategoryId: bookId
    });
  },
  // 关闭账本选择器
  closeBookSelector() {
    this.setData({
      showBookSelector: false
    });
  },

  // 关闭更多弹出层
  onMorePopupClose: function() {
    this.setData({
      showMorePopup: false
    });
  },

  // 账本排序
  handleBookSort: function() {
    this.onMorePopupClose();
    wx.navigateTo({
      url: '/packageA/pages/bookSort/bookSort'
    });
  },

  // 封存账本列表
  handleArchivedBooks: function() {
    this.onMorePopupClose();
    wx.navigateTo({
      url: '/packageA/pages/sequesterBook/sequesterBook'
    });
  },

  // 默认账本隐藏开关切换
  onHideDefaultBookChange: function(e) {
    const value = e.detail;
    this.setData({
      hideDefaultBook: value
    });
    
    // 保存设置到本地存储
    wx.setStorageSync('hideDefaultBook', value);
    
    wx.showToast({
      title: value ? '默认账本已隐藏' : '默认账本已显示',
      icon: 'none'
    });
  },

  // 常见问题
  handleFAQ: function() {
    this.onMorePopupClose();
    wx.navigateTo({
      url: '/packageA/pages/bookFAQ/bookFAQ'
    });
  },

  // 选择账本 - 修改为跳转到账本详情页
  selectBook: function (e) {
    const bookId = e.currentTarget.dataset.id;
    
    // 导航到账本详情页，并传递账本ID
    wx.navigateTo({
      url: '/packageA/pages/bookDetail/bookDetail?id=' + bookId
    });
  },

  // 添加新账本
  addNewBook() {
    this.setData({
      showAddBookPopup: true,
      selectedCategoryName: '',
      selectedCategoryDesc: '',
      selectedCategoryId: 0,
      // 随机选择一个背景颜色
      // bookColor: this.data.bookColors[Math.floor(Math.random() * this.data.bookColors.length)]
    });
  },

  closeAddBookPopup() {
    this.setData({
      showAddBookPopup: false,
      selectedCategoryName: '',
      selectedCategoryDesc: '',
      selectedCategoryId: 0
    });
  },

  closeCategoryInitPopup() {
    this.setData({
      showCategoryInitPopup: false
    });
  },

  refreshBookCover() {
    // 显示封面选择弹窗
    this.setData({
      showCoverSelectPopup: true
    });
  },

  closeCoverSelectPopup() {
    this.setData({
      showCoverSelectPopup: false
    });
  },

  // 选择封面
  selectCover(e) {
    const imageUrl = e.currentTarget.dataset.url;
    this.setData({
      bookCoverImage: imageUrl,
      showCoverSelectPopup: false
    });
    
    wx.showToast({
      title: '已选择封面',
      icon: 'success',
      duration: 1500
    });
  },

  // 上传自定义封面
  uploadCustomCover() {
    wx.chooseImage({
      count: 1,
      sizeType: ['compressed'],
      sourceType: ['album', 'camera'],
      success: (res) => {
        // 获取选中的图片临时路径
        const tempFilePath = res.tempFilePaths[0];
        // 上传图片到服务器
        this.uploadImage(tempFilePath);
      }
    });
  },

  // 上传图片到服务器
  uploadImage(filePath) {
    wx.showLoading({
      title: '上传中...',
    });
    
    // 调用上传API
    uploadFile({
      url: 'common/upload', // 指定图片上传接口路径
      filePath: filePath,
      name: 'file',
      formData: {
        'type': 'book_cover' // 指定图片类型为账本封面
      }
    }).then(res => {
      wx.hideLoading();
      
      if (res.code === 1 && res.data && res.data.url) {
        // 处理返回的图片URL
        const imageUrl = util.getImageUrl(res.data.url);
        
        this.setData({
          bookCoverImage: imageUrl,
          showCoverSelectPopup: false
        });
        
        wx.showToast({
          title: '上传成功',
          icon: 'success',
          duration: 1500
        });
      } else {
        wx.showToast({
          title: res.msg || '上传失败',
          icon: 'none'
        });
      }
    }).catch(err => {
      wx.hideLoading();
      wx.showToast({
        title: '上传失败',
        icon: 'none'
      });
      console.error('图片上传失败:', err);
    });
  },

  onBookNameInput(e) {
    this.setData({
      bookName: e.detail.value
    });
  },

  onBookRemarkInput(e) {
    this.setData({
      bookRemark: e.detail.value
    });
  },

  // 处理输入框获取焦点
  onInputFocus(e) {
    const field = e.currentTarget.dataset.field;
    const focusStates = { ...this.data.inputFocusStates };
    focusStates[field] = true;
    
    this.setData({
      inputFocusStates: focusStates
    });
  },
  
  // 处理输入框失去焦点
  onInputBlur(e) {
    const field = e.currentTarget.dataset.field;
    const focusStates = { ...this.data.inputFocusStates };
    focusStates[field] = false;
    
    this.setData({
      inputFocusStates: focusStates
    });
  },

  validateAndSaveNewBook: function() {
    // 检查是否选择了分类（selectedCategoryId 或 categoryValue 任一存在即可）
    if (!this.data.selectedCategoryId && this.data.categoryValue === undefined) {
      // 显示错误提示
      this.setData({
        showErrorTip: true
      });
      
      // 3秒后自动隐藏错误提示
      setTimeout(() => {
        this.setData({
          showErrorTip: false
        });
      }, 3000);
      
      return;
    }
    
    // 通过验证，调用原保存方法
    this.saveNewBook();
  },

  closeErrorTip: function() {
    this.setData({
      showErrorTip: false
    });
  },

  saveNewBook() {
    // 保存新账本的逻辑
    if (!this.data.bookName) {
      wx.showToast({
        title: '请输入账本名称',
        icon: 'none'
      });
      return;
    }

    wx.showLoading({
      title: '保存中...',
    });

    // 获取当前显示的封面图片URL
    const imageUrl = this.data.bookCoverImage || 'http://www.youcai.com/uploads/********/289b4a0c8b7de41b385f5dc285f174f2.jpg';

    // 构建请求数据
    const bookData = {
      name: this.data.bookName,
      notes: this.data.bookRemark,
      image: imageUrl
    };
    
    // 如果有categoryValue值，优先使用它
    if (this.data.categoryValue !== undefined) {
      bookData.accountbook_id = this.data.categoryValue;
    }
    // 否则如果选择了分类，添加分类ID
    else if (this.data.selectedCategoryId) {
      bookData.accountbook_id = this.data.selectedCategoryId;
    }

    console.log('添加账本参数:', bookData); // 调试用

    // 调用API添加账本
    addAccountBook(bookData).then(res => {
      wx.hideLoading();
      
      if (res && res.code === 1) {
        wx.showToast({
          title: '添加成功',
          icon: 'success',
          duration: 1500
        });
        
        // 关闭弹窗
        this.closeAddBookPopup();
        
        // 重置表单
        this.setData({
          bookName: '',
          bookRemark: '',
          bookCoverImage: '',
          selectedCategoryId: '',
          selectedCategoryName: '',
          selectedCategoryDesc: ''
        });
        
        // 刷新账本列表
        this.getBookList();
      } else {
        wx.showToast({
          title: res.msg || '添加失败',
          icon: 'none'
        });
      }
    }).catch(err => {
      wx.hideLoading();
      wx.showToast({
        title: '网络请求失败',
        icon: 'none'
      });
      console.error('添加账本失败:', err);
    });
  },

  // 升级会员
  upgradeToVip: function () {
    // 跳转到会员购买页面
    wx.navigateTo({
      url: '/pages/vip/vip'
    });
  },
  onBackPress() {
    // 返回 true 表示阻止默认返回行为
    return true;
  },
});