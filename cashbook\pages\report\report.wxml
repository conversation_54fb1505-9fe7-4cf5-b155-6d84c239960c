<view class="container">
  <!-- 顶部导航栏 -->
  <view class="nav-bar">
    <view class="tab-container">
      <view class="tab active">报表</view>
      <view class="tab">日历</view>
    </view>
    <view class="more-icon">
      <image src="/static/icon/more.png" mode="aspectFit" />
    </view>
  </view>
  <!-- 日期选择器 -->
  <view class="date-selector">
    <view class="date" bindtap="openDatePicker">
      <text>{{selectedYear}}年{{selectedMonth}}月</text>
      <image src="/static/icon/arrDown.png" mode="aspectFit" />
    </view>
    <view class="filter">筛选</view>
  </view>
  <!-- 收支统计卡片 -->
  <view class="stats-card">
    <view class="stats-tabs">
      <view class="stats-tab" bindtap="switchTab" data-tab="expense">
        <view class="radio-circle {{activeTab === 'expense' ? 'active' : ''}}"></view>
        <view class="tab-content">
          <text class="tab-label">月支出</text>
          <text class="amount expense">¥820.00</text>
        </view>
      </view>
      <view class="stats-tab" bindtap="switchTab" data-tab="income">
        <view class="radio-circle {{activeTab === 'income' ? 'active' : ''}}"></view>
        <view class="tab-content">
          <text class="tab-label">月收入</text>
          <text class="amount income">¥1,000.00</text>
        </view>
      </view>
      <view class="stats-tab" bindtap="switchTab" data-tab="other">
        <view class="radio-circle {{activeTab === 'other' ? 'active' : ''}}"></view>
        <view class="tab-content">
          <text class="tab-label">其他</text>
          <text class="amount other">¥8,860.00</text>
        </view>
      </view>
    </view>
    <!-- 图表容器 -->
    <view class="chart-container">
      <!-- 环形图 -->
      <view class="pie-chart" wx:if="{{chartType === 'ring'}}">
        <!-- uCharts环形图组件 -->
        <qiun-wx-ucharts type="ring" chartData="{{pieChartData}}" opts="{{pieOpts}}" bindtap="refreshChart" canvas2d="{{true}}" canvasId="ringChart" />
      </view>
      <!-- 面积图(柱状图) -->
      <view class="treemap-chart" wx:if="{{chartType === 'area'}}">
        <qiun-wx-ucharts type="column" chartData="{{treemapData}}" opts="{{treemapOpts}}" canvas2d="{{true}}" canvasId="columnChart" />
      </view>
    </view>
    <!-- 图表类型切换 -->
    <view class="chart-type-switch">
      <view class="switch-left">
        <view class="switch-category" bindtap="toggleCategorySize">
          <image src="/static/icon/switch.png" mode="aspectFit" class="switch-icon"></image>
          <text>切换小类</text>
        </view>
      </view>
      <view class="switch-right">
        <view class="chart-type-toggle">
          <view class="toggle-option {{chartType === 'ring' ? 'active' : ''}}" bindtap="setChartType" data-type="ring">
            环形
          </view>
          <view class="toggle-option {{chartType === 'area' ? 'active' : ''}}" bindtap="setChartType" data-type="area">
            面积
          </view>
        </view>
      </view>
    </view>
  </view>
  <!-- 支出类别列表 -->
  <view class="category-list">
    <view class="category-item">
      <view class="category-icon">
        <image src="/static/icon/shopping.png" mode="aspectFit" />
      </view>
      <view class="category-info">
        <text class="category-name">购物 92.25%</text>
        <view class="progress-bar">
          <view class="progress" style="width: 92.25%;"></view>
        </view>
      </view>
      <view class="category-amount">
        <text class="amount">¥2,500.00</text>
        <text class="count">1笔</text>
      </view>
    </view>
    <view class="category-item">
      <view class="category-icon">
        <image src="/static/icon/food.png" mode="aspectFit" />
      </view>
      <view class="category-info">
        <text class="category-name">餐饮 7.75%</text>
        <view class="progress-bar">
          <view class="progress" style="width: 7.75%; background-color: #ff9800;"></view>
        </view>
      </view>
      <view class="category-amount">
        <text class="amount">¥210.00</text>
        <text class="count">2笔</text>
      </view>
    </view>
  </view>
  <!-- 底部导航栏 -->
  <customBotm current="3"></customBotm>
</view>
<!-- 年月选择弹窗 -->
<view class="date-picker-popup" wx:if="{{showDatePicker}}">
  <view class="popup-mask" bindtap="closeDatePicker"></view>
  <view class="popup-content">
    <view class="popup-header">
      <text>{{selectedYear}}年{{selectedMonth}}月</text>
      <view class="tab-container">
        <view class="tab {{datePickerTab === 'week' ? 'active' : ''}}" bindtap="switchDatePickerTab" data-tab="week">
          周账单
        </view>
        <view class="tab {{datePickerTab === 'month' ? 'active' : ''}}" bindtap="switchDatePickerTab" data-tab="month">
          月账单
        </view>
        <view class="tab {{datePickerTab === 'year' ? 'active' : ''}}" bindtap="switchDatePickerTab" data-tab="year">
          年账单
        </view>
        <view class="tab {{datePickerTab === 'custom' ? 'active' : ''}}" bindtap="switchDatePickerTab" data-tab="custom">
          自定义
        </view>
      </view>
    </view>
    <view class="year-selector">
      <text>{{selectedYear}}年</text>
      <view class="year-arrows">
        <view class="arrow up" bindtap="changeYear" data-direction="up">
          <image src="/static/icon/arrow-up.png" mode="aspectFit" />
        </view>
        <view class="arrow down" bindtap="changeYear" data-direction="down">
          <image src="/static/icon/arrDown.png" mode="aspectFit" />
        </view>
      </view>
    </view>
    <view class="month-grid">
      <view class="month-row">
        <view class="month-item {{selectedMonth === 1 ? 'active' : ''}}" bindtap="selectMonth" data-month="1">
          1月
        </view>
        <view class="month-item {{selectedMonth === 2 ? 'active' : ''}}" bindtap="selectMonth" data-month="2">
          2月
        </view>
        <view class="month-item {{selectedMonth === 3 ? 'active' : ''}}" bindtap="selectMonth" data-month="3">
          3月
        </view>
        <view class="month-item {{selectedMonth === 4 ? 'active' : ''}}" bindtap="selectMonth" data-month="4">
          4月
        </view>
      </view>
      <view class="month-row">
        <view class="month-item {{selectedMonth === 5 ? 'active' : ''}}" bindtap="selectMonth" data-month="5">
          5月
        </view>
        <view class="month-item {{selectedMonth === 6 ? 'active' : ''}}" bindtap="selectMonth" data-month="6">
          6月
        </view>
        <view class="month-item {{selectedMonth === 7 ? 'active' : ''}}" bindtap="selectMonth" data-month="7">
          7月
        </view>
        <view class="month-item {{selectedMonth === 8 ? 'active' : ''}}" bindtap="selectMonth" data-month="8">
          8月
        </view>
      </view>
      <view class="month-row">
        <view class="month-item {{selectedMonth === 9 ? 'active' : ''}}" bindtap="selectMonth" data-month="9">
          9月
        </view>
        <view class="month-item {{selectedMonth === 10 ? 'active' : ''}}" bindtap="selectMonth" data-month="10">
          10月
        </view>
        <view class="month-item {{selectedMonth === 11 ? 'active' : ''}}" bindtap="selectMonth" data-month="11">
          11月
        </view>
        <view class="month-item {{selectedMonth === 12 ? 'active' : ''}}" bindtap="selectMonth" data-month="12">
          12月
        </view>
      </view>
    </view>
    <view class="popup-footer">
      <view class="btn cancel" bindtap="closeDatePicker">取消</view>
      <view class="btn confirm" bindtap="confirmDatePicker">确定</view>
    </view>
  </view>
</view>