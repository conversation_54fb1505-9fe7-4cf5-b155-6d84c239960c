<view class="content" >
  <view style="height: 200px;display: block;"></view>
  <!-- 自定义导航栏 {{mySclace}} {{chagePosition}}-->
  <view class="box " style="border: 1px solid;background-color: {{selectedColor}};">
    <view class="title">
      小猫记账
      <view class="pic">
        <view bind:tap="navtoDetail">
          <image src="/static/icon/card.png" mode="" />
        </view>
        <view bind:tap="showVip">
          <image src="/static/icon/crown.png" mode="" />
        </view>
      </view>
    </view>
    <view class="navPic" style="transform: scale(.5);">
      <image src="/static/header.png" mode="" />
    </view>
  </view>


<!-- 内容区域 -->
<view  style="border: 1px solid;">
  <!-- 卡片 -->
  <view class="card2" >
    <swiper current="{{swpTabCurrent}}" style="height: 180px;">
      <swiper-item>
        <!-- 第一列 -->
        <view class="colonmOne">
          <view class="COleft">本月支出(元)</view>
          <view bind:tap="tabswpAdd" class="zc">资产</view>
        </view>
        <!-- 查看收入 -->
        <view class="colonmTwo">
          <view class="cardprice" wx:if="{{showAccount}}">￥995</view>
          <view class="cardprice" wx:else="">￥***</view>
          <view class="showAcount">
            <view bind:tap="toggle" class="show" wx:if="{{showAccount}}">
              <image src="/static/icon/show.png" mode="" />
            </view>
            <view bind:tap="toggle" class="hide" wx:else="">
              <image src="/static/icon/hide.png" mode="" />
            </view>
          </view>
        </view>
        <!-- 第二列 -->
        <view class="colonmTwo three">
          <view>本月收入 <span style="color: #000;">0.00</span> </view>
          <view>月结余 <span style="color: #000;">-652</span> </view>
        </view>
        <!-- 录入 -->
        <view class="regist" bind:tap="setRemark">
          记一笔
        </view>
      </swiper-item>
      <swiper-item>
        <!-- 第一列 -->
        <view class="colonmOne">
          <view class="COleft">本月支出(元)</view>
          <view bind:tap="tabswpAdd2" class="zc">资产</view>
        </view>
        <!-- 查看收入 -->
        <view class="colonmTwo">
          <view class="cardprice" wx:if="{{showAccount}}">￥995</view>
          <view class="cardprice" wx:else="">￥***</view>
          <view class="showAcount">
            <view bind:tap="toggle" class="show" wx:if="{{showAccount}}">
              <image src="/static/icon/show.png" mode="" />
            </view>
            <view bind:tap="toggle" class="hide" wx:else="">
              <image src="/static/icon/hide.png" mode="" />
            </view>
          </view>
        </view>
        <!-- 第二列 -->
        <view class="colonmTwo three">
          <view>本月收入 <span style="color: #000;">0.00</span> </view>
          <view>月结余 <span style="color: #000;">-652</span> </view>
        </view>
        <!-- 录入 -->
        <view class="regist" bind:tap="setRemark">
          记一笔
        </view>
      </swiper-item>

    </swiper>

  </view>
  <!-- 本月账单&&排序 -->
  <view class="oneColonm" >
    <view>本月账单</view>
    <view class="time" bind:tap="sort1" wx:if="{{swpCurrent==0}}">按时间</view>
    <view class="time" bind:tap="sort2" wx:else="">按金额</view>
  </view>

  <swiper current="{{swpCurrent}}" style="height: {{swperHeight}}px;">
    <swiper-item>
      <view id="box1">
        <contentList list="{{list}}" />
        <contentList list="{{list}}" />
        <contentList list="{{list}}" />
        <contentList list="{{list}}" /> 
        <contentList list="{{list}}" />
        <contentList list="{{list}}" />
        <contentList list="{{list}}" /> 
        <contentList list="{{list}}" />
        <contentList list="{{list}}" />
        <contentList list="{{list}}" />
      </view>
    </swiper-item>
    <swiper-item>
      <view id="box2">
        <contentList list="{{list}}" />
        <!-- <contentList list="{{list}}" />
        <contentList list="{{list}}" /> -->
      </view>
    </swiper-item>
  </swiper>
</view>

  <!-- 隐藏 -->
  <view wx:if="{{false}}">
    <contentList time="本月账单" list="{{list}}" />
    <contentList time="本月账单" list="{{list}}" />
    <contentList time="本月账单" list="{{list}}" />
  </view>
  <vipdialog isShow="{{vipDialog}}" wx:if="{{vipDialog}}"></vipdialog>
</view><view style="height: 70px;"></view>
<customBotm bind:showMask="getMask" current="{{1}}" bind:getCateId="getcateID"></customBotm>
