<view class="content">
  <view style="height: 200px;display: block;"></view>
  <!-- 自定义导航栏 {{mySclace}} {{chagePosition}}-->
  <view class="box " style="background-color: {{selectedColor}};">
    <view class="title">
      <view class="cashbook-section" bind:tap="switchCashbook">
        <view class="cashbook-icon">
          <image src="/static/icon/card.png" mode="aspectFit" />
        </view>
        <view class="cashbook-info">
          <!-- If only one cashbook, show its name, otherwise show the count -->
          <text wx:if="{{cashbooks.length === 1}}">{{cashbooks[0].name || '默认账本'}}</text>
          <text wx:else>{{cashbooks.length}}个账本</text>
        </view>
      </view>
      <view class="pic">
        <view bind:tap="navtoDetail">
          <image src="/static/icon/card.png" mode="" />
        </view>
        <view bind:tap="showVip">
          <image src="/static/icon/crown.png" mode="" />
        </view>
      </view>
    </view>
    <view class="navPic" style="transform: scale(.5);">
      <image src="/static/header.png" mode="" />
    </view>
  </view>

  <!-- 内容区域 -->
  <scroll-view scroll-y class="scroll-container" bounces="{{false}}" enhanced="{{true}}" show-scrollbar="{{false}}" bind:scroll="onScroll">
    <!-- 卡片 -->
    <view class="card2">
      <swiper current="{{swpTabCurrent}}" style="height: 180px;" bindchange="onSwiperChange">
        <swiper-item>
          <!-- 第一列 -->
          <view class="colonmOne">
            <view class="COleft">{{activeTab === 'bill' ? '本月支出(元)' : '净资产(元)'}}</view>
            <view bind:tap="toggleActiveTab" class="zc">{{activeTab === 'bill' ? '资产' : '账单'}}</view>
          </view>
          <!-- 查看收入 -->
          <view class="colonmTwo">
            <view class="cardprice" wx:if="{{showAccount}}">￥820.00</view>
            <view class="cardprice" wx:else="">￥***</view>
            <view class="showAcount">
              <view bind:tap="toggle" class="show" wx:if="{{showAccount}}">
                <image src="/static/icon/show.png" mode="" />
              </view>
              <view bind:tap="toggle" class="hide" wx:else="">
                <image src="/static/icon/hide.png" mode="" />
              </view>
            </view>
          </view>
          <!-- 第二列 -->
          <view class="colonmTwo three">
            <block wx:if="{{activeTab === 'bill'}}">
              <view>本月收入 <span style="color: #000;">1,000.00</span> </view>
              <view>月结余 <span style="color: #000;">180.00</span> </view>
            </block>
            <block wx:else>
              <view>总资产 <span style="color: #000;">1,280.00</span> </view>
              <view>总负债 <span style="color: #000;">6,270.00</span> </view>
            </block>
          </view>
          <!-- 录入 -->
          <view class="regist" bind:tap="{{activeTab === 'bill' ? 'setRemark' : 'onAddAsset'}}">
            {{activeTab === 'bill' ? '记一笔' : '添加资产'}}
          </view>
        </swiper-item>
        <swiper-item>
          <!-- 第一列 -->
          <view class="colonmOne">
            <view class="COleft">净资产(元)</view>
            <view bind:tap="toggleActiveTab" class="zc">账单</view>
          </view>
          <!-- 查看收入 -->
          <view class="colonmTwo">
            <view class="cardprice" wx:if="{{showAccount}}">￥{{assetStats.netAssets}}</view>
            <view class="cardprice" wx:else="">￥***</view>
            <view class="showAcount">
              <view bind:tap="toggle" class="show" wx:if="{{showAccount}}">
                <image src="/static/icon/show.png" mode="" />
              </view>
              <view bind:tap="toggle" class="hide" wx:else="">
                <image src="/static/icon/hide.png" mode="" />
              </view>
            </view>
          </view>
          <!-- 第二列 -->
          <view class="colonmTwo three">
            <view>总资产 <span style="color: #000;">{{assetStats.totalAssets}}</span> </view>
            <view>总负债 <span style="color: #000;">{{assetStats.totalLiabilities}}</span> </view>
          </view>
          <!-- 录入 -->
          <view class="regist" bind:tap="onAddAsset">
            添加资产
          </view>
        </swiper-item>
      </swiper>
    </view>

    <!-- 根据activeTab显示不同内容 -->
    <block wx:if="{{activeTab === 'bill'}}">
      <!-- 本月账单&&排序 -->
      <view class="oneColonm">
        <view>近3日账单</view>
        <view class="time" bind:tap="toggleSort">{{sortType === 'amount' ? '按金额' : '按时间'}}</view>
      </view>

      <!-- 账单内容区域 -->
      <view class="bill-container">
        <contentList list="{{list}}" sortType="{{sortType}}" />
      </view>
    </block>

    <block wx:else>
      <!-- 资产列表 -->
      <asset-list
        show-amount="{{showAccount}}"
        asset-list="{{assetList}}"
        theme-color="{{selectedColor}}"
        bind:assetclick="onAssetItemClick"
        bind:addasset="onAddAsset"
        bind:setting="onAssetSetting"
        bind:hideasset="onHideAsset"
        bind:editasset="onEditAsset"
        bind:deleteasset="onDeleteAsset"
      ></asset-list>

      <!-- 加载中提示 -->
      <!-- <view class="loading-container" wx:if="{{loadingAssets}}">
        <view class="loading-spinner"></view>
        <view class="loading-text">加载中...</view>
      </view> -->
    </block>
  </scroll-view>

  <vipdialog isShow="{{vipDialog}}" wx:if="{{vipDialog}}"></vipdialog>
</view>

<!-- 调整底部导航的显示 -->
<view class="bottom-nav-spacer"></view>
<customBotm bind:showMask="getMask" current="{{1}}" bind:getCateId="getcateID"></customBotm>
