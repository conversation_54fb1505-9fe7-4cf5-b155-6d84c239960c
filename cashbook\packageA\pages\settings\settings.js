// packageA/pages/settings/settings.js
Page({
  /**
   * 页面的初始数据
   */
  data: {
    
  },

  /**
   * 生命周期函数--监听页面加载
   */
  onLoad: function (options) {
    
  },

  /**
   * 返回上一页
   */
  goBack: function() {
    wx.navigateBack({
      delta: 1
    });
  },

  /**
   * 导航到指定页面
   */
  navigateTo: function(e) {
    
    
    const url = e.currentTarget.dataset.url;
    wx.navigateTo({
      url: url
    });
  },

  /**
   * 检测网络状态
   */
  checkNetwork: function() {
    wx.getNetworkType({
      success: function(res) {
        const networkType = res.networkType;
        wx.showToast({
          title: '当前网络: ' + networkType,
          icon: 'none',
          duration: 2000
        });
      }
    });
  },

  /**
   * 退出登录
   */
  logout: function() {
    wx.showModal({
      title: '提示',
      content: '确定要退出登录吗？',
      success: function(res) {
        if (res.confirm) {
          // 清除登录状态
          wx.removeStorageSync('token');
          wx.removeStorageSync('userInfo');
          
          // 返回首页
          wx.reLaunch({
            url: '/pages/index2/index2'
          });
        }
      }
    });
  }
})