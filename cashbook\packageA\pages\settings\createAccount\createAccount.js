// 新建账户页面逻辑
const app = getApp()
const util = require('../../../../utils/index.js')

// 引入API
import { getAccounts, addAccount, getBankList, getUserAccountDetail, updateAccount, getAccountGroups } from '../../../../api/account/index'

Page({
  data: {
    // 系统信息
    statusBarHeight: wx.getSystemInfoSync().statusBarHeight,
    navHeight: 44,
    // 表单数据
    accountName: '',
    accountBalance: '',
    // 资产类型
    assetType: {
      id: 3,
      name: '支付宝',
      icon: '/assets/img/qrcode.png',
      type: 'capital',
      category: 'fund',
      type_text: '资产',
      category_text: '资金'
    },
    // 开关状态
    syncBalance: true,
    hideAsset: false,
    includeInTotal: true,
    // 自定义分组
    customGroup: '',
    isCustomGroupFocused: false, // 分组名称输入框是否聚焦
    // 自定义分组列表
    groupList: [], // 将默认值改为空数组，通过API获取
    customGroupPopupVisible: false, // 自定义分组弹窗可见性
    // 可选资产类型列表
    assetTypes: [
      { id: 'cash', name: '现金账户', icon: '/assets/icons/cash.png' },
      { id: 'alipay', name: '支付宝', icon: '/assets/icons/alipay.png' },
      { id: 'wechat', name: '微信钱包', icon: '/assets/icons/wechat.png' },
      { id: 'bank', name: '银行卡', icon: '/assets/icons/bank.png' },
      { id: 'creditcard', name: '信用卡', icon: '/assets/icons/creditcard.png' },
      { id: 'other', name: '其他', icon: '/assets/icons/other.png' }
    ],
    // 主题颜色
    themeColor: '#20cc52',
    // 资产类型选择弹窗
    assetTypePopupVisible: false,
    assetTypeList: {},
    activeTab: '资金', // 当前激活的标签
    tabs: [], // 标签列表

    // 信用卡额外字段
    selectedBank: '',
    remark: '',
    cardNumber: '',
    creditLimit: '',
    currentDebt: '',
    remainingLimit: '',
    statementDay: '每月1日',
    repaymentDay: '每月10日',
    isCreditCard: false, // 是否为信用卡
    isDebitCard: false, // 是否为借记卡

    // 借记卡额外字段
    selectedBank: '',
    remark: '',
    cardNumber: '',
    accountBalance: '',

    // 新增字段
    isReimbursement: false,
    accountCategory: '',
    isLendOut: false,
    isBorrowIn: false, // 是否为借入

    // 报销特有字段
    reimbursementPerson: '',
    reimbursementStatus: 'pending', // pending=待报销, completed=已报销

    // 借出特有字段
    borrower: '',
    loanDate: '',
    repayDate: '',

    // 借入特有字段
    lender: '', // 向谁借
    borrowAmount: '', // 借入金额
    borrowDate: '', // 借入日期
    repaymentDate: '', // 还款日期

    // 银行选择弹窗相关
    bankPopupVisible: false,
    bankSearchKeyword: '',
    bankList: [],
    originalBankList: [],
    topBanks: [], // 热门银行列表
    hasTopBanks: false, // 是否有热门银行
    letters: ['A', 'B', 'C', 'D', 'E', 'F', 'G', 'H', 'I', 'J', 'K', 'L', 'M', 'N', 'O', 'P', 'Q', 'R', 'S', 'T', 'U', 'V', 'W', 'X', 'Y', 'Z'],
    currentLetter: '',
    scrollIntoView: '',
    // 自定义图片弹窗相关
    showCustomPopup: false,
    customImageUrl: '',
    customImageName: '',
    bankIcon: '',

    // 编辑模式相关
    isEditMode: false,
    accountId: '',
    pageTitle: '新建账户'
  },

  onLoad: function (options) {
    // 获取系统信息
    const systemInfo = wx.getSystemInfoSync()
    this.setData({
      statusBarHeight: systemInfo.statusBarHeight,
      navHeight: 44
    })

    // 设置页面类型和标题
    let pageTitle = '新建账户'
    let accountType = ''

    // 检查是否为编辑模式
    const isEditMode = options && options.id && options.edit === 'true'

    if (isEditMode) {
      pageTitle = '编辑账户'
      this.setData({
        isEditMode: true,
        accountId: options.id,
        pageTitle: pageTitle
      })

      // 加载账户详情
      this.loadAccountDetail(options.id)
    }

    if (options) {
      // 处理不同类型的账户创建
      if (options.type) {
        accountType = options.type

        // 设置资产类型
        const assetType = this.data.assetTypes.find((item) => item.id === options.type)
        if (assetType) {
          this.setData({ assetType })
        }
      }

      // 处理特殊账户类型
      if (options.accountCategory) {
        switch (options.accountCategory) {
          case 'reimbursement': // 报销
            pageTitle = isEditMode ? '编辑报销' : '新建报销'
            this.setData({
              isReimbursement: true,
              accountCategory: 'reimbursement',
              pageTitle: pageTitle
            })
            break
          case 'lendOut': // 借出
            pageTitle = isEditMode ? '编辑借出' : '新建借出'
            this.setData({
              isLendOut: true,
              accountCategory: 'lendOut',
              pageTitle: pageTitle
            })
            break
          case 'borrowIn': // 借入
            pageTitle = isEditMode ? '编辑借入' : '新建借入'
            this.setData({
              isBorrowIn: true,
              accountCategory: 'borrowIn',
              pageTitle: pageTitle
            })
            break
          default:
            break
        }
      }
    }

    // 获取主题颜色
    this.getThemeColor()
    // 获取资产类型列表
    this.getAssetTypeList()
    // 获取自定义分组列表
    this.getCustomGroups()
  },

  onShow: function () {
    // 页面显示时更新主题颜色，确保从其他页面返回时颜色是最新的
    this.getThemeColor()

    // 检查是否有从银行选择器页面返回的数据
    const pages = getCurrentPages()
    const currentPage = pages[pages.length - 1]

    // 如果有selectedBank数据，说明是从银行选择器页面返回的
    if (currentPage.data.selectedBank && typeof currentPage.data.selectedBank === 'object') {
      const bank = currentPage.data.selectedBank

      // 设置选中的银行数据
      this.setData({
        selectedBank: bank.name,
        bankIcon: bank.icon || '/assets/icons/bank.png'
      })

      // 清除临时数据，防止重复处理
      currentPage.setData({
        selectedBank: null
      })
    }
  },

  // 获取主题颜色
  getThemeColor: function () {
    // 获取主题颜色类型
    const themeColorType = wx.getStorageSync('themeColorType') || 'preset'
    let themeColor

    // 直接从存储获取颜色值
    // themeColor = wx.getStorageSync('themeColorValue') || '#4cd964';

    if (app.globalData && app.globalData.selectedColor) {
      themeColor = app.globalData.selectedColor
    }

    this.setData({
      themeColor: themeColor
    })
  },

  // 更新开关组件的颜色
  updateSwitchColor: function (color) {
    if (!color) return

    // 通过css变量或其他方式设置开关颜色
    // 这里可以根据小程序UI库的不同进行不同的处理
    // 例如，某些UI库可能支持动态设置颜色属性
    // 这里简单地使用storage存储，供wxml使用
    // wx.setStorageSync('switchColor', color);
  },

  // 返回上一页
  onBackTap: function () {
    wx.navigateBack()
  },

  // 跳转到自定义银行
  navigateToCustomBank: function () {
    // 延迟一下，确保银行选择弹窗关闭后再显示自定义图片弹窗
    setTimeout(() => {
      // 显示自定义图片弹窗
      this.setData({
        showCustomPopup: true,
        customImageUrl: '',
        customImageName: ''
      })
    }, 300)
  },

  /**
   * 关闭自定义图片弹窗
   */
  onCustomPopupClose: function () {
    this.setData({
      showCustomPopup: false
    })
  },

  /**
   * 图片链接输入事件
   */
  onImageUrlInput: function (e) {
    this.setData({
      customImageUrl: e.detail.value
    })
  },

  /**
   * 图片名称输入事件
   */
  onImageNameInput: function (e) {
    this.setData({
      customImageName: e.detail.value
    })
  },

  /**
   * 保存自定义图片
   */
  onSaveCustomImage: function () {
    const { customImageUrl, customImageName } = this.data

    if (!customImageUrl.trim()) {
      wx.showToast({
        title: '请输入图片链接',
        icon: 'none'
      })
      return
    }

    if (!customImageName.trim()) {
      wx.showToast({
        title: '请输入图片名称',
        icon: 'none'
      })
      return
    }

    // 显示加载中
    wx.showLoading({
      title: '保存中...'
    })

    // 构建请求参数
    const params = {
      name: customImageName,
      image: customImageUrl
    }

    // 调用API保存自定义银行
    wx.request({
      url: util.getBaseUrl() + '/api/account/add_bank',
      method: 'POST',
      data: params,
      header: util.getCommonHeader(),
      success: (res) => {
        wx.hideLoading()

        if (res.data && res.data.code === 1) {
          wx.showToast({
            title: res.data.msg || '保存成功',
            icon: 'success'
          })

          // 关闭弹窗
          this.setData({
            showCustomPopup: false
          })

          // 重新加载银行列表
          this.loadBankList()
        } else {
          wx.showToast({
            title: (res.data && res.data.msg) || '保存失败',
            icon: 'none'
          })
        }
      },
      fail: (err) => {
        wx.hideLoading()
        wx.showToast({
          title: '网络错误，请重试',
          icon: 'none'
        })
        console.error('保存自定义银行失败:', err)
      }
    })
  },

  // 获取资产类型列表
  getAssetTypeList: function () {
    const that = this
    wx.showLoading({
      title: '加载中...'
    })

    // 调用API获取资产类型列表
    getAccounts()
      .then((res) => {
        if (res && res.code === 1) {
          const data = res.data

          // 遍历每个分类下的账户列表
          Object.keys(data).forEach((category) => {
            const accounts = data[category]
            if (Array.isArray(accounts)) {
              accounts.forEach((account) => {
                // 设置图片路径，确保是完整URL
                account.image = account.image ? util.getImageUrl(account.image) : '/assets/icons/bank/default.png'
              })
            }
          })

          // 只使用资金标签
          that.setData({
            assetTypeList: data,
            activeTab: '资金'
          })
        } else {
          wx.showToast({
            title: '获取资产类型失败',
            icon: 'none'
          })
        }
      })
      .catch((err) => {
        console.error('获取资产类型失败:', err)
        wx.showToast({
          title: '网络错误，请重试',
          icon: 'none'
        })
      })
      .finally(() => {
        wx.hideLoading()
      })
  },

  // 显示资产类型选择弹窗
  selectAssetType: function () {
    this.setData({
      assetTypePopupVisible: true
    })
  },

  // 关闭资产类型选择弹窗
  closeAssetTypePopup: function () {
    this.setData({
      assetTypePopupVisible: false
    })
  },

  // 选择资产类型
  selectType: function (e) {
    const item = e.currentTarget.dataset.item
    // 判断是否为信用卡或借记卡类型
    const isCreditCard = item.name === '信用卡' || (item.type === 'liabilities' && item.category === 'fund')
    const isDebitCard = item.name === '借记卡' || (item.type === 'capital' && item.name === '借记卡')

    // 自动设置账户名称（借记卡或信用卡时）
    let accountName = this.data.accountName
    if ((isCreditCard || isDebitCard) && !accountName) {
      // accountName = item.name
    }

    this.setData({
      assetType: item,
      assetTypePopupVisible: false,
      isCreditCard: isCreditCard,
      isDebitCard: isDebitCard,
      accountName: accountName
    })
  },

  // 输入账户名称
  onAccountNameInput: function (e) {
    this.setData({
      accountName: e.detail.value
    })
  },

  // 输入账户余额
  onAccountBalanceInput: function (e) {
    this.setData({
      accountBalance: e.detail.value
    })
  },

  // 切换同步余额开关
  toggleSyncBalance: function (e) {
    this.setData({
      syncBalance: e.detail.value
    })
  },

  // 选择自定义分组
  selectCustomGroup: function () {
    this.showCustomGroupPopup()
  },

  // 关闭自定义分组弹窗
  closeCustomGroupPopup: function () {
    this.setData({
      customGroupPopupVisible: false
    })
  },

  // 选择分组
  selectGroup: function (e) {
    const { group } = e.currentTarget.dataset
    this.setData({
      customGroup: group,
      customGroupPopupVisible: false
    })
  },

  // 切换隐藏资产开关
  toggleHideAsset: function (e) {
    this.setData({
      hideAsset: e.detail.value
    })
  },

  // 切换计入总资产开关
  toggleIncludeInTotal: function (e) {
    this.setData({
      includeInTotal: e.detail.value
    })
  },

  // 选择银行
  selectBank: function () {
    // 打开银行选择弹窗
    this.setData({
      bankPopupVisible: true
    })

    // 加载银行列表数据
    this.loadBankList()
  },

  // 关闭银行选择弹窗
  closeBankPopup: function () {
    this.setData({
      bankPopupVisible: false
    })
  },

  // 加载银行列表数据
  loadBankList: function () {
    wx.showLoading({
      title: '加载中...'
    })

    // 从API获取银行列表数据
    getBankList()
      .then((res) => {
        wx.hideLoading()

        if (res && res.code === 1 && res.data) {
          // 处理返回的银行数据
          const bankData = this.formatBankData(res.data)

          // 提取热门银行
          const topBanks = res.data.top || []
          const hasTopBanks = topBanks && topBanks.length > 0

          // 处理数据，按字母排序并添加索引标题
          const processedData = this.processAndSortBankList(bankData, topBanks)

          this.setData({
            originalBankList: bankData,
            bankList: processedData,
            topBanks: topBanks.map((bank) => ({
              id: bank.id,
              name: bank.name,
              icon: bank.image ? util.getImageUrl(bank.image) : '/static/icon/bank/default.png',
              isFavorite: true
            })),
            hasTopBanks: hasTopBanks
          })
        } else {
          wx.showToast({
            title: res.msg || '获取银行列表失败',
            icon: 'none'
          })
        }
      })
      .catch((err) => {
        wx.hideLoading()
        console.error('获取银行列表失败:', err)
      })
  },

  // 格式化API返回的银行数据
  formatBankData: function (apiData) {
    if (!apiData) return []

    let result = []

    // 处理按字母分组的数据
    Object.keys(apiData).forEach((key) => {
      // 跳过top字段，它已经在别处处理
      if (key === 'top') return

      // 确保是字母分组且是数组
      if (Array.isArray(apiData[key])) {
        apiData[key].forEach((bank) => {
          result.push({
            id: bank.id,
            name: bank.name,
            initial: bank.initial || key,
            icon: bank.image ? util.getImageUrl(bank.image) : '/static/icon/bank/default.png'
          })
        })
      }
    })

    return result
  },

  // 处理并排序银行列表
  processAndSortBankList: function (bankList, topBanks = []) {
    if (!bankList || !bankList.length) return []

    // 按拼音首字母排序
    bankList.sort((a, b) => {
      if (a.initial < b.initial) return -1
      if (a.initial > b.initial) return 1
      return a.name.localeCompare(b.name, 'zh')
    })

    // 添加字母索引标题
    const result = []
    let currentInitial = ''

    // 如果有热门银行，添加到顶部
    if (topBanks && topBanks.length > 0) {
      result.push({
        initial: '',
        isInitial: true
      })

      // 添加热门银行
      topBanks.forEach((bank) => {
        result.push({
          ...bank,
          isInitial: false,
          isFavorite: true
        })
      })
    }

    // 添加字母分组和银行
    bankList.forEach((bank) => {
      // 跳过已经在热门银行中的银行
      if (topBanks && topBanks.some((topBank) => topBank.id === bank.id)) {
        return
      }

      if (bank.initial !== currentInitial) {
        currentInitial = bank.initial
        // 添加字母索引标题
        result.push({
          initial: currentInitial,
          isInitial: true
        })
      }
      // 添加银行项
      result.push({
        ...bank,
        isInitial: false
      })
    })

    return result
  },

  // 银行搜索输入事件
  onBankSearchInput: function (e) {
    const keyword = e.detail.value.trim()

    this.setData({
      bankSearchKeyword: keyword
    })

    if (!keyword) {
      // 如果关键词为空，恢复原始列表
      const processedData = this.processAndSortBankList(this.data.originalBankList, this.data.topBanks)
      this.setData({
        bankList: processedData
      })
      return
    }

    // 根据关键词过滤银行列表
    const filteredList = this.data.originalBankList.filter((bank) => bank.name.indexOf(keyword) !== -1)

    // 过滤热门银行
    const filteredTopBanks = this.data.topBanks.filter((bank) => bank.name.indexOf(keyword) !== -1)

    // 处理并排序过滤后的列表
    const processedData = this.processAndSortBankList(filteredList, filteredTopBanks)

    this.setData({
      bankList: processedData
    })
  },

  // 点击字母索引
  onLetterTap: function (e) {
    const letter = e.currentTarget.dataset.letter

    // 设置当前选中的字母
    this.setData({
      currentLetter: letter
    })

    // 延迟设置scrollIntoView，确保视图更新
    setTimeout(() => {
      this.setData({
        scrollIntoView: letter
      })
    }, 10)
  },

  // 选择银行项
  selectBankItem: function (e) {
    const bank = e.currentTarget.dataset.bank
    const bankIcon = bank.icon || '/assets/icons/bank.png'

    // 设置选中的银行数据
    this.setData({
      selectedBank: bank.name,
      bankIcon: bankIcon,
      bankPopupVisible: false
    })
  },

  // 备注信息输入
  onRemarkInput: function (e) {
    this.setData({
      remark: e.detail.value
    })
  },

  // 银行卡号输入
  onCardNumberInput: function (e) {
    this.setData({
      cardNumber: e.detail.value
    })
  },

  // 信用额度输入
  onCreditLimitInput: function (e) {
    const creditLimit = e.detail.value
    let remainingLimit = ''

    // 如果当前欠款已有值，计算剩余额度
    if (this.data.currentDebt && creditLimit) {
      remainingLimit = (parseFloat(creditLimit) - parseFloat(this.data.currentDebt)).toFixed(2)
    }

    this.setData({
      creditLimit: creditLimit,
      remainingLimit: remainingLimit
    })
  },

  // 当前欠款输入
  onCurrentDebtInput: function (e) {
    const currentDebt = e.detail.value
    let remainingLimit = ''

    // 如果信用额度已有值，计算剩余额度
    if (this.data.creditLimit && currentDebt) {
      remainingLimit = (parseFloat(this.data.creditLimit) - parseFloat(currentDebt)).toFixed(2)
    }

    this.setData({
      currentDebt: currentDebt,
      remainingLimit: remainingLimit,
      accountBalance: '-' + currentDebt // 设置账户余额为当前欠款的负值
    })
  },

  // 账单日期选择
  selectStatementDay: function () {
    // 这里可以实现日期选择的逻辑
  },

  // 还款日期选择
  selectRepaymentDay: function () {
    // 这里可以实现日期选择的逻辑
  },

  // 加载账户详情
  loadAccountDetail: function (accountId) {
    wx.showLoading({
      title: '加载中...'
    })

    getUserAccountDetail({ user_account_id: accountId })
      .then((res) => {
        wx.hideLoading()

        if (res.code === 1 && res.data) {
          const accountData = res.data
          console.log('账户详情数据:', accountData)

          // 处理图片路径，确保是完整URL
          if (accountData.image) {
            accountData.image = util.getImageUrl(accountData.image)
          }

          // 根据账户类型设置表单数据
          const isCreditCard = accountData.account_type === 'liabilities'
          const isDebitCard = !isCreditCard && (accountData.account_name === '借记卡' || accountData.account_name === '储蓄卡')

          // 设置基本信息
          const formData = {
            selectedBank: accountData.name || '',
            accountName: accountData.username || '',
            remark: accountData.username || '',
            cardNumber: accountData.cardnum || '',
            accountBalance: accountData.money || '',
            bankIcon: accountData.image || '/assets/icons/bank/default.png',
            isCreditCard: isCreditCard,
            isDebitCard: isDebitCard,
            customGroup: accountData.group_name || '默认分组',
            includeInTotal: accountData.include === '1',
            hideAsset: accountData.status === 'hidden'
          }

          // 如果是信用卡，设置额外信息
          if (isCreditCard) {
            formData.creditLimit = accountData.debt || ''
            formData.currentDebt = accountData.money || ''
            formData.statementDay = accountData.bill_date ? `每月${accountData.bill_date}日` : '每月1日'
            formData.repaymentDay = accountData.repayment_date ? `每月${accountData.repayment_date}日` : '每月10日'

            // 计算剩余额度
            if (accountData.debt && accountData.money) {
              const debt = parseFloat(accountData.debt) || 0
              const money = parseFloat(accountData.money) || 0
              formData.remainingLimit = (debt - Math.abs(money)).toFixed(2)
            }
          }

          // 更新表单数据
          this.setData(formData)
        } else {
          wx.showToast({
            title: res.msg || '获取账户详情失败',
            icon: 'none'
          })
        }
      })
      .catch((err) => {
        wx.hideLoading()
        console.error('获取账户详情失败:', err)
        wx.showToast({
          title: err.msg || '获取账户详情失败',
          icon: 'none'
        })
      })
  },

  // 保存账户
  saveAccount: function () {
    // 表单验证
    if (!this.validateForm()) {
      return
    }

    // 显示加载中
    wx.showLoading({
      title: '保存中...'
    })

    // 构建请求参数
    const params = {
      // 基础参数
      account_id: this.data.assetType.id,
      group_name: this.data.customGroup, // 添加自定义分组
      include: this.data.includeInTotal ? 1 : 0,
      status: this.data.hideAsset ? 'hidden' : 'normal',
      bill: this.data.syncBalance ? 1 : 0
    }

    // 编辑模式需要添加ID
    if (this.data.isEditMode) {
      params.id = this.data.accountId
    }

    // 根据不同类型设置特定参数
    if (this.data.isReimbursement) {
      // 报销特有参数
      params.account_id = '21' // 报销账户ID
      params.name = '报销'
      params.image = '/assets/img/qrcode.png'.replace(/^\//, '')
      params.username = this.data.reimbursementPerson
      params.money = this.data.accountBalance
      params.reimbursement_status = this.data.reimbursementStatus
      params.remark = this.data.remark || ''
    } else if (this.data.isLendOut) {
      // 借出特有参数
      params.account_id = '22' // 借出账户ID
      params.name = '借出'
      params.image = '/assets/img/qrcode.png'.replace(/^\//, '')
      params.username = this.data.borrower
      params.money = this.data.accountBalance
      params.loan_date = this.data.loanDate
      params.repayment_date = this.data.repayDate
      params.remark = this.data.remark || ''
    } else if (this.data.isBorrowIn) {
      // 借入特有参数
      params.account_id = '23' // 借入账户ID
      params.name = '借入'
      params.image = '/assets/img/qrcode.png'.replace(/^\//, '')
      params.username = this.data.lender
      params.money = this.data.borrowAmount
      params.remark = this.data.remark || ''
    } else if (this.data.isCreditCard) {
      // 信用卡特有参数
      params.name = this.data.selectedBank
      // 处理图片路径，确保正确格式
      let bankIcon = this.data.bankIcon || '/assets/icons/bank/default.png'
      // 移除前导斜杠，因为API需要相对路径
      params.image = bankIcon.replace(/^\//, '')
      params.username = this.data.remark || this.data.selectedBank
      params.cardnum = this.data.cardNumber || ''
      params.money = this.data.currentDebt
      params.debt = this.data.creditLimit
      params.bill_date = this.data.statementDay.replace(/每月|日/g, '')
      params.repayment_date = this.data.repaymentDay.replace(/每月|日/g, '')
    } else if (this.data.isDebitCard) {
      // 借记卡参数
      params.name = this.data.selectedBank
      // 处理图片路径，确保正确格式
      let bankIcon = this.data.bankIcon || '/assets/icons/bank/default.png'
      // 移除前导斜杠，因为API需要相对路径
      params.image = bankIcon.replace(/^\//, '')
      params.username = this.data.remark || this.data.selectedBank
      params.cardnum = this.data.cardNumber || ''
      params.money = this.data.accountBalance
    } else {
      // 普通账户参数
      params.name = this.data.assetType.name
      // 处理图片路径，确保正确格式
      let assetIcon = this.data.assetType.icon || '/assets/img/qrcode.png'
      // 移除前导斜杠，因为API需要相对路径
      params.image = assetIcon.replace(/^\//, '')
      params.username = this.data.accountName || this.data.remark || this.data.assetType.name
      params.money = this.data.accountBalance
    }

    // 调用API创建或更新账户
    const apiMethod = this.data.isEditMode ? updateAccount : addAccount
    const successMsg = this.data.isEditMode ? '更新成功' : '创建成功'
    const failMsg = this.data.isEditMode ? '更新失败' : '创建失败'

    // 调用相应的API方法
    apiMethod(params)
      .then((res) => {
        wx.hideLoading()

        if (res.code === 1) {
          wx.showToast({
            title: res.msg || successMsg,
            icon: 'success',
            duration: 1500
          })
          // 延迟关闭当前页面
          setTimeout(() => {
            wx.navigateBack()
          }, 1500)
        } else {
          wx.showToast({
            title: res.msg || failMsg,
            icon: 'none'
          })
        }
      })
      .catch((err) => {
        wx.hideLoading()
        console.error(failMsg + ':', err)
        wx.showToast({
          title: typeof err === 'object' && err.msg ? err.msg : '网络错误，请重试',
          icon: 'none'
        })
      })
  },

  // 自定义分组输入
  onCustomGroupInput: function (e) {
    this.setData({
      customGroup: e.detail.value
    })
  },

  // 自定义分组输入框聚焦
  onCustomGroupFocus: function () {
    this.setData({
      isCustomGroupFocused: true
    })
  },

  // 自定义分组输入框失焦
  onCustomGroupBlur: function () {
    this.setData({
      isCustomGroupFocused: false
    })
  },

  // 报销人输入
  onReimbursementPersonInput: function (e) {
    this.setData({
      reimbursementPerson: e.detail.value
    })
  },

  // 选择报销状态
  selectReimbursementStatus: function (e) {
    const status = e.currentTarget.dataset.status
    this.setData({
      reimbursementStatus: status
    })
  },

  // 借款人输入
  onBorrowerInput: function (e) {
    this.setData({
      borrower: e.detail.value
    })
  },

  // 出借人输入
  onLenderInput: function (e) {
    this.setData({
      lender: e.detail.value
    })
  },

  // 借入金额输入
  onBorrowAmountInput: function (e) {
    this.setData({
      borrowAmount: e.detail.value,
      accountBalance: e.detail.value // 同步更新账户余额
    })
  },

  // 选择借款日期
  selectLoanDate: function () {
    const that = this
    wx.showDatePicker({
      current: that.data.loanDate || new Date().toISOString().split('T')[0],
      success: function (res) {
        that.setData({
          loanDate: res.date
        })
      }
    })
  },

  // 选择还款日期
  selectRepayDate: function () {
    const that = this
    wx.showDatePicker({
      current: that.data.repayDate || new Date().toISOString().split('T')[0],
      success: function (res) {
        that.setData({
          repayDate: res.date
        })
      }
    })
  },

  // 获取自定义分组列表
  getCustomGroups() {
    wx.showLoading({
      title: '加载中...'
    })

    getAccountGroups()
      .then((res) => {
        wx.hideLoading()
        if (res.code === 1 && res.data) {
          // 提取分组名称列表
          const groups = res.data.map((item) => item.name)
          this.setData({
            groupList: groups
          })
        }
      })
      .catch((err) => {
        wx.hideLoading()
        wx.showToast({
          title: '获取分组失败',
          icon: 'none'
        })
        console.error('获取分组失败', err)
      })
  },

  // 显示自定义分组弹窗
  showCustomGroupPopup() {
    // 确保有最新的分组数据
    this.getCustomGroups()

    this.setData({
      customGroupPopupVisible: true
    })
  },

  // 关闭自定义分组弹窗
  closeCustomGroupPopup() {
    this.setData({
      customGroupPopupVisible: false
    })
  },

  // 表单验证
  validateForm() {
    if (this.data.isReimbursement) {
      // 报销验证
      if (!this.data.reimbursementPerson) {
        wx.showToast({
          title: '请输入报销人',
          icon: 'none'
        })
        return false
      }
      if (!this.data.accountBalance) {
        wx.showToast({
          title: '请输入报销金额',
          icon: 'none'
        })
        return false
      }
    } else if (this.data.isLendOut) {
      // 借出验证
      if (!this.data.borrower) {
        wx.showToast({
          title: '请输入借款人',
          icon: 'none'
        })
        return false
      }
      if (!this.data.accountBalance) {
        wx.showToast({
          title: '请输入借出金额',
          icon: 'none'
        })
        return false
      }
    } else if (this.data.isBorrowIn) {
      // 借入验证
      if (!this.data.lender) {
        wx.showToast({
          title: '请输入出借人',
          icon: 'none'
        })
        return false
      }
      if (!this.data.borrowAmount) {
        wx.showToast({
          title: '请输入借入金额',
          icon: 'none'
        })
        return false
      }
    } else {
      // 普通账户验证
      if (!this.data.accountName && !this.data.isCreditCard && !this.data.isDebitCard) {
        wx.showToast({
          title: '请输入账户名称',
          icon: 'none'
        })
        return false
      }

      if (!this.data.accountBalance && !this.data.isCreditCard) {
        wx.showToast({
          title: '请输入账户余额',
          icon: 'none'
        })
        return false
      }

      // 信用卡额外验证
      if (this.data.isCreditCard) {
        if (!this.data.selectedBank) {
          wx.showToast({
            title: '请选择银行',
            icon: 'none'
          })
          return false
        }

        if (!this.data.creditLimit) {
          wx.showToast({
            title: '请输入信用额度',
            icon: 'none'
          })
          return false
        }

        if (!this.data.currentDebt) {
          wx.showToast({
            title: '请输入当前欠款',
            icon: 'none'
          })
          return false
        }
      }

      // 借记卡额外验证
      if (this.data.isDebitCard) {
        if (!this.data.selectedBank) {
          wx.showToast({
            title: '请选择银行',
            icon: 'none'
          })
          return false
        }

        if (!this.data.cardNumber) {
          wx.showToast({
            title: '请输入银行卡号',
            icon: 'none'
          })
          return false
        }
      }
    }
    return true
  }
})
