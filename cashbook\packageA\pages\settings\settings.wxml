<view class="container">
  <!-- 安全设置区域 -->
  <view class="section">
    <view class="section-header">
      <view class="section-indicator"></view>
      <text class="section-title">安全</text>
    </view>

    <!-- 账号与安全 -->
    <view class="setting-item" bindtap="navigateTo" data-url="/pages/accountSecurity/accountSecurity">
      <view class="item-left">
        <view class="item-icon">
          <image src="/static/icon/account-security.png" mode="aspectFit"></image>
        </view>
        <text class="item-text">账号与安全</text>
      </view>
      <view class="item-right">
        <image src="/static/icon/arrow-right.png" mode="aspectFit"></image>
      </view>
    </view>

    <!-- 安全设置 -->
    <view class="setting-item" bindtap="navigateTo" data-url="/packageA/pages/settings/security/security">
      <view class="item-left">
        <view class="item-icon">
          <image src="/static/icon/security.png" mode="aspectFit"></image>
        </view>
        <text class="item-text">安全设置</text>
      </view>
      <view class="item-right">
        <image src="/static/icon/arrow-right.png" mode="aspectFit"></image>
      </view>
    </view>
  </view>

  <!-- 资产管理区域 -->
  <view class="section">
    <view class="section-header">
      <view class="section-indicator"></view>
      <text class="section-title">资产</text>
    </view>

    <!-- 资产管理 -->
    <view class="setting-item" bindtap="navigateTo" data-url="/packageA/pages/settings/assetManagement/assetManagement">
      <view class="item-left">
        <view class="item-icon">
          <image src="/static/icon/card.png" mode="aspectFit"></image>
        </view>
        <text class="item-text">资产管理</text>
      </view>
      <view class="item-right">
        <image src="/static/icon/arrow-right.png" mode="aspectFit"></image>
      </view>
    </view>
  </view>

  <!-- 其他设置区域 -->
  <view class="section">
    <view class="section-header">
      <view class="section-indicator"></view>
      <text class="section-title">其他</text>
    </view>

    <!-- 语言 -->
    <view class="setting-item" bindtap="navigateTo" data-url="/packageA/pages/settings/language/language">
      <view class="item-left">
        <view class="item-icon">
          <image src="/static/icon/language.png" mode="aspectFit"></image>
        </view>
        <text class="item-text">语言</text>
      </view>
      <view class="item-right">
        <image src="/static/icon/arrow-right.png" mode="aspectFit"></image>
      </view>
    </view>

    <!-- 网络检测 -->
    <view class="setting-item" bindtap="checkNetwork">
      <view class="item-left">
        <view class="item-icon">
          <image src="/static/icon/network.png" mode="aspectFit"></image>
        </view>
        <text class="item-text">网络检测</text>
      </view>
      <view class="item-right">
        <image src="/static/icon/arrow-right.png" mode="aspectFit"></image>
      </view>
    </view>

    <!-- 隐私 -->
    <view class="setting-item" bindtap="navigateTo" data-url="/packageA/pages/settings/privacy/privacy">
      <view class="item-left">
        <view class="item-icon">
          <image src="/static/icon/privacy.png" mode="aspectFit"></image>
        </view>
        <text class="item-text">隐私</text>
      </view>
      <view class="item-right">
        <image src="/static/icon/arrow-right.png" mode="aspectFit"></image>
      </view>
    </view>

    <!-- 关于 -->
    <view class="setting-item" bindtap="navigateTo" data-url="/packageA/pages/settings/about/about">
      <view class="item-left">
        <view class="item-icon">
          <image src="/static/icon/about.png" mode="aspectFit"></image>
        </view>
        <text class="item-text">关于</text>
      </view>
      <view class="item-right">
        <image src="/static/icon/arrow-right.png" mode="aspectFit"></image>
      </view>
    </view>
  </view>

  <!-- 退出登录按钮 -->
  <view class="logout-button" bindtap="logout">
    <text>退出登录</text>
  </view>

  <!-- 底部导航栏 -->
  <view class="tab-bar">
    <view class="tab-item">
      <image src="/static/icon/menu.png" mode="aspectFit"></image>
    </view>
    <view class="tab-item">
      <image src="/static/icon/home.png" mode="aspectFit"></image>
    </view>
    <view class="tab-item">
      <image src="/static/icon/back.png" mode="aspectFit"></image>
    </view>
  </view>
</view>
