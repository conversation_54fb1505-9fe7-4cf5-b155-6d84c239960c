/* 整体容器 */
.book-setting-container {
  background-color: #f5f7fa;
  min-height: 100vh;
  padding-bottom: 30rpx;
}

/* 导航栏 */
.nav-bar {
  display: flex;
  align-items: center;
  padding: 20rpx 30rpx;
  background-color: #ffffff;
  position: relative;
}

.back-icon {
  width: 50rpx;
  height: 50rpx;
  display: flex;
  align-items: center;
}

.back-icon image {
  width: 30rpx;
  height: 30rpx;
}

.page-title {
  flex: 1;
  text-align: center;
  font-size: 34rpx;
  font-weight: bold;
  margin-right: 50rpx;
  /* 平衡左侧图标 */
}

/* 分段容器 */
.section-container {
  margin-top: 20rpx;
  background-color: #ffffff;
  border-radius: 10rpx;
  overflow: hidden;
  box-shadow: 0 2rpx 10rpx rgba(0, 0, 0, 0.05);
  margin-left: 20rpx;
  margin-right: 20rpx;
}

/* 分段标题 */
.section-title {
  display: flex;
  align-items: center;
  padding: 20rpx 30rpx;
  border-bottom: 1rpx solid #f0f0f0;
}

.title-line {
  width: 4px;
  height: 13px;
  border-radius: 2px;
  margin-right: 10px;
  background-image: linear-gradient(to bottom, #ff9999, white);
  background-size: 100% 100%;
}

.section-title text {
  font-size: 28rpx;
  font-weight: 500;
}

/* 设置项 */
.setting-item {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 30rpx;
  border-bottom: 1px solid #f5f5f5;
  position: relative;
}

.setting-item:last-child {
  border-bottom: none;
}

.item-left {
  display: flex;
  align-items: center;
  flex: 3;
}

.item-icon {
  width: 60rpx;
  height: 60rpx;
  border-radius: 50%;
  display: flex;
  justify-content: center;
  align-items: center;
  margin-right: 20rpx;
}

.item-icon image {
  width: 80%;
  height: 80%;
}

/* 不同图标的颜色 */
.smile {
  background-color: #f8e9b0;
}

.circle {
  background-color: #e1f5fe;
}

.document {
  background-color: #e8f5e9;
}

.home {
  background-color: #f5f5f5;
}

.select {
  background-color: #e1f5fe;
}

.image {
  background-color: #f5e6ea;
}

.list {
  background-color: #e0f2f1;
}

.trash {
  background-color: #ffebee;
}

.item-text {
  flex: 1;
  font-size: 25rpx;
}

.main-text {
  font-size: 25rpx;
  color: #333333;
  font-weight: 500;
}

.sub-text {
  font-size: 22rpx;
  color: #999999;
  margin-top: 6rpx;
  line-height: 1.4;
}

.item-right {
  display: flex;
  align-items: center;
  flex: 1;
  justify-content: flex-end;
}

.item-right image {
  width: 30rpx;
  height: 30rpx;
  opacity: 0.5;
}

/* 开关按钮样式 */
switch {
  transform: scale(0.8);
  margin-left: 10rpx;
}

/* 跟随模式容器样式 */
.follow-mode-container {
  display: flex;
  padding: 0 30rpx 25rpx;
  background-color: #ffffff;
  position: relative;
}

/* 笔记布局 */
.note-layout {
  display: flex;
  align-items: flex-start;
  width: 100%;
  padding-top: 5rpx;
  padding-left: 60rpx;
}

/* 笔记图标容器 */
.note-icon-wrapper {
  width: 36rpx;
  height: 36rpx;
  margin-right: 10rpx;
  display: flex;
  justify-content: center;
  align-items: center;
  flex-shrink: 0;
}

.note-icon {
  font-size: 28rpx;
  color: #999999;
  font-weight: bold;
}

/* 跟随模式文本 */
.follow-mode-text {
  font-size: 21rpx;
  color: #999999;
  line-height: 1.4;
  flex: 1;
}

/* 高亮文本样式 */
.highlight-text {
  font-weight: normal;
  color: #666;
}

/* 删除确认弹窗样式 */
.delete-popup-custom {
  width: 80%;
  max-width: 600rpx;
  overflow: hidden;
}

/* 设置默认查询账本弹窗样式 */
.query-popup-custom {
  width: 80%;
  max-width: 600rpx;
  overflow: hidden;
  border-radius: 35rpx;
}

.query-popup-header {
  position: relative;
  padding: 40rpx 30rpx 20rpx;
  text-align: center;
}

.query-popup-title {
  font-size: 34rpx;
  font-weight: bold;
  color: #333;
}

.query-popup-content {
  padding: 20rpx 40rpx 60rpx;
  text-align: center;
}

.query-popup-content text {
  font-size: 28rpx;
  color: #666;
  line-height: 1.6;
}

.query-popup-footer {
  display: flex;
  border-top: 1rpx solid #f5f5f5;
}

.delete-popup-header {
  padding: 30rpx;
  text-align: center;
  position: relative;
  border-bottom: 1rpx solid #f0f0f0;
}

.delete-popup-header text {
  font-size: 30rpx;
  color: #333;
  font-weight: 500;
}

.popup-close {
  position: absolute;
  right: 20rpx;
  top: 20rpx;
  width: 40rpx;
  height: 40rpx;
  display: flex;
  justify-content: center;
  align-items: center;
}

.delete-popup-content {
  padding: 50rpx 30rpx;
  text-align: center;
}

.delete-popup-content text {
  font-size: 32rpx;
  color: #333;
}

.delete-popup-footer {
  display: flex;
  border-top: 1rpx solid #f0f0f0;
}

.popup-btn {
  flex: 1;
  text-align: center;
  padding: 25rpx 0;
  font-size: 32rpx;
}

.cancel-btn {
  background-color: #f5f5f5;
  color: #666;
}

.confirm-btn {
  background-color: #56b3dd;
  color: #fff;
}



/* 添加账本弹出层容器 */
.popup-container {
  display: flex;
  flex-direction: column;
  height: 60vh;
  /* 限制总高度 */
  position: relative;
  border-radius: inherit;
}

/* 添加账本弹层内容样式 */
.add-book-popup {
  padding: 0;
  flex: 1;
  height: calc(100% - 120rpx);
  /* 减去底部按钮的高度 */
  border-radius: 40rpx;
  /* 顶部圆角 */
  overflow-y: auto;
  /* // background-color: #000000; */
  box-shadow: 0px 4px 12px rgba(0, 0, 0, 0.1);
  margin-bottom: 30px;

  .book-cover {
    position: relative;
    width: 100%;
    height: 300rpx;
    margin-bottom: 30rpx;
    border-radius: 40rpx 40rpx 0 0;
    overflow: hidden;
    background-color: #f8f8f8;
    display: flex;
    justify-content: center;
    align-items: center;
    box-shadow: 0 4rpx 12rpx rgba(0, 0, 0, 0.05);

    .cover-image {
      width: 100%;
      height: 100%;
      object-fit: cover;
    }

    .book-cover-overlay {
      position: absolute;
      top: 0;
      left: 0;
      width: 100%;
      height: 100%;
      display: flex;
      flex-direction: column;
      justify-content: center;
      align-items: center;
      background-color: rgba(0, 0, 0, 0.3);

      .refresh-icon {
        width: 64rpx;
        height: 64rpx;

        image {
          width: 100%;
          height: 100%;
          opacity: 0.9;
        }
      }

      .cover-text {
        color: #fff;
        font-size: 36rpx;
        font-weight: 500;
        text-shadow: 0 2rpx 4rpx rgba(0, 0, 0, 0.3);
      }
    }

    .cover-refresh {
      position: absolute;
      right: 20rpx;
      bottom: 20rpx;
      width: 60rpx;
      height: 60rpx;
      background-color: rgba(255, 255, 255, 0.8);
      border-radius: 50%;
      display: flex;
      align-items: center;
      justify-content: center;
      z-index: 10;

      image {
        width: 36rpx;
        height: 36rpx;
      }
    }
  }

  .category-init {
    display: flex;
    align-items: center;
    padding: 0 30rpx;

    .init-icon {
      width: 70rpx;
      height: 70rpx;
      margin-right: 20rpx;
      background-color: #f0f0f0;
      border-radius: 50%;
      display: flex;
      align-items: center;
      justify-content: center;

      image {
        width: 60rpx;
        height: 60rpx;
        border-radius: 50%;
      }
    }

    .init-content {
      flex: 1;
      display: flex;
      flex-direction: column;
    }

    .init-text {
      font-size: 34rpx;
      color: #333;
      font-weight: 500;
      line-height: 1.4;
    }

    .init-desc {
      font-size: 26rpx;
      color: #999;
      margin-top: 6rpx;
    }

    .init-arrow {
      width: 40rpx;
      height: 40rpx;
      display: flex;
      align-items: center;
      justify-content: center;

      image {
        width: 24rpx;
        height: 24rpx;
        opacity: 0.6;
      }
    }
  }

  .book-form {
    margin: 0 30rpx;
    padding-top: 20rpx;

    .form-item {
      padding: 0;
      margin-bottom: 20rpx;
      border-bottom: none;
      position: relative;

      .input-container {
        position: relative;
        width: 100%;
      }

      .input-label {
        position: absolute;
        left: 40rpx;
        top: 28rpx;
        /* 输入框高度的中心位置 */
        font-size: 30rpx;
        color: #999;
        transition: all 0.3s ease;
        pointer-events: none;
        /* 让标签不阻挡输入框的点击 */
        transform-origin: left top;
        z-index: 1;
      }

      .input-placeholder {
        position: absolute;
        left: 40rpx;
        top: 28rpx;
        font-size: 30rpx;
        color: #999;
        pointer-events: none;
        opacity: 0;
        transition: opacity 0.2s ease;
      }

      .input-placeholder.show {
        opacity: 1;
      }

      .input-label.focus,
      .input-label.has-value {
        top: -16rpx;
        font-size: 28rpx;
        font-weight: 500;
      }

      .book-input {
        width: 100%;
        height: 90rpx;
        font-size: 32rpx;
        color: #333;
        background-color: #f5f5f5;
        border-radius: 45rpx;
        padding: 0 40rpx;
        box-sizing: border-box;
        font-weight: 300;
      }

      &:last-child {
        margin-bottom: 30rpx;
      }
    }
  }
}


/* 底部占位区域，确保内容可以滚动到底部按钮上方 */
.bottom-placeholder {
  height: 40rpx;
}

/* 保存按钮容器 */
.save-btn-container {
  position: sticky;
  bottom: 0;
  left: 0;
  right: 0;
  padding: 20rpx 30rpx 30rpx;
  background-color: #fff;
  border-top: 1rpx solid #f5f5f5;
  z-index: 10;
}

/* 保存按钮样式 */
.save-btn {
  height: 90rpx;
  background-color: #ff6a6a;
  color: #fff;
  font-size: 32rpx;
  display: flex;
  justify-content: center;
  align-items: center;
  border-radius: 45rpx;
}

/* 封面选择弹层样式 */
.cover-select-container {
  padding: 20rpx;
  overflow-y: auto;
}

.cover-grid {
  display: flex;
  flex-wrap: wrap;
  justify-content: space-between;
  padding: 10rpx;
}

.cover-item {
  width: calc(50% - 20rpx);
  height: 180rpx;
  margin-bottom: 20rpx;
  border-radius: 20rpx;
  overflow: hidden;
  position: relative;
  box-shadow: 0 2rpx 8rpx rgba(0, 0, 0, 0.1);
}

.cover-thumb {
  width: 100%;
  height: 100%;
  object-fit: cover;
}

.custom-cover {
  background-color: #f5f5f5;
  display: flex;
  align-items: center;
  justify-content: center;
  border: 2rpx dashed #ddd;
}

.custom-cover-content {
  text-align: center;
  color: #999;
  font-size: 30rpx;
}

/* 错误提示条样式 - 黄底黑字 */
.error-tip {
  position: fixed;
  top: 50%;
  left: 50%;
  transform: translate(-50%, -50%);
  background-color: #FFEB3B;
  color: #000;
  padding: 20rpx 40rpx;
  font-size: 28rpx;
  border-radius: 30rpx;
  text-align: center;
  z-index: 10000;
  box-shadow: 0 4rpx 12rpx rgba(0, 0, 0, 0.15);
  min-width: 300rpx;
  max-width: 80%;
  font-weight: 500;
}

.close-error {
  display: none;
  /* 隐藏关闭按钮 */
}

/* 必填标记 */
.required-mark {
  color: #ff4d4f;
  margin-left: 6rpx;
  font-size: 24rpx;
}

/* 分类必选样式 */
.category-required {
  border: 1px solid #ff4d4f;
  border-radius: 8rpx;
  animation: shake 0.5s ease-in-out;
}

@keyframes shake {

  0%,
  100% {
    transform: translateX(0);
  }

  10%,
  30%,
  50%,
  70%,
  90% {
    transform: translateX(-5rpx);
  }

  20%,
  40%,
  60%,
  80% {
    transform: translateX(5rpx);
  }
}

/* "更多"菜单列表样式 */
.more-menu-list {
  max-height: calc(80vh - 100rpx);
  overflow-y: auto;

  .more-menu-item {
    display: flex;
    align-items: center;
    padding: 30rpx 40rpx;
    border-bottom: 1px solid #eee;

    &:last-child {
      border-bottom: none;
      margin-bottom: 20px;
      /* Add some space at the bottom for iOS devices */
    }

    .menu-item-icon {
      width: 50rpx;
      height: 50rpx;
      display: flex;
      align-items: center;
      justify-content: center;
      color: #999;
      margin-right: 20rpx;
      font-size: 40rpx;
    }

    .menu-item-content {
      flex: 1;

      .menu-item-title {
        font-size: 30rpx;
        color: #333;
        margin-bottom: 6rpx;
      }

      .menu-item-desc {
        font-size: 24rpx;
        color: #999;
      }
    }

    .menu-item-arrow {
      width: 40rpx;
      height: 40rpx;
      display: flex;
      align-items: center;
      justify-content: center;
      color: #ccc;
    }

    .menu-item-switch {
      margin-left: 20rpx;
    }
  }
}


/* 提示框样式 */
.tip-container {
  display: flex;
  background-color: rgba(237, 246, 253, 0.8);
  padding: 20rpx 30rpx;
  border-radius: 8rpx;
  margin: 20rpx 20rpx 30rpx;
  align-items: center;
  
}

.tip-left-border {
  width: 8rpx;
  height: 40rpx;
  background-color: #58b7ff;
  border-radius: 4rpx;
  margin-right: 20rpx;
  flex-shrink: 0;
}

.tip-content {
  flex: 1;
}

.tip-text {
  font-size: 26rpx;
  color: #666;
  line-height: 1.5;
}

.title-line {
  width: 4px;
  height: 20px;
  border-radius: 2px;
  margin-right: 10px;
  background-image: linear-gradient(to bottom, #ff9999, white);
  background-size: 100% 100%;
}

/* 账本封存弹窗样式 */
.archive-popup-container {
  padding: 20rpx 30rpx;
}

.archive-desc {
  background-color: rgba(242, 250, 254, 0.888);
  border-radius: 12rpx;
  padding: 20rpx;
  margin-bottom: 30rpx;
}

.desc-title {
  font-size: 28rpx;
  font-weight: 500;
  color: #333;
  margin-bottom: 10rpx;
}

.desc-content {
  font-size: 24rpx;
  color: #666;
  line-height: 1.5;
}

.archive-status-item {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 20rpx 0;
  border-radius: 12rpx;
  background-color: #fff;
}

.archive-status-item .item-left {
  display: flex;
  align-items: center;
}

.archive-status-item .item-icon {
  width: 60rpx;
  height: 60rpx;
  border-radius: 50%;
  // background-color: rgba(64, 224, 208, 0.1);
  display: flex;
  align-items: center;
  justify-content: center;
  margin-right: 20rpx;
}

.archive-status-item .item-icon image {
  width: 50rpx;
  height: 50rpx;
}

.archive-status-item .item-text .main-text {
  font-size: 28rpx;
  color: #333;
  margin-bottom: 6rpx;
}

.archive-status-item .item-text .sub-text {
  font-size: 24rpx;
  color: #999;
}

.archive-status-item .item-right {
  display: flex;
  align-items: center;
} 