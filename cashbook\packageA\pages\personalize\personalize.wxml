<view class="content">
  <!-- 头部导航栏 -->
  <view class="nav">
    <customNav IconPath="{{iconPath}}" rightText="管理" bind:sendText="getText" isRight="{{true}}" isTitle="个性化" isIcon="{{false}}"></customNav>
  </view>

  <!-- 顶部图标和描述 -->
  <view class="header-section">
    <view class="icon-container">
      <text class="iconfont icon-qiabao"></text>
    </view>
    <view class="header-text">个性化设置APP风格。</view>
  </view>

  <!-- 个性化设置区域 -->
  <view class="section-title">
    <view class="section-indicator"></view>
    <text>个性化</text>
  </view>

  <!-- 主题设置 -->
  <view class="setting-item" bindtap="navigateToTheme">
    <view class="setting-left">
      <view class="setting-icon theme-icon">
        <image src="/static/icon/theme.png" mode="aspectFit"></image>
      </view>
      <text>主题</text>
    </view>
    <view class="setting-right">
      <image src="/static/icon/arrow-right.png" mode="aspectFit" class="arrow-icon"></image>
    </view>
  </view>

  <!-- 主题风格 -->
  <view class="setting-item" bindtap="navigateToThemeStyle">
    <view class="setting-left">
      <view class="setting-icon theme-style-icon">
        <image src="/static/icon/theme-style.png" mode="aspectFit"></image>
      </view>
      <text>主题风格</text>
    </view>
    <view class="setting-right">
      <text class="follow-system">跟随系统</text>
      <image src="/static/icon/arrow-right.png" mode="aspectFit" class="arrow-icon"></image>
    </view>
  </view>

  <!-- 底部导航栏 -->
  <view class="bottom-tabbar">
    <view class="tab-item">
      <image src="/static/icon/menu.png" mode="aspectFit"></image>
    </view>
    <view class="tab-item">
      <image src="/static/icon/home-tab.png" mode="aspectFit"></image>
    </view>
    <view class="tab-item">
      <image src="/static/icon/back.png" mode="aspectFit"></image>
    </view>
  </view>
</view>
