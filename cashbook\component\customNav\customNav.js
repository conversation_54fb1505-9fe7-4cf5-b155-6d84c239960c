// component/customNav/customNav.js
Component({

  /**
   * 组件的属性列表
   */
  properties: {
    // 中间tab
    list:{
      type:Array
    },
    // 中间标题或tab 传title的话则不显示list
    isTitle:{
       type:String,
       defalut:''
    },
    // 右侧是否显示
    isRight:{
      type:<PERSON><PERSON><PERSON>,
      defalut:false
    },
    // 右侧显示图标或是文本
    isIcon:{
      type:<PERSON>olean,
      defalut:false
    },
    IconPath:{
       type:String 
    },
    // 是否显示关闭按钮。false则显示返回箭头
    isClose:{
      type:<PERSON>olean,
      defalut:true,
    },
    // 右侧文本内容，默认为更多
    rightText:{
      type:String,
      defalut:'更多'
    }

    
  },

  /**
   * 组件的初始数据
   */
  data: {
       activeId:1,
      
  },


  /**
   * 组件的方法列表
   */
  methods: {
    back(){
      wx.navigateBack();
    },
    showCalendar(){
      this.triggerEvent('sendShow'),
      this.triggerEvent('sort',1)
    },
    sendText(){
        console.log('rightText',this.properties.rightText);
        this.triggerEvent('sendText',this.properties.rightText)
    },
    toggele(e){
      const id=e.currentTarget.dataset.id;
      this.setData({
        activeId:id
      })
      this.triggerEvent('getTitleId',id);
    }
  }
})