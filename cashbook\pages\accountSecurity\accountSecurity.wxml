<view class="container">
  <!-- 顶部导航 -->
  <view class="nav-header">
    <view class="nav-back" bindtap="goBack">
      <image src="/static/icon/back.png" mode="aspectFit" />
    </view>
    <view class="nav-title">账号与安全</view>
    <view class="nav-placeholder"></view>
  </view>

  <!-- 内容区域 -->
  <view class="content">
    <!-- 登录信息 -->
    <view class="section">
      <view class="section-title">登录信息</view>
      
      <!-- 密保手机 -->
      <view bind:tap="modifyPhone" class="info-item" bindtap="modifyPhone">
        <view class="info-left">
          <image class="info-icon" src="/static/icon/mobile.png" mode="aspectFit" />
          <text class="info-label">密保手机</text>
        </view>
        <view class="info-right">
          <t-icon name="chevron-right" size="24" color="#BBBBBB" />
        </view>
      </view>
      <view bind:tap="modifyPhone" class="info-value-row">
        <text class="info-value">{{userInfo.mobile}}</text>
        <view class="modify-btn">修改</view>
      </view>
      
      <!-- 账号密码 -->
      <view bind:tap="modifyPassword" class="info-item" bindtap="modifyPassword">
        <view class="info-left">
          <image class="info-icon" src="/static/icon/password.png" mode="aspectFit" />
          <text class="info-label">账号密码</text>
        </view>
        <view class="info-right">
          <t-icon name="chevron-right" size="24" color="#BBBBBB" />
        </view>
      </view>
      <view bind:tap="modifyPassword" class="info-value-row">
        <text class="info-value">为保证账户安全请定期更改</text>
        <view class="modify-btn">修改</view>
      </view>
    </view>

    <!-- 第三方账号绑定 -->
    <view class="section">
      <view class="section-title">第三方账号绑定</view>
      
      <!-- 微信 -->
      <view class="info-item" bindtap="toggleWechatBind">
        <view class="info-left">
          <image class="info-icon" src="/static/icon/wechat.png" mode="aspectFit" />
          <text class="info-label">微信</text>
        </view>
        <view class="info-right">
          <text class="info-value">{{wechatBound ? '已绑定' : '未绑定'}}</text>
          <view class="toggle-switch">
            <switch checked="{{wechatBound}}" color="#a5ddb9" />
          </view>
        </view>
      </view>
    </view>

    <!-- 其他 -->
    <view class="section">
      <view class="section-title">其他</view>
      
      <!-- 注销账号 -->
      <view class="info-item" bindtap="deleteAccount">
        <view class="info-left">
          <image class="info-icon" src="/static/icon/delete.png" mode="aspectFit" />
          <text class="info-label">注销账号</text>
        </view>
        <view class="info-right">
          <text class="info-value">注销后无法恢复</text>
          <t-icon name="chevron-right" size="24" color="#BBBBBB" />
        </view>
      </view>
    </view>
  </view>
</view>