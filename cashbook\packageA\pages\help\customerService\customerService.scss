/* packageA/pages/help/customerService/customerService.wxss */
.customer-service {
  background-color: #f5f5f5;
  min-height: 100vh;
  padding: 0 0 30rpx 0;
  position: relative;
  
  .nav {
    margin-bottom: 20rpx;
    position: fixed;
    top: 0;
    left: 0;
    right: 0;
    z-index: 200;
  }
  
  // 导航栏占位符，防止内容被遮挡
  .nav-placeholder {
    height: 180rpx; // 根据导航栏实际高度调整
  }
  
  .service-content {
    padding: 30rpx;
    
    .service-item {
      background-color: #ffffff;
      border-radius: 16rpx;
      padding: 30rpx;
      margin-bottom: 30rpx;
      box-shadow: 0 2rpx 10rpx rgba(0, 0, 0, 0.05);
      
      .item-title {
        display: flex;
        align-items: center;
        margin-bottom: 20rpx;
        
        .title-icon {
          width: 40rpx;
          height: 40rpx;
          margin-right: 15rpx;
        }
        
        text {
          font-size: 32rpx;
          font-weight: bold;
          color: #333333;
        }
      }
      
      .service-tips {
        font-size: 24rpx;
        color: #999999;
        margin-top: 15rpx;
        display: block;
      }
    }
    
    .phone-section {
      .phone-number {
        display: flex;
        align-items: center;
        justify-content: space-between;
        font-size: 34rpx;
        color: #333333;
        padding: 20rpx 0;
        border-bottom: 1rpx dashed #eeeeee;
        
        text {
          font-weight: 500;
        }
        
        .call-button {
          background-color: #97bf75;
          color: #ffffff;
          font-size: 26rpx;
          padding: 10rpx 20rpx;
          border-radius: 30rpx;
        }
      }
    }
    
    .qrcode-section {
      .qrcode-container {
        display: flex;
        justify-content: center;
        align-items: center;
        width: 100%;
        padding: 30rpx 0;
        
        .qrcode-image {
          width: 400rpx;
          height: 400rpx;
          background-color: #f9f9f9;
        }
        
        .no-qrcode {
          height: 200rpx;
          display: flex;
          align-items: center;
          justify-content: center;
          font-size: 28rpx;
          color: #999999;
          background-color: #f9f9f9;
          width: 80%;
          border-radius: 8rpx;
        }
      }
    }
    
    .service-time {
      background-color: #ffffff;
      border-radius: 16rpx;
      padding: 30rpx;
      box-shadow: 0 2rpx 10rpx rgba(0, 0, 0, 0.05);
      
      .time-title {
        font-size: 32rpx;
        font-weight: bold;
        color: #333333;
        display: block;
        margin-bottom: 15rpx;
      }
      
      .time-content {
        font-size: 28rpx;
        color: #333333;
        display: block;
        margin-bottom: 10rpx;
      }
      
      .time-tips {
        font-size: 24rpx;
        color: #999999;
      }
    }
  }
}