<view class="container">
  <!-- 顶部导航 -->
  <view class="nav-header">
    <view class="nav-back" bindtap="goBack">
      <image src="/static/icon/back.png" mode="aspectFit" />
    </view>
    <view class="nav-title">验证手机号</view>
    <view class="nav-placeholder"></view>
  </view>

  <!-- 内容区域 -->
  <view class="content">
    <!-- 手机号显示 -->
    <view class="phone-display">
      <text class="phone-number">{{phoneNumber}}</text>
      <text class="phone-hint">输入手机号*号码部分号码</text>
    </view>

    <!-- 验证码输入框 -->
    <view class="verification-code">
      <view class="code-input-box" wx:for="{{4}}" wx:key="index">
        <input type="number" maxlength="1" bindinput="inputCode" data-index="{{index}}" focus="{{currentFocus === index}}" value="{{codeValues[index]}}" />
      </view>
    </view>

    <!-- 清除按钮 -->
    <view class="clear-btn" bindtap="clearCode">
      <text>清除</text>
    </view>
  </view>

  <!-- 验证按钮 -->
  <view class="verify-btn" bindtap="verifyCode">
    <text>验证</text>
  </view>

  <!-- 底部导航栏 -->
  <view class="bottom-tabbar">
    <view class="tab-item">
      <image src="/static/icon/menu.png" mode="aspectFit"></image>
    </view>
    <view class="tab-item">
      <image src="/static/icon/home-tab.png" mode="aspectFit"></image>
    </view>
    <view class="tab-item">
      <image src="/static/icon/back.png" mode="aspectFit"></image>
    </view>
  </view>
</view>