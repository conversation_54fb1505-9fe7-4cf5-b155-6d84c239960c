// utils/request.js

// 基础URL
const BASE_URL = 'http://localhost:3000';

// 请求拦截器
const requestInterceptor = (options) => {
    // 可以在这里添加请求头、处理请求参数等
    options.header = {
        ...options.header,
        'Content-Type': 'application/json'
    };
    return options;
};

// 响应拦截器
const responseInterceptor = (res) => {
    // 可以在这里处理响应数据、判断状态码等
    if (res.statusCode === 200) {
        return res.data;
    } else {
        console.error('请求出错:', res);
        return null;
    }
};

// 封装请求方法
const request = (options) => {
    // 合并默认配置和用户传入的配置
    const defaultOptions = {
        url: '',
        method: 'GET',
        data: {},
        header: {},
        timeout: 5000
    };
    const mergedOptions = { ...defaultOptions, ...options };

    // 处理URL
    mergedOptions.url = BASE_URL + mergedOptions.url;

    // 请求拦截器处理
    const interceptedOptions = requestInterceptor(mergedOptions);

    return new Promise((resolve, reject) => {
        wx.request({
            ...interceptedOptions,
            success: (res) => {
                const responseData = responseInterceptor(res);
                if (responseData) {
                    resolve(responseData);
                } else {
                    reject(new Error('请求出错'));
                }
            },
            fail: (err) => {
                console.error('请求失败:', err);
                reject(err);
            }
        });
    });
};

export default request;