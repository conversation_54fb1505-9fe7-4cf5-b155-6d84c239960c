<view class="content">
  <view class="nav">
    <customNav isRight="{{true}}" bind:sendText="getText" isTitle="{{listDetail.name}}" isIcon="{{false}}" rightText="管理"></customNav>
  </view>

  <view class="card3" bind:tap="toDetail">
    <view class="header3">
      <view>还剩那么多，加油哦~</view>
      <view bind:tap="toEdit">编辑</view>
    </view>
    <!-- 第二列 -->
    <view class="gridTmeplte">
      <view class="avator avatorBg brr">
        <image src="/static/icon/avator.png" mode="" />
      </view>
      <view>
        <view>{{listDetail.name}}</view>
        <view class="description2 noMP fs14">{{listDetail.notes}}</view>
      </view>
      <view>
        <view>￥{{listDetail.totoal_money}}</view>
        <view class="description2 noMP fs14" style="text-align: right">{{listDetail.completed_num}}/{{listDetail.incomplete_num}}件</view>
      </view>
    </view>
    <!-- 第三列 -->
    <view class="sp1">
      <view>预购{{listDetail.starttime}}开始</view>
      <view>预购{{listDetail.endtime}}结束</view>
    </view>
  </view>

  <!-- 进度条 -->
  <view class="card3">
    <view class="progress">
      <view>已存：{{listDetail.completed_num}}</view>
      <view>剩余：{{listDetail.incomplete_num}}</view>
    </view>
    <progress :percent="{{listDetail.completed_percent*100}}" show-info stroke-width="6" />
    <view class="progress">
      <view>开始:{{listDetail.starttime}}</view>
      <view>结束:{{listDetail.endtime}}</view>
    </view>
  </view>

  <!-- 商品列表 -->
  <view class="card3">
    <view class="header3">
      <view>商品列表</view>
      <view class="col1" bind:tap="toAddShop">添加</view>
    </view>
    <!-- 第二列 -->
    <view class="shopInfo" wx:for="{{listgoodss}}" wx:key="index">
      <!-- 左侧 -->
      <view class="SIleft">
        <view>
          <checkbox value="" />
        </view>
        <view class="avatorBg avator brr">
          <image src="/static/icon/card.png" mode="" />
        </view>
        <view>
          <view>{{item.name}}</view>
          <view class="description2 noMP">{{item.notes}}</view>
        </view>
        <view> {{item.status_text}} </view>
      </view>
      <!-- 右侧 -->
      <view class="SIright">
        <view>￥{{(item.money*100*item.num)/100}}</view>
        <view class="description2 noMP fs12">单价：{{item.money}}/{{item.num}}件</view>
        <view><van-rate value="{{ item.priority }}" gutter="1rpx" size="15" disabled-color="#f4af47" disabled bind:change="onChange" /> </view>
        <view class="sp1 noMP" style="justify-content: end">
          <view wx:if="{{item.preordertime}}">预购:{{item.preordertime}}</view>
          <view wx:else>预购时间:无</view>
          <!-- <view>默认账本</view> -->
        </view>
      </view>
    </view>
  </view>

  <view class="mask" wx:if="{{depositedShow || managerDialog || showShare}}"></view>
  <!-- 管理弹框 managerDialog -->
  <view class="dialog" wx:if="{{managerDialog}}">
    <view style="padding: 10px">
      <view class="dialogheader">
        <view class="close" bind:tap="closeDialog2">X</view>
      </view>
      <view class="manager">
        <view class="mitem" bind:tap="toEdit">
          <view> 编辑</view>
          <view class="icon2">
            <image src="/static/icon/arrRight.png" mode="" />
          </view>
        </view>
        <view class="mitem" bind:tap="GD">
          <view> 暂停</view>
          <view class="icon2">
            <image src="/static/icon/arrRight.png" mode="" />
          </view>
        </view>
        <view class="mitem" bind:tap="delete">
          <view> 删除</view>
          <view class="icon2">
            <image src="/static/icon/arrRight.png" mode="" />
          </view>
        </view>
      </view>
    </view>
  </view>
</view>
