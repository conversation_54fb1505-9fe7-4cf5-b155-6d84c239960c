/* 整体容器 */
.container {
  display: flex;
  flex-direction: column;
  min-height: 100vh;
  background-color: #f5f5f5;
  position: relative;
}

/* 顶部导航 */
.nav-header {
  display: flex;
  align-items: center;
  justify-content: space-between;
  padding: 10px 15px;
  background-color: #ffffff;
  position: sticky;
  top: 0;
  z-index: 100;
  box-shadow: 0 1px 3px rgba(0, 0, 0, 0.05);

  .nav-back {
    width: 24px;
    height: 24px;
    display: flex;
    align-items: center;
    justify-content: center;

    image {
      width: 20px;
      height: 20px;
    }
  }

  .nav-title {
    font-size: 16px;
    font-weight: 500;
  }

  .nav-placeholder {
    width: 24px;
  }
}

/* 页面说明 */
.page-desc {
  display: flex;
  align-items: center;
  padding: 15px;
  background-color: #ffffff;
  margin-bottom: 10px;
  border-radius: 0 0 12px 12px;

  .desc-icon {
    width: 48rpx;
    height: 48rpx;
    margin-right: 10px;
    display: flex;
    align-items: center;
    justify-content: center;
  }

  .desc-text {
    font-size: 14px;
    color: #999999;
  }
}

/* 分区样式 */
.section {
  margin: 10px 0;
  background-color: #ffffff;
  overflow: hidden;

  .section-title {
    padding: 12px 15px;
    font-size: 14px;
    color: #999999;
    font-weight: 500;
    position: relative;

    &::before {
      content: "";
      position: absolute;
      left: 0;
      top: 14px;
      width: 3px;
      height: 14px;
      background-color: #a5ddb9;
    }
  }
}

/* 设置项样式 */
.setting-item {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 15px;
  background-color: #ffffff;

  .item-label {
    font-size: 15px;
    color: #333333;
  }

  .item-value {
    display: flex;
    align-items: center;

    text {
      font-size: 14px;
      color: #999999;
      margin-right: 5px;
    }
  }
}

.item-desc {
  font-size: 12px;
  color: #999999;
  padding: 0 15px 15px;
  border-bottom: 1px solid #f5f5f5;
}

/* 开关样式 */
switch {
  transform: scale(0.8);
}

/* 弹窗样式 */
.date-picker-popup {
  width: 650rpx;
  background-color: #fff;
  border-radius: 24rpx;
  overflow: hidden;

  .popup-header {
    padding: 30rpx;
    text-align: center;
    position: relative;

    .popup-title {
      font-size: 32rpx;
      font-weight: bold;
      margin-bottom: 16rpx;
    }

    .close-btn {
      position: absolute;
      left: 20rpx;
      top: 20rpx;
    }
  }

  .popup-subtitle {
    font-size: 28rpx;
    color: #333;
    margin-bottom: 10rpx;
  }

  .popup-desc {
    font-size: 24rpx;
    color: #999;
  }

  .date-grid {
    padding: 20rpx 30rpx;

    .date-row {
      display: flex;
      justify-content: space-between;
      margin-bottom: 20rpx;

      .date-cell {
        width: 70rpx;
        height: 70rpx;
        display: flex;
        align-items: center;
        justify-content: center;
        border-radius: 50%;
        font-size: 28rpx;

        &.selected {
          background-color: #a5ddb9;
          color: #fff;
        }
      }
    }
  }

  .popup-footer {
    display: flex;
    border-top: 1rpx solid #eee;

    .cancel-btn, .confirm-btn {
      flex: 1;
      height: 90rpx;
      display: flex;
      align-items: center;
      justify-content: center;
      font-size: 30rpx;
    }

    .cancel-btn {
      color: #666;
      background-color: #f5f5f5;
    }

    .confirm-btn {
      color: #fff;
      background-color: #a5ddb9;
    }
  }
}

// 账本选择弹窗样式
.book-selector-popup {
  width: 100%;
  background-color: #fff;
  border-radius: 24rpx 24rpx 0 0;
  overflow: hidden;
  padding-bottom: 30rpx;
  
  .popup-header {
    padding: 30rpx;
    display: flex;
    justify-content: space-between;
    align-items: center;
    border-bottom: 1rpx solid #f5f5f5;
    
    .close-btn {
      width: 48rpx;
      height: 48rpx;
    }
    
    .header-actions {
      display: flex;
      align-items: center;
      flex: 1;
      justify-content: space-between;
      margin-left: 20rpx;
      
      .view-mode-switch {
        display: flex;
        background-color: #f5f5f5;
        border-radius: 30rpx;
        overflow: hidden;
        
        .mode-item {
          padding: 10rpx 30rpx;
          font-size: 28rpx;
          color: #666;
          
          &.active {
            background-color: #a5ddb9;
            color: #fff;
          }
        }
      }
      
      .add-btn {
        padding: 10rpx 30rpx;
        background-color: #a5ddb9;
        color: #fff;
        font-size: 28rpx;
        border-radius: 30rpx;
      }
    }
  }
  
  .book-list {
    padding: 20rpx;
    
    &.card-mode {
      display: flex;
      flex-wrap: wrap;
      justify-content: space-between;
      
      .book-item {
        width: 48%;
        margin-bottom: 20rpx;
        
        .book-card {
          position: relative;
          height: 180rpx;
          border-radius: 16rpx;
          overflow: hidden;
          background-color: #f5f5f5;
          display: flex;
          justify-content: center;
          align-items: center;
          
          .book-image {
            position: absolute;
            top: 0;
            left: 0;
            width: 100%;
            height: 100%;
            z-index: 1;
          }
          
          .book-name {
            position: relative;
            z-index: 2;
            color: #fff;
            font-size: 32rpx;
            font-weight: bold;
            text-shadow: 0 2rpx 4rpx rgba(0, 0, 0, 0.5);
            padding: 10rpx;
            text-align: center;
            background-color: rgba(0, 0, 0, 0.3);
            width: 100%;
          }
          
          .book-select-indicator {
            position: absolute;
            top: 10rpx;
            right: 10rpx;
            z-index: 3;
            
            .select-circle {
              width: 40rpx;
              height: 40rpx;
              border-radius: 50%;
              background-color: #a5ddb9;
              border: 2rpx solid #fff;
              box-shadow: 0 2rpx 4rpx rgba(0, 0, 0, 0.2);
            }
          }
        }
      }
    }
    
    &.list-mode {
      .book-item {
        display: flex;
        align-items: center;
        padding: 20rpx 0;
        border-bottom: 1rpx solid #f5f5f5;
        
        &.selected {
          background-color: #f0f9f4;
        }
        
        .book-icon {
          width: 80rpx;
          height: 80rpx;
          border-radius: 50%;
          background-color: #a5ddb9;
          display: flex;
          justify-content: center;
          align-items: center;
          margin-right: 20rpx;
          
          image {
            width: 100%;
            height: 100%;
            border-radius: 50%;
          }
          
          text {
            color: #fff;
            font-size: 32rpx;
            font-weight: bold;
          }
        }
        
        .all-books-icon {
          background-color: #f5f5f5;
        }
        
        .book-info {
          flex: 1;
          
          .book-name {
            font-size: 30rpx;
            color: #333;
            margin-bottom: 6rpx;
          }
          
          .book-desc {
            font-size: 24rpx;
            color: #999;
          }
        }
      }
    }
  }
  
  .reload-hint {
    text-align: center;
    padding: 20rpx 0;
    font-size: 26rpx;
    color: #a5ddb9;
  }
  
  .popup-footer {
    padding: 20rpx 30rpx;
    display: flex;
    justify-content: space-between;
    align-items: center;
    border-top: 1rpx solid #f5f5f5;
    
    .select-mode-container {
      display: flex;
      background-color: #f5f5f5;
      border-radius: 30rpx;
      overflow: hidden;
      
      .select-mode {
        padding: 10rpx 30rpx;
        font-size: 28rpx;
        color: #666;
        
        &.active {
          background-color: #a5ddb9;
          color: #fff;
        }
      }
    }
    
    .confirm-btn {
      padding: 10rpx 30rpx;
      background-color: #a5ddb9;
      color: #fff;
      font-size: 28rpx;
      border-radius: 30rpx;
    }
  }
}

/* 记账页面设置弹窗样式 */
.booking-page-popup {
  width: 650rpx;
  height: 900rpx;
  background-color: #fff;
  border-radius: 24rpx;
  overflow: hidden;
  display: flex;
  flex-direction: column;

  .popup-header {
    padding: 30rpx;
    text-align: center;
    position: relative;
    border-bottom: 1rpx solid #eee;

    .close-btn {
      position: absolute;
      left: 20rpx;
      top: 50%;
      transform: translateY(-50%);
    }

    .popup-title {
      font-size: 32rpx;
      font-weight: bold;
      margin: 0 auto;
    }
  }

  .popup-content {
    flex: 1;
    height: 820rpx;

    .setting-section {
      padding: 20rpx 0;

      .section-title {
        padding: 10rpx 30rpx;
        font-size: 28rpx;
        color: #999;
        position: relative;

        &::before {
          content: "";
          position: absolute;
          left: 15rpx;
          top: 15rpx;
          width: 6rpx;
          height: 30rpx;
          background-color: #a5ddb9;
        }
      }

      .setting-item {
        display: flex;
        justify-content: space-between;
        align-items: flex-start;
        padding: 20rpx 30rpx;
        border-bottom: 1rpx solid #f5f5f5;

        .item-left {
          flex: 1;
          padding-right: 20rpx;

          .item-label {
            font-size: 28rpx;
            color: #333;
            margin-bottom: 8rpx;
          }

          .item-desc {
            font-size: 24rpx;
            color: #999;
            line-height: 1.4;
          }
        }

        .item-value {
          display: flex;
          align-items: center;

          text {
            font-size: 26rpx;
            color: #999;
            margin-right: 10rpx;
          }

          switch {
            transform: scale(0.8);
            margin-right: -10rpx;
          }
        }
      }
    }
  }
}

/* 资产账户选择弹窗样式 */
.asset-selector-popup {
  background-color: #fff;
  border-radius: 24rpx 24rpx 0 0;
  max-height: 80vh;
  display: flex;
  flex-direction: column;

  .popup-header {
    position: relative;
    padding: 30rpx 0;
    text-align: center;
    border-bottom: 1rpx solid #f0f0f0;

    .close-btn {
      position: absolute;
      left: 30rpx;
      top: 30rpx;
    }

    .popup-title {
      font-size: 34rpx;
      font-weight: 500;
    }
  }

  .asset-account-list {
    padding: 20rpx 30rpx;
    max-height: 60vh;
    overflow-y: auto;

    .asset-account-item {
      display: flex;
      align-items: center;
      padding: 30rpx 0;
      border-bottom: 1rpx solid #f5f5f5;
      position: relative;

      &:last-child {
        border-bottom: none;
      }

      &.selected {
        background-color: #f8f8f8;
      }

      .account-icon {
        width: 80rpx;
        height: 80rpx;
        display: flex;
        align-items: center;
        justify-content: center;
        margin-right: 20rpx;

        image {
          width: 60rpx;
          height: 60rpx;
        }
      }

      .account-info {
        flex: 1;

        .account-name {
          font-size: 32rpx;
          margin-bottom: 6rpx;
        }

        .account-desc, .account-balance {
          font-size: 26rpx;
          color: #999;
        }
      }

      .select-indicator {
        margin-left: 20rpx;
      }
    }
  }

  .popup-footer {
    display: flex;
    border-top: 1rpx solid #f0f0f0;
    height: 100rpx;

    .footer-btn {
      flex: 1;
      display: flex;
      align-items: center;
      justify-content: center;
      font-size: 32rpx;
    }

    .cancel-btn {
      color: #666;
      background-color: #f5f5f5;
    }

    .confirm-btn {
      color: #fff;
      background-color: #a5ddb9;
    }
  }
}

/* 分类样式弹层 */
.category-style-popup {
  background-color: #fff;
  border-radius: 24rpx 24rpx 0 0;
  overflow: hidden;
  
  .popup-header {
    position: relative;
    padding: 30rpx 0;
    text-align: center;
    border-bottom: 1rpx solid #f0f0f0;
    
    .close-btn {
      position: absolute;
      left: 30rpx;
      top: 50%;
      transform: translateY(-50%);
    }
    
    .popup-title {
      font-size: 34rpx;
      font-weight: 500;
    }
  }
  
  .style-list {
    padding: 0;
    
    .style-item {
      display: flex;
      align-items: center;
      justify-content: space-between;
      padding: 30rpx 40rpx;
      border-bottom: 1rpx solid #f5f5f5;
      
      &:last-child {
        border-bottom: none;
      }
      
      .item-label {
        font-size: 30rpx;
        color: #333;
      }
      
      .item-arrow {
        color: #999;
      }
    }
  }
}

/* 分类显示行数选择弹层 */
.rows-selector-popup {
  width: 650rpx;
  background-color: #fff;
  border-radius: 24rpx;
  overflow: hidden;
  
  .popup-header {
    position: relative;
    padding: 30rpx 0;
    text-align: center;
    border-bottom: 1rpx solid #f0f0f0;
    
    .close-btn {
      position: absolute;
      left: 30rpx;
      top: 50%;
      transform: translateY(-50%);
    }
    
    .popup-title {
      font-size: 34rpx;
      font-weight: 500;
    }
  }
  
  .rows-grid {
    padding: 30rpx;
    
    .rows-row {
      display: flex;
      justify-content: space-between;
      margin-bottom: 30rpx;
      
      &:last-child {
        margin-bottom: 0;
      }
      
      .row-cell {
        width: 70rpx;
        height: 70rpx;
        display: flex;
        align-items: center;
        justify-content: center;
        font-size: 30rpx;
        color: #333;
        border-radius: 50%;
        
        &.selected {
          background-color: #a5ddb9;
          color: #fff;
        }
      }
    }
  }
  
  .popup-footer {
    display: flex;
    border-top: 1rpx solid #f0f0f0;
    
    .cancel-btn, .confirm-btn {
      flex: 1;
      height: 90rpx;
      display: flex;
      align-items: center;
      justify-content: center;
      font-size: 30rpx;
    }
    
    .cancel-btn {
      color: #666;
      background-color: #f5f5f5;
    }
    
    .confirm-btn {
      color: #fff;
      background-color: #a5ddb9;
    }
  }
}

/* 日期类型选择弹层 */
.date-type-popup {
  background-color: #fff;
  border-radius: 24rpx 24rpx 0 0;
  overflow: hidden;
  
  .popup-header {
    position: relative;
    padding: 30rpx 0;
    text-align: center;
    border-bottom: 1rpx solid #f0f0f0;
    
    .close-btn {
      position: absolute;
      left: 30rpx;
      top: 50%;
      transform: translateY(-50%);
    }
    
    .popup-title {
      font-size: 34rpx;
      font-weight: 500;
    }
  }
  
  .date-type-list {
    padding: 0;
    
    .date-type-item {
      position: relative;
      padding: 30rpx 40rpx;
      border-bottom: 1rpx solid #f5f5f5;
      
      &:last-child {
        border-bottom: none;
      }
      
      .type-label {
        font-size: 32rpx;
        color: #333;
        margin-bottom: 10rpx;
      }
      
      .type-desc {
        font-size: 28rpx;
        color: #666;
        margin-bottom: 6rpx;
      }
      
      .type-hint {
        font-size: 24rpx;
        color: #999;
        margin-right: 40rpx;
      }
      
      .item-arrow {
        position: absolute;
        right: 30rpx;
        top: 50%;
        transform: translateY(-50%);
      }
    }
  }
}

/* 记账功能弹层 */
.feature-popup {
  width: 650rpx;
  background-color: #fff;
  border-radius: 24rpx;
  overflow: hidden;
  
  .popup-header {
    position: relative;
    padding: 30rpx 0;
    text-align: center;
    border-bottom: 1rpx solid #f0f0f0;
    
    .close-btn {
      position: absolute;
      left: 30rpx;
      top: 50%;
      transform: translateY(-50%);
    }
    
    .popup-title {
      font-size: 34rpx;
      font-weight: 500;
    }
  }
  
  .feature-list {
    padding: 0;
    max-height: 70vh;
    overflow-y: auto;
    
    .feature-item {
      display: flex;
      justify-content: space-between;
      align-items: center;
      padding: 30rpx 40rpx;
      border-bottom: 1rpx solid #f5f5f5;
      
      &:last-child {
        border-bottom: none;
      }
      
      .feature-info {
        flex: 1;
        
        .feature-name {
          font-size: 30rpx;
          color: #333;
          margin-bottom: 6rpx;
        }
        
        .feature-desc {
          font-size: 24rpx;
          color: #999;
        }
      }
      
      .feature-switch {
        switch {
          transform: scale(0.9);
        }
      }
    }
  }
}