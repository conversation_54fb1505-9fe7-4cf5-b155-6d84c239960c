<view class="login-container">
  <!-- 欢迎标题 -->
  <view class="welcome-title">欢迎来到小青账</view>
  
  <!-- 登录图示 -->
  <view class="login-illustration">
    <!-- 左侧装饰元素 -->
    <view class="decoration left-decoration">
      <view class="dot-group">
        <view class="dot"></view>
        <view class="dot"></view>
        <view class="line"></view>
      </view>
    </view>
    
    <!-- 中间登录卡片 -->
    <view class="login-card">
      <view class="avatar-container">
        <view class="avatar-circle">
          <image src="/static/icon/default_avatar.png" mode="aspectFill"></image>
        </view>
      </view>
      <view class="card-elements">
        <view class="element element-1"></view>
        <view class="element element-2"></view>
        <view class="element element-3"></view>
        <view class="element element-4"></view>
        <view class="element element-5"></view>
      </view>
      <view class="card-dots">
        <view class="dot dot-1"></view>
        <view class="dot dot-2"></view>
        <view class="dot dot-3"></view>
        <view class="dot dot-4"></view>
      </view>
    </view>
    
    <!-- 右侧装饰元素 -->
    <view class="decoration right-decoration">
      <view class="color-beads">
        <view class="bead bead-1"></view>
        <view class="bead bead-2"></view>
        <view class="bead bead-3"></view>
        <view class="bead bead-4"></view>
      </view>
    </view>
  </view>
  
  <!-- 登录按钮区域 -->
  <view class="login-buttons">
    <button class="login-btn wechat-login" bindtap="wechatLogin">微信登录</button>
    <button class="login-btn phone-login" bindtap="phoneLogin">手机号登录</button>
  </view>
  
  <!-- 隐私政策 -->
  <view class="privacy-policy">
    <checkbox-group bindchange="checkboxChange">
      <label class="checkbox">
        <checkbox value="agree" checked="{{isAgree}}" color="#8dc63f" />
        <text>我已阅读并同意</text>
        <text class="policy-link" bindtap="showPrivacyPolicy">《隐私政策》</text>
      </label>
    </checkbox-group>
  </view>
</view>