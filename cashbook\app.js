// app.js
App({
  onLaunch() {

    // 定义默认主题颜色
    const defaultColor = '#a2b486';

    wx.getStorage({
      key: 'selectedColor',
      success: (res) => {
        // 如果本地存储中有值，使用该值
        console.log('read selectedColor from local storage');

        this.globalData.selectedColor = res.data;
      },
      fail: () => {
        console.log('读取本地存储失败');

        // 如果读取失败，使用默认颜色并保存到本地存储
        this.globalData.selectedColor = defaultColor;

        // 将默认颜色保存到本地存储
        wx.setStorage({
          key: 'selectedColor',
          data: defaultColor,
          success: () => {
            console.log('默认主题颜色已保存到本地存储');
          },
          fail: (err) => {
            console.error('保存默认主题颜色失败', err);
          }
        });
      }
    });

    // 登录
    wx.login({
      success: () => {
        // 发送 res.code 到后台换取 openId, sessionKey, unionId
        // 如果需要使用 res.code，请取消注释下面的代码
        // console.log('登录成功，code:', res.code);
      }
    })


    // 获取手机的菜单栏和导航栏信息
    try {
      // 使用 wx.getMenuButtonBoundingClientRect() 获取胶囊按钮位置信息
      const menuButtonInfo = wx.getMenuButtonBoundingClientRect();
      // console.log(menuButtonInfo, 'menuButtonInfo');

      // 计算导航栏高度
      // 通常导航栏高度 = 胶囊按钮底部到窗口顶部的距离 + 一些额外的边距
      // 这是导航栏的高度
      const navBarHeight = menuButtonInfo.bottom + 4; // 4px 是额外的边距
      // console.log(navBarHeight, 'navBarHeight');


      // 设置全局变量
      this.globalData.Navigation = navBarHeight;
      this.globalData.menuButtonInfo = menuButtonInfo;

      // console.log('导航栏高度:', navBarHeight, '菜单按钮信息:', menuButtonInfo);
    } catch (error) {
      // console.error('获取导航栏信息失败:', error);

      // 如果获取失败，使用默认值
      this.globalData.Navigation = 44; // 默认导航栏高度
    }

    // 获取系统信息
    wx.getSystemInfo({
      success: (res) => {
        // console.log('系统信息:', res.safeArea);
        this.globalData.windowHeight = res.safeArea.top; // 设置导航栏高度为状态栏+标题栏的高度
      },
      fail: (err) => {
        console.error('获取系统信息失败', err);
      }
    });
  },
  globalData: {
    showCalendar: false,
    selectedColor: '#a2b486',
    // 通过导航栏高度
    Navigation: '',
    // 通过getSystemInfo获取的导航栏的高度，
    windowHeight: '',
    menuButtonInfo: null,
    statusBarHeight: 0,
    // 定义全局的图片的URL
    BASE_URL: 'https://www.520it.com/',
  },
  onShow() {
    wx.hideShareMenu({
      success: () => {
        console.log('当前页面显示分享按钮成功');
      },
      fail: (err) => {
        console.error('当前页面显示分享按钮失败', err);
      }
    });
  }
})
