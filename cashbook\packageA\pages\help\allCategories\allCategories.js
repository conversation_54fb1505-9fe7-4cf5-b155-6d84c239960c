import { getArticle_category } from '../../../../api/basic/index';

Page({
  /**
   * 页面的初始数据
   */
  data: {
    categories: [],
    loading: true
  },

  /**
   * 生命周期函数--监听页面加载
   */
  onLoad() {
    // 设置页面标题
    wx.setNavigationBarTitle({
      title: '全部分类'
    });
    
    // 加载所有文章分类
    this.loadAllCategories();
  },

  /**
   * 加载所有文章分类
   */
  async loadAllCategories() {
    try {
      wx.showLoading({
        title: '加载中...',
      });
      
      // 请求文章分类数据
      const result = await getArticle_category({});
      
      // 处理结果
      if (result && result.data) {
        // 对分类进行排序和分组
        const sortedCategories = this.processCategoriesData(result.data);
        
        this.setData({
          categories: sortedCategories,
          loading: false
        });
      } else {
        this.setData({
          loading: false
        });
      }
    } catch (error) {
      console.error('加载分类失败:', error);
      this.setData({
        loading: false
      });
      
      wx.showToast({
        title: '加载分类失败',
        icon: 'none'
      });
    } finally {
      wx.hideLoading();
      wx.stopPullDownRefresh();
    }
  },
  
  /**
   * 处理分类数据
   */
  processCategoriesData(categories) {
    if (!categories || categories.length === 0) return [];
    
    // 按照某种标准排序
    return categories.sort((a, b) => {
      // 优先按照顺序号排序（如果有的话）
      if (a.order !== undefined && b.order !== undefined) {
        return a.order - b.order;
      }
      
      // 否则按照名称字母排序
      return a.name.localeCompare(b.name);
    });
  },

  /**
   * 查看分类文章
   */
  viewCategory(e) {
    const categoryId = e.currentTarget.dataset.id;
    const categoryName = e.currentTarget.dataset.name;
    
    if (categoryId) {
      wx.navigateTo({
        url: `/packageA/pages/help/articleCategory/articleCategory?id=${categoryId}`
      });
    }
  },

  /**
   * 返回上一页
   */
  goBack() {
    wx.navigateBack();
  },

  /**
   * 页面相关事件处理函数--监听用户下拉动作
   */
  onPullDownRefresh() {
    this.loadAllCategories();
  }
}) 