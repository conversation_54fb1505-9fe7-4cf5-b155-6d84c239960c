/* 收支分类页面样式 */
.category-container {
  min-height: 100vh;
  background-color: #f8f8f8;
}

/* 顶部导航栏 */
.nav-bar {
  display: flex;
  align-items: center;
  justify-content: space-between;
  position: fixed;
  top: 0;
  left: 0;
  right: 0;
  background-color: #59b5dd;
  padding: 0 30rpx;
  z-index: 100;
  box-shadow: 0 2rpx 10rpx rgba(0, 0, 0, 0.05);
}

.back-icon {
  padding: 10rpx;
}

.tab-container {
  display: flex;
  margin-left: 20rpx;
  background-color: #2a4057;
  border-radius: 40rpx;
  padding: 6rpx;
}

.tab {
  position: relative;
  padding: 12rpx 30rpx;
  font-size: 28rpx;
  color: #fff;
  border-radius: 34rpx;
  text-align: center;
}

.tab.active {
  background-color: #fff;
  color: #333;
  font-weight: 500;
}

.book-selector {
  display: flex;
  align-items: center;
  font-size: 24rpx;
  color: #fff;
}

.book-selector text {
  margin-left: 10rpx;
  max-width: 150rpx;
  overflow: hidden;
  text-overflow: ellipsis;
  white-space: nowrap;
}

/* 内容区域 */
.content-area {
  width: 100%;
  box-sizing: border-box;
}

/* 分类列表 */
.category-list {
  padding-bottom: 120rpx;
}

/* 分类卡片 */
.category-card {
  border-radius: 12rpx;
  overflow: hidden;
  background-color: #fff;
  box-shadow: 0 2rpx 8rpx rgba(0, 0, 0, 0.05);
}

.category-item {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 25rpx 30rpx;
  width: 100%;
  box-sizing: border-box;
}

.item-left {
  display: flex;
  align-items: center;
}

.category-icon {
  width: 80rpx;
  height: 80rpx;
  border-radius: 35%;
  background-color: #f5f5f5;
  display: flex;
  justify-content: center;
  align-items: center;
  margin-right: 20rpx;
  margin-left: 20rpx;
  overflow: hidden;
}

.category-icon image {
  width: 48rpx;
  height: 48rpx;
}

.category-name {
  font-size: 30rpx;
  color: #333;
}

.item-right {
  padding: 20rpx;
  min-width: 60rpx;
  min-height: 60rpx;
  display: flex;
  justify-content: center;
  align-items: center;
  position: relative;
  z-index: 10;
}

/* 箭头图标样式 */
.arrow-icon {
  margin-right: 10rpx;
  display: inline-block;
  transform-origin: center center;
  vertical-align: middle;
}

/* 子分类容器 */
.subcategory-container {
  border-top: 1rpx solid #f5f5f5;
  overflow: hidden;
  max-height: 0;
  opacity: 0;
  transition: max-height 0.3s ease-out, opacity 0.3s ease-out;
}

/* 展开状态 */
.subcategory-container.expanded {
  max-height: 1000rpx;
  opacity: 1;
}

/* 收起状态 */
.subcategory-container.collapsed {
  max-height: 0;
  opacity: 0;
}

/* 添加按钮 */
.add-button {
  position: fixed;
  bottom: 20px;
  left: 10px;
  right: 10px;
  height: 100rpx;
  border-radius: 50rpx;
  background-color: #59b5dd;
  color: #000000;
  display: flex;
  justify-content: center;
  align-items: center;
  font-size: 32rpx;
  font-weight: 500;
}

/* 空状态提示 */
.empty-tip {
  padding: 100rpx 0;
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
}

.empty-image {
  width: 200rpx;
  height: 200rpx;
  margin-bottom: 30rpx;
}

.empty-text {
  font-size: 28rpx;
  color: #999;
}

/* 子分类网格样式 */
.subcategory-grid {
  display: grid;
  grid-template-columns: repeat(5, 1fr);
  gap: 20rpx;
  padding: 30rpx 10rpx;
  background-color: #ffffff;
}

.subcategory-item {
  display: flex;
  flex-direction: column;
  align-items: center;
  padding: 10rpx;
}

.subcategory-icon {
  width: 80rpx;
  height: 80rpx;
  border-radius: 35%;
  background-color: #f5f5f5;
  display: flex;
  justify-content: center;
  align-items: center;
  margin-bottom: 10rpx;
  overflow: hidden;
  box-shadow: 0 2rpx 8rpx rgba(0, 0, 0, 0.05);
}

.subcategory-icon image {
  width: 50rpx;
  height: 50rpx;
}

.subcategory-name {
  font-size: 24rpx;
  color: #333;
  text-align: center;
  width: 100%;
  overflow: hidden;
  text-overflow: ellipsis;
  white-space: nowrap;
}

.add-icon {
  width: 80rpx;
  height: 80rpx;
  border-radius: 35%;
  background-color: #f5f5f5;
  display: flex;
  justify-content: center;
  align-items: center;
  margin-bottom: 10rpx;
  box-shadow: 0 2rpx 8rpx rgba(0, 0, 0, 0.05);
}

/* 媒体查询，小屏幕设备显示4列 */
@media screen and (max-width: 375px) {
  .subcategory-grid {
    grid-template-columns: repeat(4, 1fr);
  }
}

/* 媒体查询，超小屏幕设备显示3列 */
@media screen and (max-width: 320px) {
  .subcategory-grid {
    grid-template-columns: repeat(3, 1fr);
  }
}

/* 更多选项弹出层样式 */
.more-options-list {
  padding: 0;
  background-color: #f8f8f8;
}

.more-option-item {
  display: flex;
  align-items: center;
  justify-content: space-between;
  padding: 24rpx 30rpx;
  border-bottom: 1rpx solid #eee;
  background-color: #fff;
  transition: background-color 0.2s ease;
}

.more-option-item:active {
  background-color: #f8f8f8;
}

.option-left {
  display: flex;
  align-items: center;
  flex: 1;
}

.option-icon {
  width: 40rpx;
  height: 40rpx;
  margin-right: 20rpx;
  display: flex;
  justify-content: center;
  align-items: center;
}

.option-icon image {
  width: 100%;
  height: 100%;
}

.option-content {
  flex: 1;
}

.option-title {
  font-size: 30rpx;
  color: #333;
  margin-bottom: 6rpx;
}

.option-desc {
  font-size: 24rpx;
  color: #999;
  line-height: 1.4;
}

.option-arrow {
  margin-left: 20rpx;
}

/* 删除选项特殊样式 */
.delete-item {
  background-color: #fff;
}

.delete-item .option-title {
  color: #ff5a5a;
}

/* 分类初始化弹出层样式 */
.more-menu-list {
  padding: 0;
  background-color: #f8f8f8;
}

.more-menu-item {
  display: flex;
  align-items: center;
  justify-content: space-between;
  padding: 24rpx 30rpx;
  border-bottom: 1rpx solid #eee;
  background-color: #fff;
  transition: background-color 0.2s ease;
}

.more-menu-item:last-child {
  border-bottom: none;
}

.more-menu-item:active {
  background-color: #f8f8f8;
}

.menu-item-left {
  display: flex;
  align-items: center;
  flex: 1;
}

.menu-item-icon {
  width: 40rpx;
  height: 40rpx;
  margin-right: 20rpx;
  display: flex;
  justify-content: center;
  align-items: center;
}

.menu-item-icon image {
  width: 100%;
  height: 100%;
}

.menu-item-content {
  flex: 1;
}

.menu-item-title {
  font-size: 30rpx;
  color: #333;
  margin-bottom: 6rpx;
}

.menu-item-desc {
  font-size: 24rpx;
  color: #999;
  line-height: 1.4;
}

.menu-item-arrow {
  margin-left: 20rpx;
}

/* 分类图标中的文本图标 */
.category-icon .text-icon {
  font-size: 32rpx;
  color: #333;
  width: 100%;
  height: 100%;
  display: flex;
  justify-content: center;
  align-items: center;
  font-weight: 500;
}

/* 分类图标中的emoji图标 */
.category-icon .emoji-icon {
  font-size: 36rpx;
  width: 100%;
  height: 100%;
  display: flex;
  justify-content: center;
  align-items: center;
}

/* 子分类图标中的文本图标 */
.subcategory-icon .text-icon {
  font-size: 28rpx;
  color: #333;
  width: 100%;
  height: 100%;
  display: flex;
  justify-content: center;
  align-items: center;
  font-weight: 500;
}

/* 子分类图标中的emoji图标 */
.subcategory-icon .emoji-icon {
  font-size: 32rpx;
  width: 100%;
  height: 100%;
  display: flex;
  justify-content: center;
  align-items: center;
}

/* 账单迁移弹窗样式 */
.migration-container {
  padding: 20rpx 30rpx 40rpx;
  background-color: #ffffff;
}

.migration-section {
  margin-bottom: 30rpx;
}

.migration-label {
  font-size: 28rpx;
  color: #666;
  margin-bottom: 16rpx;
}

/* 待迁移分类显示 */
.migration-category-display {
  border-radius: 16rpx;
  position: relative;
}

.migration-category-content {
  display: flex;
  justify-content: space-between;
  align-items: center;
  background-color: #f1f1f1;
  border-radius: 35px;
  padding: 15rpx 22rpx 15rpx 40rpx;
}

.migration-category-name {
  font-size: 36rpx;
  color: #333;
  font-weight: 500;
}

.migration-category-icon {
  width: 60rpx;
  height: 60rpx;
  border-radius: 50%;
  display: flex;
  justify-content: center;
  align-items: center;
  background-color: #fff;
}

.migration-category-icon image {
  width: 50rpx;
  height: 50rpx;
  border-radius: 50%;
}

.migration-category-icon .text-icon {
  width: 100%;
  height: 100%;
  display: flex;
  justify-content: center;
  align-items: center;
  font-size: 28rpx;
  color: #333;
  font-weight: 500;
}

.migration-category-icon .emoji-icon {
  width: 100%;
  height: 100%;
  display: flex;
  justify-content: center;
  align-items: center;
  font-size: 32rpx;
}

/* 迁移目标选择器 */
.migration-target-selector {
  border-radius: 16rpx;
  position: relative;
}

.migration-target-content {
  display: flex;
  justify-content: space-between;
  align-items: center;
  background-color: #f1f1f1;
  border-radius: 35px;
  padding: 15rpx 22rpx 15rpx 40rpx;
  min-height: 60rpx;
}

.migration-target-text {
  font-size: 36rpx;
  color: #333;
  font-weight: 500;
}

.migration-target-icon {
  width: 60rpx;
  height: 60rpx;
  border-radius: 50%;
  display: flex;
  justify-content: center;
  align-items: center;
  background-color: #fff;
}

.migration-target-icon image {
  width: 50rpx;
  height: 50rpx;
  border-radius: 50%;
}

.migration-target-icon .text-icon {
  width: 100%;
  height: 100%;
  display: flex;
  justify-content: center;
  align-items: center;
  font-size: 28rpx;
  color: #333;
  font-weight: 500;
}

.migration-target-icon .emoji-icon {
  width: 100%;
  height: 100%;
  display: flex;
  justify-content: center;
  align-items: center;
  font-size: 32rpx;
}

/* 提示文本 */
.migration-tips {
  margin: 30rpx 0;
  padding: 20rpx;
  background-color: #f8f9fa;
  border-radius: 12rpx;
  border-left: 4rpx solid #59b5dd;
}

.migration-tips text {
  font-size: 26rpx;
  color: #666;
  line-height: 1.5;
}

/* 迁移选项按钮容器 */
.migration-options {
  margin-top: 40rpx;
}

.migration-option-btn {
  padding: 24rpx 30rpx;
  border-radius: 16rpx;
  margin-bottom: 20rpx;
  transition: all 0.3s ease;
  border: 2rpx solid transparent;
}

.migration-option-btn:last-child {
  margin-bottom: 0;
}

.migration-option-btn:active {
  transform: translateY(2rpx);
}

/* 主要按钮样式（账单迁移） */
.migration-option-btn.primary {
  background: linear-gradient(135deg, #5fb2ff 0%, #4395ff 100%);
  color: white;
  box-shadow: 0 8rpx 20rpx rgba(79, 149, 255, 0.3);
}

.migration-option-btn.primary:active {
  box-shadow: 0 4rpx 12rpx rgba(79, 149, 255, 0.3);
}

/* 次要按钮样式（删除分类并迁移） */
.migration-option-btn.secondary {
  background: #fff;
  color: #ff6b6b;
  border: 2rpx solid #ff6b6b;
  box-shadow: 0 4rpx 12rpx rgba(255, 107, 107, 0.1);
}

.migration-option-btn.secondary:active {
  background: #fff5f5;
  box-shadow: 0 2rpx 8rpx rgba(255, 107, 107, 0.1);
}

.migration-option-btn .option-title {
  font-size: 32rpx;
  font-weight: 500;
  margin-bottom: 8rpx;
}

.migration-option-btn.primary .option-title {
  color: white;
}

.migration-option-btn.secondary .option-title {
  color: #ff6b6b;
}

.migration-option-btn .option-desc {
  font-size: 24rpx;
  line-height: 1.4;
  opacity: 0.8;
}

.migration-option-btn.primary .option-desc {
  color: rgba(255, 255, 255, 0.9);
}

.migration-option-btn.secondary .option-desc {
  color: #999;
}

/* 分类选择器样式 */
.category-selector-container {
  padding: 20rpx 0;
  background-color: #ffffff;
  max-height: 80vh;
  overflow-y: auto;
}

.category-selector-list {
  padding: 0 30rpx;
}

.category-selector-item {
  display: flex;
  align-items: center;
  padding: 24rpx 0;
  border-bottom: 1rpx solid #f5f5f5;
  transition: background-color 0.2s ease;
}

.category-selector-item:active {
  background-color: #f8f8f8;
}

.category-selector-item:last-child {
  border-bottom: none;
}

/* 主分类样式 */
.category-selector-item.main-category {
  font-weight: 500;
  background-color: #fafafa;
  margin-bottom: 10rpx;
  border-radius: 12rpx;
  padding: 24rpx 20rpx;
  border-bottom: none;
}

.category-selector-item.main-category .category-selector-name {
  font-size: 32rpx;
  color: #333;
  font-weight: 500;
}

/* 子分类样式 */
.subcategory-selector-list {
  margin-left: 40rpx;
  margin-bottom: 20rpx;
}

.category-selector-item.sub-category {
  padding: 20rpx 0;
  margin-left: 20rpx;
  position: relative;
}

.category-selector-item.sub-category::before {
  content: '';
  position: absolute;
  left: -20rpx;
  top: 50%;
  transform: translateY(-50%);
  width: 12rpx;
  height: 12rpx;
  background-color: #ddd;
  border-radius: 50%;
}

.category-selector-item.sub-category .category-selector-name {
  font-size: 28rpx;
  color: #666;
}

.category-selector-icon {
  width: 60rpx;
  height: 60rpx;
  border-radius: 50%;
  background-color: #f5f5f5;
  display: flex;
  justify-content: center;
  align-items: center;
  margin-right: 20rpx;
  overflow: hidden;
}

.category-selector-icon image {
  width: 40rpx;
  height: 40rpx;
}

.category-selector-icon .text-icon {
  width: 100%;
  height: 100%;
  display: flex;
  justify-content: center;
  align-items: center;
  font-size: 24rpx;
  color: #333;
  font-weight: 500;
}

.category-selector-icon .emoji-icon {
  width: 100%;
  height: 100%;
  display: flex;
  justify-content: center;
  align-items: center;
  font-size: 28rpx;
}

.category-selector-name {
  flex: 1;
  font-size: 30rpx;
  color: #333;
}
