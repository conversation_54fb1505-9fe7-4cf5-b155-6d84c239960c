<!-- packageA/pages/bookDetail/bookDetail.wxml -->
<view class="container">
  <view class="header" style="padding-top: {{windowHeight}}px;">
    <view class="close-btn" bindtap="onClose">
      <van-icon name="cross" size="20px" color="#fff" />
    </view>
  </view>
  <!-- 编辑按钮 - 移到右下角 -->
  <view class="edit-btn-corner" bindtap="onEdit">
    <van-icon name="edit" size="24px" color="#fff" />
  </view>
  <!-- 添加按钮 -->
  <view class="add-btn" style="background-color: {{selectedColor}};" bindtap="onAddRecord">
    <van-icon name="plus" />
  </view>
  <!-- 背景图 -->
  <!-- style="transform: translateY(-{{scrollY * 0.1}}px);" -->
  <image class="bg-image" src="{{bookDetail.image || '/static/images/city_skyline.jpg'}}" mode="aspectFill"></image>
  <!-- 上部区域(慢滚动部分) -->
  <!-- style="transform: translateY(-{{scrollY * 0.1}}px);" -->
  <view class="blue-section">
    <!-- 顶部安全区域 -->
    <!-- <view class="safe-area"></view> -->
    <!-- 顶部信息区 -->
    <!-- 月度统计信息 -->
    <view class="monthly-stats" style="padding-top: {{Navigation}}px;">
      <view class="stats-label">本月支出</view>
      <view class="stats-amount">¥ {{monthlyStats.expense || '0.00'}}</view>
      <view class="sub-stats">
        <view class="sub-stats-row">
          <text class="sub-stats-label">月收入</text>
          <text class="sub-stats-value">¥ {{monthlyStats.income || '0.00'}}</text>
        </view>
        <view class="sub-stats-row">
          <text class="sub-stats-label">月结余</text>
          <text class="sub-stats-value">¥ {{monthlyStats.balance || '0.00'}}</text>
        </view>
        <view class="sub-stats-row">
          <text class="sub-stats-label">预算</text>
          <view class="budget-wrapper">
            <text class="sub-stats-value">¥ {{monthlyStats.budget || '0.00'}}</text>
            <van-icon name="arrow" size="12px" color="#fff" />
          </view>
        </view>
      </view>
      <!-- 添加日期显示组件 -->
      <view class="day-display" wx:if="{{name || notes}}">
        <view class="day-icon">
          <image src="{{bookDetail.image}}" class="day-icon-image" mode="scaleToFill"></image>
        </view>
        <view class="day-info">
          <text class="day-name" wx:if="{{name}}">{{name}}</text>
          <text class="day-notes" wx:if="{{notes}}">{{notes}}</text>
        </view>
      </view>
    </view>
  </view>
  <!-- 下部区域(正常滚动部分) -->
  <!-- style="transform: translateY(-{{scrollY * 0.1}}px);" -->
  <view class="gray-section">
    <view class="content-container">
      <!-- 更新提示区 -->
      <view class="update-tip" wx:if="{{bookDetail.updates}}">
        <view class="tip-icon">
          <image src="{{bookDetail.avatar || '/static/images/avatar.png'}}" mode="aspectFill"></image>
        </view>
        <view class="tip-text">更新了 {{bookDetail.updates}}</view>
      </view>
      <!-- 标签页导航 - 使用Vant的Tabs组件 -->
      <van-tabs active="{{ activeTab }}" bind:change="onTabChange" color="{{selectedColor || '#FF7BAC'}}" title-active-color="{{selectedColor || '#FF7BAC'}}" animated swipeable sticky offset-top="{{Navigation}}">
        <van-tab title="账单" name="overview"></van-tab>
        <van-tab title="月报" name="month"></van-tab>
        <van-tab title="年报" name="year"></van-tab>
        <van-tab title="总报" name="total"></van-tab>
      </van-tabs>
      <!-- 年月选择器 -->
      <view class="year-month-selector">
        <view class="year-month-selector-item" bindtap="showMonthPicker">
          <text>{{pickerYear}}年{{pickerMonth}}月</text>
          <van-icon name="arrow-down" color="#333" size="14px" />
        </view>
      </view>
      <!-- 月报内容区域 -->
      <view class="report-content" wx:if="{{activeTab === 'month'}}">
        <!-- 数据选择选项卡 -->
        <view class="data-selector">
          <view class="selector-item {{dataType === 'expense' ? 'active' : ''}}" bindtap="setDataType" data-type="expense">
            <view class="selector-radio">
              <view class="radio-dot {{dataType === 'expense' ? 'active' : ''}}"></view>
            </view>
            <view class="selector-content">
              <text class="selector-label">月支出</text>
              <text class="selector-value">¥ {{monthlyStats.expense}}</text>
            </view>
          </view>
          <view class="selector-item {{dataType === 'income' ? 'active' : ''}}" bindtap="setDataType" data-type="income">
            <view class="selector-radio">
              <view class="radio-dot {{dataType === 'income' ? 'active' : ''}}"></view>
            </view>
            <view class="selector-content">
              <text class="selector-label">月收入</text>
              <text class="selector-value">¥ {{monthlyStats.income}}</text>
            </view>
          </view>
          <view class="selector-item {{dataType === 'other' ? 'active' : ''}}" bindtap="setDataType" data-type="other">
            <view class="selector-radio">
              <view class="radio-dot {{dataType === 'other' ? 'active' : ''}}"></view>
            </view>
            <view class="selector-content">
              <text class="selector-label">其他</text>
              <text class="selector-value">¥ {{monthlyStats.other || 0}}</text>
            </view>
          </view>
        </view>
        <!-- 饼图区域 -->
        <view class="pie-chart-container">
          <view class="pie-chart-wrapper">
            <!-- 这里是实际的环形图组件 -->
            <view class="pie-chart">
              <!-- 图表由JS渲染 -->
            </view>
            <!-- 环形图中心文本 -->
            <view class="chart-center">
              <text class="center-title">支出大类</text>
              <view class="center-icon">
                <image src="/static/icon/refresh.png" mode="aspectFit" bindtap="refreshChart"></image>
              </view>
              <text class="center-subtitle">轻点刷新</text>
            </view>
            <!-- 环形图标签 -->
            <view class="chart-labels">
              <!-- 餐饮标签 -->
              <view class="chart-label label-left">
                <text class="label-text">餐饮</text>
                <text class="label-line">——</text>
                <text class="label-percent">32.07%</text>
              </view>
              <!-- 水果标签 -->
              <view class="chart-label label-top">
                <text class="label-text">水果</text>
                <text class="label-line">|</text>
              </view>
              <!-- 能本油盐标签 -->
              <view class="chart-label label-right">
                <text class="label-percent">54.88%</text>
                <text class="label-line">——</text>
                <text class="label-text">能本油盐</text>
              </view>
            </view>
          </view>
        </view>
        <!-- 底部控制栏 -->
        <view class="chart-controls">
          <view class="control-left">
            <view class="switch-category" bindtap="toggleCategorySize">
              <image src="/static/icon/switch.png" mode="aspectFit" class="switch-icon"></image>
              <text class="switch-text">切换小类</text>
            </view>
          </view>
          <view class="control-right">
            <view class="chart-type-selector">
              <view class="type-btn {{chartType === 'pie' ? 'active' : ''}}" bindtap="setChartType" data-type="pie">
                环形
              </view>
              <view class="type-btn {{chartType === 'bar' ? 'active' : ''}}" bindtap="setChartType" data-type="bar">
                面积
              </view>
            </view>
          </view>
        </view>
        <!-- 分类支出列表 -->
        <view class="category-list">
          <view class="category-item" wx:for="{{categories}}" wx:key="id">
            <view class="category-icon" style="background-color: {{item.color}}30;">
              <image src="{{item.icon}}" mode="aspectFit"></image>
            </view>
            <view class="category-info">
              <text class="category-name">{{item.name}}</text>
              <view class="category-bar">
                <view class="bar-progress" style="width: {{item.percentage}}%; background-color: {{item.color || '#4AA0EB'}}"></view>
              </view>
            </view>
            <view class="category-amount">
              <text class="amount-value">¥ {{item.amount}}</text>
              <text class="amount-count">{{item.count}}笔</text>
            </view>
          </view>
        </view>
      </view>
      <!-- 账单明细区域 -->
      <view wx:if="{{activeTab === 'overview'}}" class="bill-overview-container">
        <!-- 账单明细标题 -->
        <view class="oneColonm">
          <view>账单明细</view>
          <view class="time" style="background-color: {{selectedColor}}30; color: {{selectedColor}};" bind:tap="toggleSort">
            {{sortType === 'amount' ? '按金额' : '按时间'}}
          </view>
        </view>
        <!-- 账单列表 -->
        <view class="bill-list">
          <!-- 空状态 -->
          <view class="empty-state" wx:if="{{hasRecords}}">
            <image class="empty-icon" src="/static/images/bill_icon.png" mode="aspectFit"></image>
            <text class="empty-text">未发现账单啊，试着记一笔~</text>
          </view>
          <!-- 账单列表 -->
          <view class="bill-list-wrapper" wx:else>
            <contentList isShowIcon="{{true}}" list="{{list}}" sortType="{{sortType}}" enableSwipe="{{true}}" leftWidth="{{0}}" rightWidth="{{180}}" />
          </view>
        </view>
      </view>
      <!-- 年报和总报内容区域 -->
      <view wx:if="{{activeTab === 'year' || activeTab === 'total'}}">
        <view class="report-placeholder">
          <text>{{activeTab === 'year' ? '年报' : '总报'}}内容</text>
        </view>
      </view>
    </view>
  </view>
  <!-- 月份选择器组件 -->
  <month-picker visible="{{showPicker}}" initYear="{{pickerYear}}" initMonth="{{pickerMonth}}" mode="{{bottomId === 1 ? 'year' : 'month'}}" bind:cancel="hideMonthPicker" bind:confirm="onMonthSelected"></month-picker>
</view>