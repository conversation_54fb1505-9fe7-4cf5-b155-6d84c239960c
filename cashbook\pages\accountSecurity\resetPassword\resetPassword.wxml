<view class="container">
  <!-- 顶部导航 -->
  <view class="nav-header">
    <view class="nav-back" bindtap="goBack">
      <image src="/static/icon/back.png" mode="aspectFit" />
    </view>
    <view class="nav-title">请设置新密码吧</view>
    <view class="nav-placeholder"></view>
  </view>

  <!-- 内容区域 -->
  <view class="content">
    <!-- 密码输入区域 -->
    <view class="password-section">
      <!-- 密码输入框 -->
      <view class="input-group">
        <input class="password-input" type="{{showPassword ? 'text' : 'password'}}" 
               placeholder="密码" bindinput="onPasswordInput" value="{{password}}"/>
        <view class="password-toggle" bindtap="togglePasswordVisibility">
          <image src="{{showPassword ? '/static/icon/eye_open.png' : '/static/icon/eye_close.png'}}" mode="aspectFit"></image>
        </view>
      </view>
      
      <!-- 重复密码输入框 -->
      <view class="input-group">
        <input class="password-input" type="{{showConfirmPassword ? 'text' : 'password'}}" 
               placeholder="重复密码" bindinput="onConfirmPasswordInput" value="{{confirmPassword}}"/>
        <view class="password-toggle" bindtap="toggleConfirmPasswordVisibility">
          <image src="{{showConfirmPassword ? '/static/icon/eye_open.png' : '/static/icon/eye_close.png'}}" mode="aspectFit"></image>
        </view>
      </view>
    </view>
  </view>

  <!-- 修改按钮 -->
  <view class="modify-btn" bindtap="resetPassword">
    <text>修改</text>
  </view>

  <!-- 底部导航栏 -->
  <view class="bottom-tabbar">
    <view class="tab-item">
      <image src="/static/icon/menu.png" mode="aspectFit"></image>
    </view>
    <view class="tab-item">
      <image src="/static/icon/home-tab.png" mode="aspectFit"></image>
    </view>
    <view class="tab-item">
      <image src="/static/icon/back.png" mode="aspectFit"></image>
    </view>
  </view>
</view>