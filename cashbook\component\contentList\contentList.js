// component/contentList/contentList.js
Component({

  /**
   * 组件的属性列表
   */
  properties: {
    time: {
      type: String
    },
    list: {
      type: Array
    },
    sortType: {
      type: String,
      value: 'time' // 默认按时间排序，可选值: 'time', 'amount'
    }
  },

  observers: {
    'sortType': function (newType) {
      // 当sortType属性变化时，更新排序方式
      console.log('sortType changed to:', newType);
      this.updateDisplayList(newType);
    },
    
    'list': function(newList) {
      // 当外部传入list变化时，更新显示列表
      if (newList && newList.length > 0) {
        this.setData({ dataList: newList });
        this.updateDisplayList(this.properties.sortType);
      }
    }
  },

  /**
   * 组件的初始数据
   */
  data: {
    current: 0,
    displayList: [], // 用于显示的列表
    dataList: [      // 统一的数据列表
      {
        isTransfer: true,
        time: '5月10日',
        istoday: '今天',
        desc: '【资产初始化】初期余额100.0',
        amount: '100',
        remark: '豪华地区哦',
        payAccount: '100.00',
        date: '2023-05-10'
      },
      {
        isTransfer: true,
        time: '5月10日',
        istoday: '今天',
        desc: '【资产初始化】初期余额100.0',
        amount: '100',
        remark: '豪华地区哦',
        payAccount: '100.00',
        date: '2023-05-10'
      },
      {
        isTransfer: true,
        time: '5月10日',
        istoday: '今天',
        desc: '【资产初始化】初期余额100.0',
        amount: '100',
        remark: '豪华地区哦',
        payAccount: '100.00',
        date: '2023-05-10'
      }
    ]
  },

  /**
   * 组件的方法列表
   */
  methods: {
    // 更新显示列表
    updateDisplayList(sortType) {
      if (sortType === 'amount') {
        // 按金额排序 - 直接显示原始列表，可能需要进行排序
        const sortedList = [...this.data.dataList].sort((a, b) => {
          // 将金额转换为数字进行比较（去除逗号和￥符号）
          const amountA = parseFloat(a.amount.replace(/,/g, ''));
          const amountB = parseFloat(b.amount.replace(/,/g, ''));
          return amountB - amountA; // 降序排列
        });
        
        // 确保金额格式统一
        const formattedList = sortedList.map(item => {
          const formattedAmount = this.formatAmount(item.amount);
          return {
            ...item,
            // 原始amount保留，添加新的格式化显示字段
            amountDisplay: formattedAmount
          };
        });
        
        this.setData({
          displayList: formattedList
        });
      } else {
        // 按时间排序 - 需要按日期分组
        const groupedByDate = this.groupByDate(this.data.dataList);
        
        // 确保金额格式统一
        const formattedList = groupedByDate.map(item => {
          const formattedAmount = this.formatAmount(item.amount);
          return {
            ...item,
            // 原始amount保留，添加新的格式化显示字段
            amountDisplay: formattedAmount
          };
        });
        
        this.setData({
          displayList: formattedList
        });
      }
    },
    
    // 格式化金额，确保显示一致
    formatAmount(amount) {
      if (!amount) return '0.00';
      
      // 移除非数字字符
      const cleanAmount = amount.toString().replace(/[^0-9.-]/g, '');
      const num = parseFloat(cleanAmount);
      
      // 不使用toLocaleString，直接格式化为固定格式
      // 保留2位小数，不使用千分位分隔符
      const formattedNum = num.toFixed(2);
      
      // 对于大额数字，截断小数部分显示
      if (num >= 1000000) {
        return Math.floor(num).toString();
      } else if (num >= 1000) {
        return formattedNum;
      } else {
        return formattedNum;
      }
    },
    
    // 按日期对数据进行分组
    groupByDate(list) {
      // 这里简化实现，因为当前数据已经按日期排序
      // 如果需要真正的分组，可以使用以下代码
      return list;
      
      /* 
      // 真正的分组实现（如果数据来源更复杂时使用）
      const groups = {};
      list.forEach(item => {
        const date = item.time;
        if (!groups[date]) {
          groups[date] = {
            time: date,
            istoday: item.istoday,
            payAccount: 0,
            items: []
          };
        }
        groups[date].items.push(item);
        // 累计payAccount
        const amount = parseFloat(item.amount.replace(/,/g, ''));
        groups[date].payAccount += amount;
      });
      
      // 转换为数组并排序
      return Object.values(groups).sort((a, b) => {
        // 按日期排序
        return new Date(b.date) - new Date(a.date);
      });
      */
    },
    
    toggele() {
      this.triggerEvent('Byacoumt', 1)
      this.setData({
        current: 1
      })
    },
    toggele2() {
      this.triggerEvent('Bytime', 0)
      this.setData({
        current: 0
      })
    }
  },

  // 生命周期函数
  lifetimes: {
    attached() {
      // 组件在视图层布局完成后执行
      this.updateDisplayList(this.properties.sortType);
    }
  }
})