// component/contentList/contentList.js
Component({

  /**
   * 组件的属性列表
   */
  properties: {
    time: {
      type: String
    },
    list: {
      type: Array
    },
    sortType: {
      type: String,
      value: 'time' // 默认按时间排序，可选值: 'time', 'amount'
    },
    // 添加滑动相关属性
    enableSwipe: {
      type: Boolean,
      value: false // 默认不启用滑动
    },
    leftWidth: {
      type: Number,
      value: 0 // 左侧滑动区域宽度
    },
    rightWidth: {
      type: Number,
      value: 0 // 右侧滑动区域宽度
    },
    actions: {
      type: Array,
      value: [] // 滑动操作按钮配置
    },
    isShowIcon: {
      type: Boolean,
      value: false // 是否显示图标
    }
  },

  observers: {
    'sortType': function (newType) {
      // 当sortType属性变化时，更新排序方式
      console.log('sortType changed to:', newType);
      this.updateDisplayList(newType);
    },

    'list': function (newList) {
      console.log('contentList收到的list数据:', newList);
      // 当外部传入list变化时，更新显示列表
      if (newList && newList.length > 0) {
        this.setData({ dataList: newList });
        this.updateDisplayList(this.properties.sortType);
      }
    }
  },

  /**
   * 组件的初始数据
   */
  data: {
    showSharePopup: false,
    billData: {
      expenseCount: 1,
      expenseTotal: 20.00,
      incomeCount: 1,
      incomeTotal: 0.00,
      date: '5月24日',
      items: [

      ]
    },
    cardFormat: 'frog', // 默认样式：小青账
    current: 0,
    displayList: [], // 用于显示的列表
    dataList: [      // 统一的数据列表
      {
        isTransfer: true,
        time: '5月10日',
        istoday: '今天',
        desc: '【资产初始化】初期余额100.0',
        amount: '100',
        remark: '信用卡',
        payAccount: '100.00',
        date: '2023-05-10',
        icon: '/static/icon/jr.png',
        title: '内部转账'
      },
      {
        isTransfer: true,
        time: '5月10日',
        istoday: '今天',
        desc: '【资产初始化】初期余额100.0',
        amount: '500',
        remark: '邮政',
        payAccount: '100.00',
        date: '2023-05-10',
        icon: '/static/icon/jr.png',
        title: '内部转账'
      },
      {
        isTransfer: true,
        time: '5月10日',
        istoday: '今天',
        desc: '收债',
        amount: '100',
        remark: '',
        payAccount: '100.00',
        date: '2023-05-10',
        icon: '/static/icon/jr.png',
        title: '收债'
      }
    ],
    // 滑动状态相关数据
    swiping: false, // 是否正在滑动
    swipingItemId: '', // 当前滑动项的ID
    x: 0, // 滑动位置
    startX: 0, // 开始滑动时的位置

    // 分享卡片相关数据
    showAddBookPopup: false, // 更改弹窗控制变量
    currentColorIndex: 0,
    storedColors: {},
    selectedColor: '', // 强调色，将从本地存储加载
    cardFormat: 'frog', // 默认使用青蛙样式：'frog', 'avatar', 'none'
    formatPopupVisible: false, // 样式选择弹窗是否可见
  },

  /**
   * 组件的方法列表
   */
  methods: {
    // 开始滑动
    handleTouchStart(e) {
      if (!this.data.enableSwipe) return;

      const itemId = e.currentTarget.dataset.id;
      this.setData({
        swiping: true,
        swipingItemId: itemId,
        startX: e.touches[0].clientX
      });
    },

    // 滑动中
    handleTouchMove(e) {
      if (!this.data.swiping) return;

      const moveX = e.touches[0].clientX;
      const offsetX = moveX - this.data.startX;

      // 限制滑动范围
      let x = offsetX;
      if (offsetX > 0 && this.data.leftWidth === 0) {
        x = 0; // 如果没有左侧按钮，禁止右滑
      } else if (offsetX < 0 && this.data.rightWidth === 0) {
        x = 0; // 如果没有右侧按钮，禁止左滑
      }

      // 更新滑动位置
      this.setData({ x });
    },

    // 结束滑动
    handleTouchEnd() {
      if (!this.data.swiping) return;

      let finalX = 0;

      // 根据滑动方向和距离确定最终位置
      if (this.data.x < -50 && this.data.rightWidth > 0) {
        // 左滑超过阈值，显示右侧操作按钮
        finalX = -this.data.rightWidth;
      } else if (this.data.x > 50 && this.data.leftWidth > 0) {
        // 右滑超过阈值，显示左侧操作按钮
        finalX = this.data.leftWidth;
      }

      // 更新最终位置
      this.setData({
        swiping: false,
        x: finalX
      });
    },

    // 重置滑动状态
    resetSwipe() {
      this.setData({
        swiping: false,
        swipingItemId: '',
        x: 0
      });
    },

    // 处理 van-swipe-cell 的点击事件
    onCellClick(e) {
      const { position, instance } = e.detail;

      // 关闭单元格
      instance.close();
    },

    // 隐藏账单分享卡片
    hideBillShareCard() {
      this.setData({
        showAddBookPopup: false
      });
    },

    // 处理操作按钮点击
    handleActionClick(e) {
      const { action, id } = e.currentTarget.dataset;

      // 触发操作事件
      this.triggerEvent('actionclick', {
        action,
        itemId: id
      });

      console.log('点击了操作按钮:', action, '，对应项ID:', id);


      // 关闭所有打开的单元格
      // 注意：这里无法直接访问实例，可以考虑通过其他方式关闭
      // 比如使用selectedKeys或更新视图等方式
    },

    // 取消按钮的选择
    hideShareCard() {
      this.setData({
        showSharePopup: false
      });
    },

    // 更新显示列表
    updateDisplayList(sortType) {
      if (sortType === 'amount') {
        // 按金额排序 - 直接显示原始列表，按金额从大到小排序
        const sortedList = [...this.data.dataList].sort((a, b) => {
          // 将金额转换为数字进行比较（去除逗号和￥符号）
          const amountA = parseFloat(a.amount.toString().replace(/[^\d.-]/g, ''));
          const amountB = parseFloat(b.amount.toString().replace(/[^\d.-]/g, ''));
          return Math.abs(amountB) - Math.abs(amountA); // 按绝对值降序排列
        });

        // 统一处理数据格式
        const formattedList = sortedList.map(item => {
          // 格式化金额显示
          const formattedAmount = this.formatAmount(item.amount);

          // 提取标题 - 如果desc包含【】格式，则提取【】之外的部分作为标题
          let title = item.title || '';
          if (!title && item.desc) {
            const match = item.desc.match(/【(.+?)】(.*)/);
            if (match) {
              title = match[2].trim() || match[1].trim();
            } else {
              title = item.desc.trim();
            }
          }

          // 提取账户信息
          const fromAccount = item.fromAccount || '';
          const toAccount = item.toAccount || item.remark || '';

          return {
            ...item,
            amountDisplay: formattedAmount,
            title: title || '收债',
            fromAccount: fromAccount,
            toAccount: toAccount,
            interest: item.interest || ''
          };
        });

        this.setData({
          displayList: formattedList
        });
      } else {
        // 按时间排序 - 需要按日期分组处理
        // 1. 先格式化数据并按日期排序
        let formattedItems = [...this.data.dataList].map(item => {
          const formattedAmount = this.formatAmount(item.amount);
          const fromAccount = item.fromAccount || '';
          const toAccount = item.toAccount || item.remark || '';

          return {
            ...item,
            amountDisplay: formattedAmount,
            fromAccount: fromAccount,
            toAccount: toAccount,
            interest: item.interest || '',
            // 确保有日期标识
            dateKey: item.time || (item.date ? this.formatDateToDisplay(item.date) : '未知日期')
          };
        });

        // 2. 对数据按日期分组
        const groupedData = {};
        formattedItems.forEach(item => {
          if (!groupedData[item.dateKey]) {
            groupedData[item.dateKey] = {
              date: item.dateKey,
              istoday: item.istoday,
              items: [],
              totalPay: 0
            };
          }

          // 添加到对应日期组
          groupedData[item.dateKey].items.push(item);

          // 累计支出金额
          const amount = parseFloat(item.amount.toString().replace(/[^\d.-]/g, '')) || 0;
          if (amount < 0) {
            groupedData[item.dateKey].totalPay += Math.abs(amount);
          }
        });

        // 3. 将分组数据转换为展示所需的格式
        const displayList = [];
        Object.keys(groupedData).sort((a, b) => {
          // 对日期进行排序，最新日期在前
          // 这里简化处理，假设日期格式是"月日"，如"5月21日"
          return b.localeCompare(a);
        }).forEach(dateKey => {
          const group = groupedData[dateKey];

          // 添加日期标题
          displayList.push({
            isDateHeader: true,
            time: group.date,
            istoday: group.istoday,
            payAccount: this.formatAmount(group.totalPay),
            items: group.items
          });

          // 添加该日期下的所有项目
          group.items.forEach(item => {
            displayList.push(item);
          });
        });

        this.setData({
          displayList: displayList
        });
      }
    },

    // 分享相关方法
    share() {
      const currentDate = new Date();
      const formattedDate = this.formatDateToDisplay(currentDate.toISOString().split('T')[0]);

      // 统计账单数据
      let expenseCount = 0;
      let expenseTotal = 0;
      let incomeCount = 0;
      let incomeTotal = 0;

      // 处理账单项
      const items = [];
      this.data.displayList.forEach(item => {
        if (!item.isDateHeader && item.amount) {
          const amount = parseFloat(item.amount.toString().replace(/[^\d.-]/g, ''));
          if (amount < 0) {
            expenseCount++;
            expenseTotal += Math.abs(amount);
          } else if (amount > 0) {
            incomeCount++;
            // 限制金额大小，避免显示异常大数值
            const limitedAmount = Math.min(amount, 999999);
            incomeTotal += limitedAmount;
          }

          // 添加所有账单记录
          // 限制异常大的金额值
          const rawAmount = parseFloat(item.amount);
          const limitedAmount = Math.min(Math.abs(rawAmount), 999999);

          items.push({
            icon: item.icon || '/static/icon/food.png',
            category: item.title || '餐饮',
            subcategory: item.desc || '',
            amount: limitedAmount.toFixed(2),
            tag: item.toAccount || '午餐',
            interest: item.interest || '0.00',
            fromAccount: item.fromAccount || '',
            toAccount: item.toAccount || ''
          });
        }
      });

      // 从本地存储获取颜色配置
      const storedColors = wx.getStorageSync('cardColors');

      // 确保summaryBgColor属性存在
      // if (!storedColors.theme1.backgroundColor) {
      //   storedColors.theme1.backgroundColor = 'rgba(242, 250, 254, 0.888)';
      // }

      // 设置分享数据（设置默认值）
      this.setData({
        showAddBookPopup: true, // 显示分享弹窗
        billData: {
          expenseCount: expenseCount,
          expenseTotal: expenseTotal.toFixed(2),
          incomeCount: incomeCount,
          incomeTotal: incomeTotal.toFixed(2),
          date: formattedDate,
          items: items
        },
        // 使用本地存储的颜色主题
        cardStyle: storedColors.theme1,
        storedColors: storedColors,
        btnStyle: { backgroundColor: storedColors.theme1.backgroundColor }
      });
    },

    closeAddBookPopup() {
      this.setData({
        showAddBookPopup: false
      });
    },

    // 更换卡片颜色
    onChangeColor() {
      // 获取本地存储的颜色
      const storedColors = wx.getStorageSync('cardColors')

      // 判断当前颜色是哪一种，然后切换到另一种
      const currentBgColor = this.data.cardStyle.backgroundColor;
      let newTheme;

      if (currentBgColor === storedColors.theme1.backgroundColor) {
        newTheme = storedColors.theme2;
      } else {
        newTheme = storedColors.theme1;
      }

      // 获取直接存储的selectedColor
      const directSelectedColor = wx.getStorageSync('selectedColor') || '#06866c';

      // 更新颜色
      this.setData({
        cardStyle: newTheme,
        storedColors: storedColors,
        selectedColor: directSelectedColor,
        btnStyle: { backgroundColor: newTheme.backgroundColor }
      });
    },

    // 更改卡片样式格式
    onChangeFormat() {
      this.setData({
        formatPopupVisible: true
      });
    },

    // 关闭样式选择弹窗
    onCloseFormatPopup() {
      this.setData({
        formatPopupVisible: false
      });
    },

    // 选择卡片样式
    selectFormat(e) {
      const format = e.currentTarget.dataset.format;
      this.setData({
        cardFormat: format,
        formatPopupVisible: false
      });

      wx.showToast({
        title: '样式已更新',
        icon: 'success',
        duration: 1500
      });
    },

    // 保存分享图片
    onSaveImage() {
      wx.showToast({
        title: '保存图片功能开发中',
        icon: 'none'
      });
    },

    // 将日期格式化为显示用的格式（如 "5月21日"）
    formatDateToDisplay(dateStr) {
      try {
        const date = new Date(dateStr);
        return `${date.getMonth() + 1}月${date.getDate()}日`;
      } catch (e) {
        return dateStr; // 如果转换失败，返回原始字符串
      }
    },

    // 账单项点击事件
    onBillItemTap(e) {
      console.log(e.currentTarget.dataset.type, 'e');
      this.setData({
        showPopup: true
      });
    },

    // 格式化金额，确保显示一致
    formatAmount(amount) {
      if (!amount) return '0.00';

      // 移除非数字字符，但保留小数点和负号
      let cleanAmount = amount.toString().replace(/[^\d.-]/g, '');

      // 确保是有效数字
      try {
        const num = parseFloat(cleanAmount);

        // 根据数值大小处理显示格式
        if (Math.abs(num) >= 1000) {
          return Number(num.toFixed(2)).toLocaleString();
        } else {
          return num.toFixed(2);
        }
      } catch (e) {
        // 如果解析失败，返回原始金额
        return cleanAmount;
      }
    },

    toggele() {
      this.triggerEvent('Byacoumt', 1);
      this.setData({
        current: 1
      });
    },

    toggele2() {
      this.triggerEvent('Bytime', 0);
      this.setData({
        current: 0
      });
    }
  },

  // 生命周期函数
  lifetimes: {
    attached() {
      // 组件在视图层布局完成后执行
      this.updateDisplayList(this.properties.sortType);

      // 从本地存储加载cardColors
      let storedColors = wx.getStorageSync('cardColors');
      if (!storedColors) {
        storedColors = {
          theme1: {
            backgroundColor: '#ffb6c1', // 粉色背景
            textColor: '#000000',
            summaryBgColor: 'rgba(242, 250, 254, 0.888)'
          },
          theme2: {
            backgroundColor: '#454545', // 暗黑主题
            textColor: '#ffffff',
            summaryBgColor: 'rgba(24, 50, 65, 0.8)'
          },
        };
        wx.setStorageSync('cardColors', storedColors);
      }

      // 确保storedColors中的属性存在
      if (!storedColors.theme1.textColor) {
        storedColors.theme1.textColor = '#333333';
      }

      // 确保summaryBgColor属性存在
      if (!storedColors.theme1.summaryBgColor) {
        console.log('Setting default summaryBgColor for theme1');

        storedColors.theme1.summaryBgColor = 'rgba(242, 250, 254, 0.888)';
      }
      if (!storedColors.theme2.summaryBgColor) {
        storedColors.theme2.summaryBgColor = 'rgba(24, 50, 65, 0.8)';
      }

      // 获取直接存储的selectedColor
      let directSelectedColor = wx.getStorageSync('selectedColor');
      if (!directSelectedColor) {
        directSelectedColor = '#06866c'; // 默认值
        wx.setStorageSync('selectedColor', directSelectedColor);
      }

      // 初始化样式
      this.setData({
        cardStyle: storedColors.theme1,
        storedColors: storedColors,
        selectedColor: directSelectedColor,
        btnStyle: { backgroundColor: storedColors.theme1.backgroundColor }
      });
    }
  },
});