// component/contentList/contentList.js
Component({

  /**
   * 组件的属性列表
   */
  properties: {
       time:{
         type:String
       },
       list:{
         type:Array
       },
       

  },

  /**
   * 组件的初始数据
   */
  data: {
       current:0,
  },

  /**
   * 组件的方法列表
   */
  methods: {
    toggele(){
      this.triggerEvent('Byacoumt',1)
      this.setData({
        current:1
      })
    },
    toggele2(){
      this.triggerEvent('Bytime',0)
      this.setData({
        current:0
      })
    },
  }
})