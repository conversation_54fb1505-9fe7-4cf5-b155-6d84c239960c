<view class="container">


  <!-- 用户信息卡片 -->
  <view class="section user-section">
    <view class="section-title">
      <view class="section-indicator"></view>
      <text>我的</text>
    </view>
    
    <view class="user-info">
      <view class="user-avatar" bindtap="changeAvatar">
        <image src="{{userInfo.avatar}}" mode="aspectFill" />
        <view class="change-avatar">更换</view>
      </view>
      <view bindtap="showNicknameDialog" class="user-details">
        <view class="user-greeting">你好，{{userInfo.nickname}}</view>
        <view class="user-join-date">{{userInfo.joinDate}}</view>
      </view>
      <view bindtap="showNicknameDialog" class="user-decoration">
        <image src="/static/icon/user-decoration.png" mode="aspectFit" />
      </view>
    </view>
  </view>

  <!-- 会员信息 -->
  <view class="section member-section">
    <view class="section-title">
      <view class="section-indicator"></view>
      <text>我的会员</text>
    </view>
    
    <view class="member-info">
      <view class="vip-tag">VIP</view>
      <view class="member-status">{{isVip ? '已开通' : '未开通'}}</view>
      <view class="member-arrow">
        <image src="/static/icon/arrow-right.png" mode="aspectFit" />
      </view>
    </view>
  </view>

  <!-- 个人信息列表 -->
  <view class="section personal-info-section">
    <view class="section-title">
      <view class="section-indicator"></view>
      <text>个人信息</text>
    </view>
    
    <!-- 会员编码 -->
    <view class="info-item" bindtap="copyMemberCode">
      <view class="info-icon">
        <image src="/static/icon/member-code.png" mode="aspectFit" />
      </view>
      <view class="info-content">
        <text class="info-label">会员编码</text>
        <text class="info-value">{{userInfo.memberCode}}</text>
      </view>
      <view class="info-arrow">
        <image src="/static/icon/arrow-right.png" mode="aspectFit" />
      </view>
    </view>
    
    <!-- 昵称 -->
    <view class="info-item" bindtap="showNicknameDialog">
      <view class="info-icon">
        <image src="/static/icon/nickname.png" mode="aspectFit" />
      </view>
      <view class="info-content">
        <text class="info-label">昵称</text>
        <text class="info-value">{{userInfo.nickname}}</text>
      </view>
      <view class="info-arrow">
        <image src="/static/icon/arrow-right.png" mode="aspectFit" />
      </view>
    </view>
    
    <!-- 手机号 -->
    <view class="info-item">
      <view class="info-icon">
        <image src="/static/icon/mobile.png" mode="aspectFit" />
      </view>
      <view class="info-content">
        <text class="info-label">手机号</text>
        <text class="info-value">{{userInfo.mobile}}</text>
      </view>
      <view class="info-arrow">
        <image src="/static/icon/arrow-right.png" mode="aspectFit" />
      </view>
    </view>
    
    <!-- 加入时间 -->
    <view class="info-item">
      <view class="info-icon">
        <image src="/static/icon/join-time.png" mode="aspectFit" />
      </view>
      <view class="info-content">
        <text class="info-label">加入时间</text>
        <text class="info-value">{{userInfo.joinTime}}</text>
      </view>
      <view class="info-arrow">
        <image src="/static/icon/arrow-right.png" mode="aspectFit" />
      </view>
    </view>
  </view>

</view>

<!-- 修改昵称弹窗 -->
<view class="nickname-dialog-mask" wx:if="{{showNicknameDialog}}" bindtap="hideNicknameDialog">
  <view class="nickname-dialog" catchtap="stopPropagation">
    <view class="nickname-dialog-header">
      <text class="nickname-dialog-title">修改昵称</text>
      <view class="nickname-dialog-close" bindtap="hideNicknameDialog">
        <text>×</text>
      </view>
    </view>
    <view class="nickname-dialog-content">
      <view class="nickname-input-box">
        <input class="nickname-input" value="{{tempNickname}}" bindinput="onNicknameInput" placeholder="请输入昵称" />
      </view>
    </view>
    <view class="nickname-dialog-footer">
      <button class="save-btn" bindtap="saveNickname">保存</button>
    </view>
  </view>
</view>