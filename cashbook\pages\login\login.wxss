.login-container {
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: space-between;
  padding: 60rpx 40rpx;
  height: 100vh;
  background-color: #f8f8f8;
}

/* 欢迎标题 */
.welcome-title {
  font-size: 36rpx;
  font-weight: bold;
  color: #333;
  margin-top: 80rpx;
  margin-bottom: 60rpx;
}

/* 登录图示区域 */
.login-illustration {
  position: relative;
  width: 100%;
  height: 500rpx;
  display: flex;
  justify-content: center;
  align-items: center;
}

/* 登录卡片 */
.login-card {
  width: 280rpx;
  height: 400rpx;
  background-color: #ffd8c4;
  border-radius: 30rpx;
  position: relative;
  display: flex;
  flex-direction: column;
  align-items: center;
  padding: 30rpx;
  box-shadow: 0 10rpx 20rpx rgba(0, 0, 0, 0.1);
}

/* 头像容器 */
.avatar-container {
  position: absolute;
  top: -40rpx;
  width: 100%;
  display: flex;
  justify-content: center;
}

.avatar-circle {
  width: 100rpx;
  height: 100rpx;
  border-radius: 50%;
  background-color: #ff6b35;
  display: flex;
  justify-content: center;
  align-items: center;
  overflow: hidden;
}

.avatar-circle image {
  width: 80rpx;
  height: 80rpx;
  border-radius: 50%;
}

/* 卡片元素 */
.card-elements {
  margin-top: 80rpx;
  width: 100%;
}

.element {
  height: 30rpx;
  border-radius: 15rpx;
  margin-bottom: 20rpx;
}

.element-1 {
  background-color: white;
  width: 80%;
}

.element-2 {
  background-color: #ff6b35;
  width: 60%;
}

.element-3 {
  background-color: black;
  width: 40%;
}

.element-4 {
  background-color: #8dc63f;
  width: 90%;
}

.element-5 {
  background-color: #ffcba4;
  width: 70%;
}

/* 卡片底部的点 */
.card-dots {
  position: absolute;
  bottom: 20rpx;
  display: flex;
  justify-content: center;
  width: 100%;
}

.dot {
  width: 16rpx;
  height: 16rpx;
  border-radius: 50%;
  margin: 0 6rpx;
}

.dot-1 {
  background-color: #ff6b35;
}

.dot-2 {
  background-color: #ffcba4;
}

.dot-3 {
  background-color: #8dc63f;
}

.dot-4 {
  background-color: #333;
}

/* 装饰元素 */
.decoration {
  position: absolute;
  height: 100%;
}

.left-decoration {
  left: 40rpx;
  display: flex;
  align-items: center;
}

.right-decoration {
  right: 40rpx;
  display: flex;
  align-items: center;
}

.dot-group {
  display: flex;
  flex-direction: column;
  align-items: center;
}

.dot-group .dot {
  width: 30rpx;
  height: 30rpx;
  border-radius: 50%;
  background-color: #e0e0e0;
  margin: 10rpx 0;
}

.dot-group .line {
  width: 6rpx;
  height: 80rpx;
  background-color: #e0e0e0;
}

.color-beads {
  display: flex;
  flex-direction: column;
}

.bead {
  width: 40rpx;
  height: 40rpx;
  border-radius: 50%;
  margin: 10rpx 0;
}

.bead-1 {
  background-color: #ff6b35;
}

.bead-2 {
  background-color: #8dc63f;
}

.bead-3 {
  background-color: #6a5acd;
}

.bead-4 {
  background-color: #ffcba4;
}

/* 登录按钮 */
.login-buttons {
  width: 100%;
  margin-top: 80rpx;
}

.login-btn {
  width: 100%;
  height: 90rpx;
  border-radius: 45rpx;
  margin-bottom: 30rpx;
  font-size: 32rpx;
  display: flex;
  align-items: center;
  justify-content: center;
}

.wechat-login {
  background-color: #8dc63f;
  color: white;
}

.phone-login {
  background-color: #f5f5f5;
  color: #333;
  border: 1px solid #e0e0e0;
}

/* 隐私政策 */
.privacy-policy {
  margin-top: 20rpx;
  font-size: 26rpx;
  color: #999;
}

.checkbox {
  display: flex;
  align-items: center;
}

.policy-link {
  color: #8dc63f;
}

checkbox .wx-checkbox-input {
  width: 30rpx;
  height: 30rpx;
  border-radius: 50%;
}