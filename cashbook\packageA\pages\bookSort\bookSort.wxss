/* packageA/pages/bookSort/bookSort.wxss */
.container {
  background-color: #FFFFFF;
  min-height: 100vh;
  padding: 30rpx;
  box-sizing: border-box;
  display: flex;
  flex-direction: column;
}

/* 顶部导航 */
.nav-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 0 20rpx;
}

.nav-back {
  width: 60rpx;
  height: 60rpx;
  display: flex;
  align-items: center;
}

.nav-title {
  font-size: 36rpx;
  font-weight: 600;
  color: #000;
}

.nav-placeholder {
  width: 60rpx;
}

/* 子标题 */
.subtitle {
  font-size: 24rpx;
  color: #999;
  padding: 20rpx 30rpx;
}

/* 拖拽区域 */
.movable-area {
  width: 100%;
  height: calc(100vh - 300rpx);
  position: relative;
  overflow: hidden;
}

/* 标题 */
.title {
  font-size: 30rpx;
  color: #999;
  margin-bottom: 30rpx;
}

/* 滚动区域 */
.book-scroll-view {
  flex: 1;
  position: relative;
  margin-bottom: 120rpx;
}

/* 账本列表 */
.book-list {
  position: relative;
  padding-bottom: 20rpx;
}

.book-item {
  display: flex;
  align-items: center;
  padding: 10rpx 0;
  border-bottom: 1rpx solid #F5F5F5;
  background-color: #FFFFFF;
  position: relative;
  transition: transform 0.15s ease-out, opacity 0.15s ease-out;
  transform: translate3d(0, 0, 0); /* 启用GPU加速 */
  will-change: transform; /* 提示浏览器此元素将频繁变化 */
  user-select: none;
  -webkit-user-select: none;
}

.book-item.moving {
  opacity: 0.95;
  transform: translate3d(0, 0, 0) scale(1.02);
  background-color: #f8f8f8;
  box-shadow: 0 4rpx 12rpx rgba(0, 0, 0, 0.1);
  z-index: 100;
  transition: box-shadow 0.2s ease-out, background-color 0.2s ease-out, opacity 0.2s ease-out;
  touch-action: none; /* 只在拖动时禁用默认触摸行为 */
}

.book-icon {
  width: 100rpx;
  height: 100rpx;
  border-radius: 50%;
  overflow: hidden;
  margin-right: 30rpx;
  background-color: #F5F5F5;
}

.book-icon image {
  width: 100%;
  height: 100%;
  object-fit: cover;
}

.book-icon view {
  width: 100%;
  height: 100%;
}

.book-name {
  flex: 1;
  font-size: 36rpx;
  font-weight: 500;
  color: #333;
}

.drag-handle {
  padding: 20rpx;
}

/* 保存按钮 */
.save-btn {
  position: fixed;
  bottom: 40rpx;
  left: 30rpx;
  right: 30rpx;
  height: 90rpx;
  background-color: #FFC0CB;
  color: #333;
  font-size: 32rpx;
  font-weight: 500;
  border-radius: 45rpx;
  display: flex;
  justify-content: center;
  align-items: center;
  z-index: 100;
}

/* 底部安全区域 */
.safe-area-bottom {
  height: 150rpx;
} 