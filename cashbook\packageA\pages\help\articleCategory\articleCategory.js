import { getArticlelist } from '../../../../api/basic/index';

Page({
  /**
   * 页面的初始数据
   */
  data: {
    categoryId: null,
    categoryName: '文章分类',
    categoryDescription: '',
    articles: [],
    loading: true,
    page: 1,
    pageSize: 10,
    hasMore: true
  },

  /**
   * 生命周期函数--监听页面加载
   */
  onLoad(options) {
    // 如果有传入分类ID，则记录下来
    if (options.id) {
      this.setData({
        categoryId: options.id
      });
      
      // 设置页面标题
      if (options.name) {
        this.setData({
          categoryName: options.name
        });
        wx.setNavigationBarTitle({
          title: options.name
        });
      } else {
        // 为账单创建分类设置特定名称
        if (options.id == 1) {
          this.setData({
            categoryName: '账单创建',
            categoryDescription: '账单创建 点这里'
          });
          wx.setNavigationBarTitle({
            title: '账单创建'
          });
        } else {
          wx.setNavigationBarTitle({
            title: '文章分类'
          });
        }
      }
      
      // 加载该分类下的文章
      this.loadArticles();
    } else {
      // 没有指定分类ID，显示提示
      this.setData({
        loading: false
      });
      
      wx.showToast({
        title: '未指定分类',
        icon: 'none'
      });
    }
  },

  /**
   * 加载文章列表
   */
  async loadArticles() {
    try {
      wx.showLoading({
        title: '加载中...',
      });
      
      // 构建查询参数
      const params = {
        category_id: this.data.categoryId,
        page: this.data.page,
        page_size: this.data.pageSize
      };
      
      // 请求文章列表数据
      const result = await getArticlelist(params);
      
      // 处理结果
      if (result && result.data) {
        const newArticles = this.data.page === 1 
          ? result.data 
          : [...this.data.articles, ...result.data];
        
        // 模拟数据 - 如果是账单创建分类且没有返回数据，添加示例文章
        if (this.data.categoryId == 1 && (!result.data || result.data.length === 0)) {
          const sampleArticles = this.getSampleArticles();
          this.setData({
            articles: sampleArticles,
            loading: false,
            hasMore: false
          });
        } else {
          this.setData({
            articles: newArticles,
            loading: false,
            hasMore: result.data.length >= this.data.pageSize
          });
        }
      } else {
        // 模拟数据 - 如果是账单创建分类且没有返回数据，添加示例文章
        if (this.data.categoryId == 1) {
          const sampleArticles = this.getSampleArticles();
          this.setData({
            articles: sampleArticles,
            loading: false,
            hasMore: false
          });
        } else {
          this.setData({
            loading: false,
            hasMore: false
          });
        }
      }
    } catch (error) {
      console.error('加载文章失败:', error);
      
      // 模拟数据 - 如果是账单创建分类且加载失败，添加示例文章
      if (this.data.categoryId == 1) {
        const sampleArticles = this.getSampleArticles();
        this.setData({
          articles: sampleArticles,
          loading: false,
          hasMore: false
        });
      } else {
        this.setData({
          loading: false
        });
        
        wx.showToast({
          title: '加载文章失败',
          icon: 'none'
        });
      }
    } finally {
      wx.hideLoading();
      wx.stopPullDownRefresh();
    }
  },

  /**
   * 获取账单创建分类的示例文章
   */
  getSampleArticles() {
    return [
      {
        id: 101,
        title: '如何创建新账单',
        description: '详细指导如何在小猫记账中创建和管理新的账单',
        publish_time: '2023-06-15',
        view_count: 1254
      },
      {
        id: 102,
        title: '账单分类使用指南',
        description: '了解如何使用账单分类功能，让您的记账更加条理',
        publish_time: '2023-06-18',
        view_count: 986
      },
      {
        id: 103,
        title: '自定义账单模板',
        description: '创建符合您个人需求的自定义账单模板',
        publish_time: '2023-06-20',
        view_count: 753
      },
      {
        id: 104,
        title: '批量导入账单数据',
        description: '学习如何从其他应用导入账单数据，快速开始使用',
        publish_time: '2023-06-25',
        view_count: 542
      }
    ];
  },

  /**
   * 查看文章详情
   */
  viewArticle(e) {
    const articleId = e.currentTarget.dataset.id;
    if (articleId) {
      wx.navigateTo({
        url: `/packageA/pages/help/articleDetail/articleDetail?id=${articleId}`
      });
    }
  },

  /**
   * 返回上一页
   */
  goBack() {
    wx.navigateBack();
  },

  /**
   * 页面相关事件处理函数--监听用户下拉动作
   */
  onPullDownRefresh() {
    // 重置页码并重新加载
    this.setData({
      page: 1,
      hasMore: true
    });
    this.loadArticles();
  },

  /**
   * 页面上拉触底事件的处理函数
   */
  onReachBottom() {
    // 如果还有更多数据，加载下一页
    if (this.data.hasMore) {
      this.setData({
        page: this.data.page + 1
      });
      this.loadArticles();
    }
  }
}) 