/* 全局容器 */
.container {
  display: flex;
  flex-direction: column;
  min-height: 100vh;
  background-color: #f7f8fa;
  padding-bottom: 100rpx;
}

/* 导航栏 */
.nav {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 60rpx 30rpx 20rpx;
  position: relative;
  z-index: 10;
}

.back-btn {
  width: 70rpx;
  height: 70rpx;
  background-color: white;
  border-radius: 50%;
  display: flex;
  align-items: center;
  justify-content: center;
  font-size: 40rpx;
  box-shadow: 0 4rpx 10rpx rgba(0, 0, 0, 0.1);
}

.title {
  font-size: 36rpx;
  font-weight: 600;
  color: #333;
  transition: all 0.3s ease;
}

.calendar-btn {
  width: 70rpx;
  height: 70rpx;
  background-color: white;
  border-radius: 50%;
  display: flex;
  align-items: center;
  justify-content: center;
  box-shadow: 0 4rpx 10rpx rgba(0, 0, 0, 0.1);
}

.calendar-btn image {
  width: 40rpx;
  height: 40rpx;
}

/* Swiper和内容区域 */
.content-swiper {
  flex: 1;
  height: calc(100vh - 250rpx);
}

.content-scroll {
  height: 100%;
  padding: 0 30rpx;
  box-sizing: border-box;
}

/* 预算卡片 */
.budget-card {
  background-color: white;
  border-radius: 24rpx;
  padding: 30rpx;
  margin-bottom: 30rpx;
  box-shadow: 0 4rpx 16rpx rgba(0, 0, 0, 0.05);
}

.card-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 20rpx;
  position: relative;
}

.account-info {
  position: relative;
  padding-left: 20rpx;
}

.account-info::before {
  content: '';
  position: absolute;
  left: 0;
  top: 6rpx;
  height: 80%;
  width: 8rpx;
  background: linear-gradient(to bottom, #a4cfa0, #f7f8fa);
  border-radius: 4rpx;
}

.account-name {
  font-size: 32rpx;
  font-weight: 500;
  color: #333;
  margin-bottom: 4rpx;
}

.date-range {
  font-size: 24rpx;
  color: #999;
}

.setting-btn {
  padding: 10rpx 24rpx;
  background-color: #f8f0de;
  border-radius: 30rpx;
  font-size: 28rpx;
  color: #b3a89c;
}

/* 预算金额 */
.budget-amount {
  margin: 30rpx 0;
}

.amount-wrapper {
  display: flex;
  justify-content: space-between;
  align-items: center;
}

.amount-label {
  font-size: 28rpx;
  color: #999;
  margin-bottom: 10rpx;
}

.amount-value {
  font-size: 60rpx;
  font-weight: 600;
  color: #333;
}

/* 圆形进度条 */
.progress-circle-container {
  width: 120rpx;
  height: 120rpx;
  margin-right: 10rpx;
}

.progress-circle {
  width: 100%;
  height: 100%;
  border-radius: 50%;
  background-color: #f7f7f7;
  display: flex;
  align-items: center;
  justify-content: center;
  box-shadow: 0 0 10rpx rgba(0, 0, 0, 0.05);
  position: relative;
  --progress: 0; /* 默认进度0% */
}

.progress-circle::before {
  content: '';
  position: absolute;
  top: 0;
  right: 0;
  bottom: 0;
  left: 0;
  border-radius: 50%;
  border: 12rpx solid #f1f1f1;
}

.progress-circle::after {
  content: '';
  position: absolute;
  top: 0;
  right: 0;
  bottom: 0;
  left: 0;
  border-radius: 50%;
  border: 12rpx solid transparent;
  border-top-color: #a4cfa0;
  border-right-color: #a4cfa0;
  clip-path: polygon(50% 0, 100% 0, 100% 100%, 0 100%, 0 0);
  transform: rotate(calc(45deg + (var(--progress) * 3.6deg)));
  transition: transform 0.5s ease;
}

.progress-circle-inner {
  width: 90rpx;
  height: 90rpx;
  border-radius: 50%;
  background-color: white;
  z-index: 1;
}

/* 进度值对应不同的显示方式 */
.progress-circle[style*="--progress: 100"]::after {
  border-color: #a4cfa0;
}

/* 预算统计项 */
.budget-stats {
  display: flex;
  justify-content: space-between;
  margin-bottom: 30rpx;
}

.stat-item {
  text-align: center;

}

.stat-label {
  font-size: 26rpx;
  margin-bottom: 20rpx;
}

.stat-value {
  font-size: 28rpx;
  color: #333;
  font-weight: 500;
}

/* 分割线 */
.divider {
  height: 2rpx;
  background-color: #f0f0f0;
  margin: 20rpx 0;
  border-top: 1rpx dashed #ddd;
}

/* 日均统计 */
.daily-stats {
  padding: 10rpx 0;
}

.daily-item {
  display: flex;
  align-items: center;
  margin-bottom: 16rpx;
}

.dot {
  width: 16rpx;
  height: 16rpx;
  border-radius: 50%;
  margin-right: 16rpx;
}

.orange {
  background-color: #f8b37d;
}

.purple {
  background-color: #8e7af3;
}

.blue {
  background-color: #7fb4f5;
}

.daily-label {
  flex: 1;
  font-size: 28rpx;
  color: #666;
}

.daily-value {
  font-size: 28rpx;
  color: #333;
  text-align: right;
}

/* 分类预算 */
.category-budget {
  background-color: white;
  border-radius: 24rpx;
  padding: 30rpx;
  box-shadow: 0 4rpx 16rpx rgba(0, 0, 0, 0.05);
  margin-bottom: 30rpx;
}

.category-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 30rpx;
  position: relative;
}

.category-title {
  font-size: 32rpx;
  font-weight: 500;
  color: #333;
  position: relative;
  padding-left: 20rpx;
}

.category-title::before {
  content: '';
  position: absolute;
  left: 0;
  top: 6rpx;
  height: 80%;
  width: 8rpx;
  background: linear-gradient(to bottom, #a4cfa0, #f7f8fa);
  border-radius: 4rpx;
}

.add-btn {
  padding: 10rpx 24rpx;
  background-color: #f8f0de;
  border-radius: 30rpx;
  font-size: 28rpx;
  color: #b3a89c;
}

/* 空状态 */
.empty-state {
  padding: 60rpx 0;
  text-align: center;
}

.empty-icon {
  margin-bottom: 20rpx;
}

.empty-icon image {
  width: 160rpx;
  height: 160rpx;
}

.empty-text {
  font-size: 28rpx;
  color: #999;
}

/* 底部切换按钮 - 胶囊样式 */
.tab-toggle-container {
  position: fixed;
  bottom: 40rpx;
  left: 0;
  right: 0;
  display: flex;
  justify-content: center;
  padding: 0 30rpx;
  z-index: 100;
}

.tab-toggle {
  display: flex;
  width: 300rpx;
  height: 80rpx;
  background-color: #f0f0f0;
  border-radius: 40rpx;
  overflow: hidden;
  box-shadow: 0 4rpx 16rpx rgba(0, 0, 0, 0.1);
}

.toggle-option {
  flex: 1;
  display: flex;
  align-items: center;
  justify-content: center;
  font-size: 28rpx;
  color: #666;
  position: relative;
  transition: all 0.3s;
}

.toggle-option.active {
  background-color: #a4cfa0;
  color: white;
  font-weight: 500;
}
