// packageA/pages/bookSetting/incomeCategory/sortCategory/sortCategory.js
// 分类排序
import { sortCategory, getCategoryList } from '../../../../../api/category/index.js';
const util = require('../../../../../utils/index.js');

Page({

  /**
   * 页面的初始数据
   */
  data: {
    statusBarHeight: 0, // 状态栏高度
    navBarHeight: 44, // 导航栏高度
    categories: [], // 分类列表
    accountbook_id: '', // 账本ID
    type: 'expenses', // 分类类型，默认支出
    pid: 0, // 父类ID，0表示主分类排序
    isDragging: false, // 是否正在拖动
    isLongPressing: false, // 是否正在长按
    startY: 0, // 拖动开始的Y坐标
    startX: 0, // 拖动开始的X坐标
    currentIndex: -1, // 当前拖动的项索引
    sortChanged: false, // 排序是否改变
    moveY: 0, // 拖动的Y偏移量
    itemHeight: 81, // 每个项目的高度（包括margin）
    draggedItem: null, // 被拖动的项目
    targetIndex: -1, // 目标位置索引
    longPressTimer: null, // 长按定时器
    longPressDelay: 300, // 长按延迟时间（毫秒），减少长按延迟
    isDropping: false // 是否正在放置
  },

  /**
   * 生命周期函数--监听页面加载
   */
  onLoad(options) {
    console.log('排序页面参数:', options);

    // 获取系统信息
    this.getSystemInfo();

    // 获取参数
    const { accountbook_id = '', type = 'expenses', pid = '0' } = options;

    this.setData({
      accountbook_id,
      type,
      pid: parseInt(pid)
    });

    // 加载分类数据
    this.loadCategories();
  },

  /**
   * 生命周期函数--监听页面初次渲染完成
   */
  onReady() {
    // 计算实际的项目高度
    this.calculateItemHeight();
  },

  /**
   * 计算项目实际高度
   */
  calculateItemHeight() {
    wx.createSelectorQuery()
      .select('.category-item')
      .boundingClientRect(rect => {
        if (rect) {
          // 更新项目高度，加上间距
          this.setData({
            itemHeight: rect.height + 10 // 10是margin-bottom的值
          });

          // 重新计算每个项目的位置
          this.updateCategoriesPosition();
        }
      }).exec();
  },

  /**
   * 更新所有分类项的位置
   */
  updateCategoriesPosition() {
    const { categories, itemHeight } = this.data;

    if (!categories.length) return;

    const updatedCategories = categories.map((item, index) => {
      item.sortIndex = index;
      item.translateY = 0; // 确保所有项初始化无偏移
      return item;
    });

    this.setData({
      categories: updatedCategories
    });
  },

  /**
   * 获取系统信息，设置导航栏高度
   */
  getSystemInfo: function() {
    try {
      const systemInfo = wx.getSystemInfoSync();
      // 状态栏高度
      const statusBarHeight = systemInfo.statusBarHeight;
      // 导航栏高度，默认44px
      const navBarHeight = 44;

      this.setData({
        statusBarHeight: statusBarHeight,
        navBarHeight: navBarHeight
      });
    } catch (e) {
      console.error('获取系统信息失败', e);
    }
  },

  /**
   * 加载分类数据
   */
  loadCategories: function() {
    wx.showLoading({
      title: '加载中...',
    });

    // 构建请求参数
    const params = {
      accountbook_id: this.data.accountbook_id,
      type: this.data.type
    };

    // 如果是子分类排序，需要传递pid
    if (this.data.pid !== 0) {
      params.pid = this.data.pid;
    }

    console.log('加载分类数据，请求参数:', params);

    // 调用接口获取分类数据
    getCategoryList(params).then(res => {
      wx.hideLoading();

      if (res && res.code === 1 && res.data) {
        console.log('获取分类数据成功:', res.data);

        // 处理分类数据
        let categories = [];

        if (this.data.pid === 0) {
          // 主分类排序
          categories = res.data.map((item, index) => {
            // 处理图标
            this.processIconType(item);

            // 添加排序索引
            item.sortIndex = index;
            item.translateY = 0; // 初始化无偏移
            return item;
          });
        } else {
          // 子分类排序，找到对应的父分类
          const parentCategory = res.data.find(item => item.id === this.data.pid);

          if (parentCategory && parentCategory.child && Array.isArray(parentCategory.child)) {
            categories = parentCategory.child.map((item, index) => {
              // 处理图标
              this.processIconType(item);

              // 添加排序索引
              item.sortIndex = index;
              item.translateY = 0; // 初始化无偏移
              return item;
            });
          }
        }

        // 更新分类数据
        this.setData({
          categories: categories
        });
      } else {
        wx.showToast({
          title: res.msg || '获取分类数据失败',
          icon: 'none'
        });
      }
    }).catch(err => {
      wx.hideLoading();
      console.error('获取分类数据失败:', err);

      wx.showToast({
        title: '网络请求失败',
        icon: 'none'
      });
    });
  },

  /**
   * 处理图标类型
   */
  processIconType: function(item) {
    if (item.image) {
      // 检查是否是文字图标
      if (typeof item.image === 'string' && item.image.indexOf('text:') === 0) {
        item.imageType = 'text';
        item.iconContent = item.image.substring(5); // 去掉'text:'前缀
      }
      // 检查是否是emoji图标
      else if (typeof item.image === 'string' && item.image.indexOf('emoji:') === 0) {
        item.imageType = 'emoji';
        item.iconContent = item.image.substring(6); // 去掉'emoji:'前缀
      }
      // 检查是否是base64格式图片
      else if (typeof item.image === 'string' && item.image.indexOf('data:image/') === 0) {
        item.imageType = 'image';
        // base64格式图片不需要通过getImageUrl处理
      }
      // 普通图片
      else {
        item.imageType = 'image';
        item.image = util.getImageUrl(item.image);
      }
    }
    return item;
  },

  /**
   * 触摸开始事件
   */
  touchStart: function(e) {
    const index = e.currentTarget.dataset.index;
    const touch = e.touches[0];

    // 清除之前的定时器
    if (this.data.longPressTimer) {
      clearTimeout(this.data.longPressTimer);
    }

    // 记录初始位置
    this.setData({
      startY: touch.clientY,
      startX: touch.clientX,
      currentIndex: index,
      isLongPressing: false,
      isDragging: false,
      targetIndex: index // 初始化目标索引为当前索引
    });

    // 设置长按定时器
    const timer = setTimeout(() => {
      this.startDragging(index);
    }, this.data.longPressDelay);

    this.setData({
      longPressTimer: timer
    });

    // 添加震动反馈
    wx.vibrateShort({
      type: 'light'
    });
  },

  /**
   * 开始拖动
   */
  startDragging: function(index) {
    const draggedItem = this.data.categories[index];

    // 为所有项目添加初始位置信息
    const categories = this.data.categories.map((item, idx) => ({
      ...item,
      originalIndex: idx,
      currentPosition: idx,
      translateY: 0
    }));

    this.setData({
      isDragging: true,
      isLongPressing: true,
      targetIndex: index,
      moveY: 0,
      draggedItem: draggedItem,
      categories: categories
    });

    // 长按震动反馈
    wx.vibrateShort({
      type: 'medium'
    });
  },

  /**
   * 触摸移动事件
   */
  touchMove: function(e) {
    const touch = e.touches[0];
    const moveX = Math.abs(touch.clientX - this.data.startX);
    const moveY = touch.clientY - this.data.startY;
    const moveDistance = Math.sqrt(moveX * moveX + moveY * moveY);

    // 如果移动距离超过阈值，取消长按
    if (moveDistance > 5 && this.data.longPressTimer && !this.data.isDragging) {
      clearTimeout(this.data.longPressTimer);
      this.setData({
        longPressTimer: null,
        isLongPressing: false
      });
      return;
    }

    // 只有在拖动状态下才处理移动
    if (!this.data.isDragging) return;

    const currentIndex = this.data.currentIndex;
    const categories = this.data.categories;
    const itemHeight = this.data.itemHeight;

    // 计算当前拖动位置相对于起始位置的位移
    const displacement = moveY;

    // 基于位移计算目标索引
    let newTargetIndex = currentIndex;

    // 向下拖动（位移为正）
    if (displacement > 0) {
      const itemsToMove = Math.floor((displacement + itemHeight / 3) / itemHeight);
      newTargetIndex = Math.min(currentIndex + itemsToMove, categories.length - 1);
    }
    // 向上拖动（位移为负）
    else if (displacement < 0) {
      const itemsToMove = Math.ceil((displacement - itemHeight / 3) / itemHeight);
      newTargetIndex = Math.max(currentIndex + itemsToMove, 0);
    }

    // 如果目标位置发生变化，更新其他元素的位置
    if (newTargetIndex !== this.data.targetIndex) {
      // 添加轻微震动反馈
      wx.vibrateShort({
        type: 'light'
      });
      this.updateItemPositions(currentIndex, newTargetIndex);

      this.setData({
        targetIndex: newTargetIndex,
        sortChanged: true // 标记为排序已改变
      });
    }

    this.setData({
      moveY: moveY
    });
  },

  /**
   * 更新元素位置
   */
  updateItemPositions: function(fromIndex, toIndex) {
    const categories = [...this.data.categories];

    categories.forEach((item, index) => {
      if (index === fromIndex) {
        // 被拖动的元素不在这里处理位置
        return;
      }

      let translateY = 0;

      if (fromIndex < toIndex) {
        // 向下拖动
        if (index > fromIndex && index <= toIndex) {
          translateY = -this.data.itemHeight; // 向上移动一个位置
        }
      } else {
        // 向上拖动
        if (index >= toIndex && index < fromIndex) {
          translateY = this.data.itemHeight; // 向下移动一个位置
        }
      }

      item.translateY = translateY;
    });

    this.setData({
      categories: categories
    });
  },

  /**
   * 触摸结束事件
   */
  touchEnd: function() {
    // 清除长按定时器
    if (this.data.longPressTimer) {
      clearTimeout(this.data.longPressTimer);
      this.setData({
        longPressTimer: null
      });
    }

    // 如果没有在拖动状态，直接返回
    if (!this.data.isDragging) {
      this.setData({
        isLongPressing: false,
        currentIndex: -1
      });
      return;
    }

    const currentIndex = this.data.currentIndex;
    const targetIndex = this.data.targetIndex;

    // 计算被拖动元素应该移动到的最终位置
    const finalMoveY = (targetIndex - currentIndex) * this.data.itemHeight;

    // 第一阶段：被拖动元素平滑移动到目标位置
    this.setData({
      isDragging: false, // 结束拖动状态，启用过渡动画
      moveY: finalMoveY, // 移动到最终位置
      isDropping: true // 标记为放置状态
    });

    // 添加震动反馈
    wx.vibrateShort({
      type: 'medium'
    });

    // 第二阶段：延迟执行排序重组
    setTimeout(() => {
      this.swapItemsAndReorder(currentIndex, targetIndex);
    }, 300); // 等待放置动画完成
  },

  /**
   * 交换元素并重新排序
   */
  swapItemsAndReorder: function(fromIndex, toIndex) {
    // 如果位置没有变化，直接重置
    if (fromIndex === toIndex) {
      this.resetItemPositions();
      return;
    }

    const categories = [...this.data.categories];

    // 移动元素
    const item = categories.splice(fromIndex, 1)[0];
    categories.splice(toIndex, 0, item);

    // 更新排序索引并重置所有位置
    categories.forEach((item, index) => {
      item.sortIndex = index;
      item.translateY = 0; // 重置位置
    });

    // 更新数据并重置所有状态
    this.setData({
      categories: categories,
      sortChanged: true,
      isLongPressing: false,
      currentIndex: -1,
      targetIndex: -1,
      draggedItem: null,
      moveY: 0,
      isDropping: false
    });
  },

  /**
   * 重置元素位置
   */
  resetItemPositions: function() {
    const categories = this.data.categories.map(item => ({
      ...item,
      translateY: 0
    }));

    this.setData({
      categories: categories,
      isLongPressing: false,
      isDragging: false,
      currentIndex: -1,
      targetIndex: -1,
      draggedItem: null,
      moveY: 0,
      isDropping: false
    });
  },

  /**
   * 保存排序
   */
  saveSort: function() {
    if (!this.data.sortChanged) {
      wx.showToast({
        title: '排序未改变',
        icon: 'none'
      });
      return;
    }

    wx.showLoading({
      title: '保存中...',
    });

    // 构建请求参数
    const params = {
      accountbook_id: this.data.accountbook_id
    };

    // 如果是子分类排序，添加pid参数
    if (this.data.pid !== 0) {
      params.pid = this.data.pid;
    }

    // 添加排序参数，格式为 sort[分类ID] = 排序值
    // 排序值越大越靠前，所以第一个位置的分类排序值最大
    this.data.categories.forEach((item, index) => {
      params[`sort[${item.id}]`] = this.data.categories.length - index;
    });

    console.log('保存排序，请求参数:', params);
    console.log('分类数量:', this.data.categories.length);
    console.log('排序详情:', this.data.categories.map((item, index) => ({
      id: item.id,
      name: item.name,
      position: index,
      sortValue: this.data.categories.length - index
    })));

    // 调用排序接口
    sortCategory(params).then(res => {
      wx.hideLoading();
      console.log('排序接口返回结果:', res);

      if (res && res.code === 1) {
        wx.showToast({
          title: '保存成功',
          icon: 'success'
        });

        // 标记排序已保存
        this.setData({
          sortChanged: false
        });

        // 返回上一页
        setTimeout(() => {
          // 设置上一页需要刷新
          const pages = getCurrentPages();
          if (pages.length > 1) {
            const prevPage = pages[pages.length - 2];
            if (prevPage && prevPage.setData) {
              prevPage.setData({
                needReload: true
              });
            }
          }

          wx.navigateBack();
        }, 1500);
      } else {
        wx.showToast({
          title: res.msg || '保存失败',
          icon: 'none'
        });
        console.error('排序保存失败:', res);
      }
    }).catch(err => {
      wx.hideLoading();
      console.error('保存排序失败:', err);

      wx.showToast({
        title: '网络请求失败',
        icon: 'none'
      });
    });
  },

  /**
   * 返回上一页
   */
  goBack: function() {
    if (this.data.sortChanged) {
      wx.showModal({
        title: '提示',
        content: '排序已改变，是否保存？',
        cancelText: '不保存',
        confirmText: '保存',
        success: (res) => {
          if (res.confirm) {
            this.saveSort();
          } else {
            wx.navigateBack();
          }
        }
      });
    } else {
      wx.navigateBack();
    }
  },

  /**
   * 生命周期函数--监听页面显示
   */
  onShow() {

  },

  /**
   * 生命周期函数--监听页面隐藏
   */
  onHide() {

  },

  /**
   * 生命周期函数--监听页面卸载
   */
  onUnload() {

  },

  /**
   * 页面相关事件处理函数--监听用户下拉动作
   */
  onPullDownRefresh() {

  },

  /**
   * 页面上拉触底事件的处理函数
   */
  onReachBottom() {

  },

  /**
   * 用户点击右上角分享
   */
  onShareAppMessage() {

  }
})