<!-- packageA/pages/help/agreementList/agreementList.wxml -->
<view class="agreement-list-container">
  <!-- 导航栏 -->
  <!-- <view class="nav">
    <customNav isRight="{{true}}" isClose="{{true}}" isIcon="{{false}}"></customNav>
  </view> -->
  <!-- 导航栏占位符 -->
  <!-- <view class="nav-placeholder"></view> -->
  <!-- 加载中提示 -->
  <view class="loading" wx:if="{{loading}}">
    <text>加载中...</text>
  </view>
  <!-- 空状态提示 -->
  <view class="empty-state" wx:elif="{{!agreementList || agreementList.length === 0}}">
    <text>暂无协议内容</text>
  </view>
  <!-- 协议列表 -->
  <view class="agreement-list" wx:else>
    <view class="agreement-item" wx:for="{{agreementList}}" wx:key="id" bindtap="viewAgreementDetail" data-id="{{item.id}}" data-title="{{item.title}}">
      <view class="item-header">
        <view class="item-title">{{item.title || '用户协议'}}</view>
        <view class="item-arrow">
          <text>></text>
        </view>
      </view>
      <view class="item-desc" wx:if="{{item.desc}}">{{item.desc}}</view>
      <view class="item-time" wx:if="{{item.create_time}}">{{item.create_time}}</view>
    </view>
  </view>
</view>