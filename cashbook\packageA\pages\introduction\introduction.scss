/* packageA/pages/introduction/introduction.scss */

.introduction-container {
  padding: 0 0 30rpx 0;
  background-color: #f7f7f7;
  min-height: 100vh;

  /* 加载状态样式 */
  .loading-container {
    display: flex;
    flex-direction: column;
    justify-content: center;
    align-items: center;
    height: 300rpx;
    
    .loading-icon {
      width: 80rpx;
      height: 80rpx;
      border: 6rpx solid #f3f3f3;
      border-top: 6rpx solid #3498db;
      border-radius: 50%;
      animation: spin 1s linear infinite;
      margin-bottom: 20rpx;
    }
    
    .loading-text {
      font-size: 28rpx;
      color: #999;
    }
    
    @keyframes spin {
      0% { transform: rotate(0deg); }
      100% { transform: rotate(360deg); }
    }
  }

  /* 顶部标题样式 */
  .header {
    padding: 40rpx 30rpx;
    background-color: #fff;
    border-bottom: 1rpx solid #eee;
    margin-bottom: 20rpx;

    .title {
      font-size: 40rpx;
      font-weight: bold;
      color: #333;
      margin-bottom: 10rpx;
    }

    .subtitle {
      font-size: 28rpx;
      color: #999;
    }
  }
  
  /* 引言内容样式 */
  .introduction-content {
    padding: 30rpx;
    background-color: #fff;
    margin-bottom: 20rpx;
    line-height: 1.6;
    
    .intro-text {
      display: block;
      font-size: 28rpx;
      color: #666;
      text-align: justify;
      padding: 10rpx 0;
    }
  }

  /* 功能列表区域样式 */
  .functions-container {
    padding: 0 20rpx;

    .section {
      margin-bottom: 30rpx;
      background-color: #fff;
      border-radius: 12rpx;
      overflow: hidden;
      box-shadow: 0 2rpx 10rpx rgba(0, 0, 0, 0.05);

      .section-title {
        padding: 25rpx 30rpx;
        font-size: 32rpx;
        color: #333;
        font-weight: 500;
        border-bottom: 1rpx solid #f0f0f0;
      }

      /* 普通列表视图 */
      .section-content {
        padding: 0 10rpx;

        .function-item {
          display: flex;
          align-items: center;
          padding: 25rpx 20rpx;
          border-bottom: 1rpx solid #f5f5f5;

          &:last-child {
            border-bottom: none;
          }

          .function-icon {
            width: 80rpx;
            height: 80rpx;
            background-color: #f5f7f6;
            border-radius: 40rpx;
            display: flex;
            justify-content: center;
            align-items: center;
            margin-right: 20rpx;

            image {
              width: 40rpx;
              height: 40rpx;
            }
          }

          .function-info {
            flex: 1;

            .function-name {
              font-size: 30rpx;
              color: #333;
              margin-bottom: 5rpx;
            }

            .function-desc {
              font-size: 24rpx;
              color: #999;
            }
          }

          .arrow {
            width: 40rpx;
            height: 40rpx;
            display: flex;
            justify-content: center;
            align-items: center;

            image {
              width: 24rpx;
              height: 24rpx;
            }
          }
        }
      }
      
      /* 网格视图 - 用于更多文章分类 */
      .section-grid {
        display: flex;
        flex-wrap: wrap;
        padding: 20rpx 10rpx;
        
        .grid-item {
          width: 25%; /* 一行4个 */
          padding: 15rpx;
          box-sizing: border-box;
          display: flex;
          flex-direction: column;
          align-items: center;
          
          .grid-icon {
            width: 80rpx;
            height: 80rpx;
            background-color: #f5f7f6;
            border-radius: 40rpx;
            display: flex;
            justify-content: center;
            align-items: center;
            margin-bottom: 10rpx;
            
            image {
              width: 40rpx;
              height: 40rpx;
            }
          }
          
          .grid-name {
            font-size: 24rpx;
            color: #333;
            text-align: center;
            width: 100%;
            white-space: nowrap;
            overflow: hidden;
            text-overflow: ellipsis;
          }
        }
      }
      
      /* 推荐文章卡片视图 */
      .section-cards {
        padding: 20rpx;
        
        .card-item {
          display: flex;
          margin-bottom: 20rpx;
          background-color: #fff;
          border-radius: 8rpx;
          padding: 15rpx;
          border: 1rpx solid #f0f0f0;
          box-shadow: 0 2rpx 8rpx rgba(0, 0, 0, 0.03);
          
          &:last-child {
            margin-bottom: 0;
          }
          
          .card-image {
            width: 120rpx;
            height: 120rpx;
            border-radius: 8rpx;
            overflow: hidden;
            margin-right: 15rpx;
            
            image {
              width: 100%;
              height: 100%;
            }
          }
          
          .card-content {
            flex: 1;
            display: flex;
            flex-direction: column;
            justify-content: space-between;
            
            .card-title {
              font-size: 28rpx;
              font-weight: 500;
              color: #333;
              margin-bottom: 10rpx;
              line-height: 1.4;
              display: -webkit-box;
              -webkit-line-clamp: 2;
              -webkit-box-orient: vertical;
              overflow: hidden;
            }
            
            .card-desc {
              font-size: 24rpx;
              color: #999;
              display: -webkit-box;
              -webkit-line-clamp: 2;
              -webkit-box-orient: vertical;
              overflow: hidden;
            }
          }
        }
      }
    }
  }
  
  /* 客服信息样式 */
  .kefu-container {
    margin: 0 20rpx 30rpx;
    background-color: #fff;
    border-radius: 12rpx;
    padding: 30rpx;
    box-shadow: 0 2rpx 10rpx rgba(0, 0, 0, 0.05);
    
    .kefu-title {
      font-size: 32rpx;
      font-weight: 500;
      color: #333;
      padding-bottom: 20rpx;
      border-bottom: 1rpx solid #f0f0f0;
      margin-bottom: 20rpx;
    }
    
    .kefu-info {
      .kefu-item {
        display: flex;
        padding: 15rpx 0;
        
        .kefu-label {
          font-size: 28rpx;
          color: #666;
          width: 180rpx;
        }
        
        .kefu-value {
          font-size: 28rpx;
          color: #333;
          flex: 1;
          
          &.phone {
            color: #4BA3E1;
            text-decoration: underline;
          }
        }
      }
    }
  }

  /* 底部信息样式 */
  .footer {
    padding: 30rpx 0;
    text-align: center;

    .version {
      font-size: 24rpx;
      color: #999;
      margin-bottom: 10rpx;
    }

    .copyright {
      font-size: 22rpx;
      color: #bbb;
    }
  }
}