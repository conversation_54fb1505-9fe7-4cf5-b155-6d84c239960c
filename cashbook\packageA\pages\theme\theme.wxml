<view class="theme-container">
  <!-- 顶部导航栏 -->
  <!-- <view class="nav-header">
    <view class="back-icon" bindtap="goBack">
      <text class="iconfont icon-back"></text>
    </view>
    <view class="title">主题</view>
  </view> -->

  <!-- 字体颜色部分 -->
  <view class="section font-color-section">
    <view class="section-indicator"></view>
    <view class="section-title">字体颜色</view>
  </view>
  <view class="section-desc">快选择自己喜欢的主题颜色吧~</view>

  <!-- 自定义颜色部分 -->
  <view class="section custom-color-section">
    <view class="section-indicator"></view>
    <view class="section-title">自定义颜色</view>
  </view>

  <!-- 选择颜色按钮 -->
  <view class="color-select-btn" bindtap="showColorPicker">
    <view class="btn-text">选择颜色</view>
    <view class="check-icon" wx:if="{{selectedColorType === 'custom'}}">
      <text class="iconfont icon-check"></text>
    </view>
  </view>

  <!-- 颜色图标 -->
  <view class="color-icon">
    <image src="/static/images/color-icon.png" mode="aspectFit"></image>
  </view>

  <!-- 加载中提示 -->
  <view class="loading-wrapper" wx:if="{{loading}}">
    <view class="loading-text">正在加载主题色...</view>
  </view>

  <!-- 颜色选择网格 - 使用服务器数据 -->
  <view class="color-grid" wx:if="{{!loading && colorList.length > 0}}">
    <view class="color-row">
      <view
        wx:for="{{colorList}}"
        wx:key="id"
        class="color-item {{selectedColor === item.value ? 'selected' : ''}}"
        style="background-color: {{item.color}};"
        data-color="{{item.value}}"
        bindtap="selectColor"
      >
        <view class="color-name">{{item.name}}</view>
        <view class="check-icon" wx:if="{{selectedColor === item.value}}">
          <text class="iconfont icon-check"></text>
        </view>
      </view>
    </view>
  </view>

  <!-- 空状态提示 -->
  <view class="empty-state" wx:if="{{!loading && colorList.length === 0}}">
    <view class="empty-text">暂无可用主题色</view>
  </view>

  <!-- 服务器主题颜色部分 -->
  <view class="section" wx:if="{{serverThemes.length > 0}}">
    <view class="section-indicator"></view>
    <view class="section-title">在线主题</view>
  </view>

  <view class="color-grid" wx:if="{{serverThemes.length > 0}}">
    <view class="color-row">
      <view
        wx:for="{{serverThemes}}"
        wx:key="id"
        class="color-item {{selectedColor === 'server_'+item.id ? 'selected' : ''}}"
        style="background-color: {{item.color}};"
        data-color="{{'server_'+item.id}}"
        bindtap="selectColor"
      >
        <view class="color-name">{{item.name}}</view>
        <view class="check-icon" wx:if="{{selectedColor === 'server_'+item.id}}">
          <text class="iconfont icon-check"></text>
        </view>
      </view>
    </view>
  </view>

  <!-- 底部导航栏 -->
  <view class="bottom-tabbar">
    <view class="tab-item">
      <text class="iconfont icon-menu"></text>
    </view>
    <view class="tab-item">
      <text class="iconfont icon-home"></text>
    </view>
    <view class="tab-item">
      <text class="iconfont icon-back"></text>
    </view>
  </view>
</view>
