/* packageA/pages/help/articleDetail/articleDetail.scss */
.article-detail-container {
  min-height: 100vh;
  background-color: #f8f9fa;
  padding-bottom: 120rpx; /* 为底部工具栏留出空间 */
  
  /* 顶部导航栏 */
  .nav-bar {
    position: fixed;
    top: 0;
    left: 0;
    right: 0;
    height: 88rpx;
    background-color: #fff;
    display: flex;
    align-items: center;
    padding: 0 30rpx;
    z-index: 100;
    box-shadow: 0 2rpx 10rpx rgba(0, 0, 0, 0.05);
    
    .back-btn {
      width: 40rpx;
      height: 40rpx;
      padding: 10rpx;
      
      image {
        width: 100%;
        height: 100%;
      }
    }
    
    .article-title-short {
      flex: 1;
      text-align: center;
      font-size: 32rpx;
      font-weight: 500;
      color: #333;
      white-space: nowrap;
      overflow: hidden;
      text-overflow: ellipsis;
      margin: 0 20rpx;
    }
    
    .share-btn {
      width: 40rpx;
      height: 40rpx;
      
      .share-button {
        padding: 0;
        background: none;
        border: none;
        line-height: 1;
        width: 100%;
        height: 100%;
        
        &::after {
          border: none;
        }
        
        image {
          width: 100%;
          height: 100%;
        }
      }
    }
  }
  
  /* 文章内容样式 */
  .article-content {
    background-color: #fff;
    
    .article-header {
      padding: 30rpx 0;
      
      .article-title {
        font-size: 40rpx;
        font-weight: bold;
        color: #333;
        line-height: 1.4;
        margin-bottom: 20rpx;
      }
      
      .article-meta {
        display: flex;
        align-items: center;
        font-size: 24rpx;
        color: #999;
        
        .article-author, .article-date, .article-views {
          margin-right: 20rpx;
        }
      }
    }
    
    .article-cover {
      margin: 20rpx 0;
      
      image {
        width: 100%;
        border-radius: 8rpx;
      }
    }
    
    /* 账单创建指南样式 */
    .bill-creation-guide {
      padding: 20rpx 0;
      
      .guide-title {
        font-size: 36rpx;
        font-weight: 600;
        color: #333;
        margin-bottom: 40rpx;
        text-align: center;
      }
      
      .guide-step {
        display: flex;
        margin-bottom: 40rpx;
        position: relative;
        
        &:not(:last-child):after {
          content: '';
          position: absolute;
          top: 60rpx;
          left: 30rpx;
          width: 2rpx;
          height: calc(100% - 30rpx);
          background-color: #e0e0e0;
          z-index: 1;
        }
        
        .step-number {
          width: 60rpx;
          height: 60rpx;
          border-radius: 50%;
          background-color: #4a90e2;
          color: #fff;
          font-size: 28rpx;
          font-weight: bold;
          display: flex;
          align-items: center;
          justify-content: center;
          margin-right: 20rpx;
          position: relative;
          z-index: 2;
        }
        
        .step-content {
          flex: 1;
          
          .step-title {
            font-size: 32rpx;
            font-weight: 500;
            color: #333;
            margin-bottom: 10rpx;
          }
          
          .step-desc {
            font-size: 28rpx;
            color: #666;
            line-height: 1.6;
          }
        }
      }
      
      .guide-tips {
        background-color: #f5f7fa;
        border-radius: 12rpx;
        padding: 30rpx;
        margin-top: 40rpx;
        
        .tips-title {
          font-size: 30rpx;
          font-weight: 500;
          color: #333;
          margin-bottom: 20rpx;
        }
        
        .tips-content {
          .tip-item {
            font-size: 26rpx;
            color: #666;
            line-height: 1.8;
            position: relative;
          }
        }
      }
    }
    
    .article-body {
      font-size: 30rpx;
      color: #333;
      line-height: 1.8;
      
      /* 富文本样式 */
      rich-text {
        display: block;
        width: 100%;
        
        image {
          max-width: 100% !important;
          height: auto !important;
        }
        
        /* 富文本中常见元素的样式 */
        p {
          margin-bottom: 20rpx;
        }
        
        h1, h2, h3, h4, h5 {
          margin: 30rpx 0 20rpx;
          font-weight: bold;
        }
        
        h1 {
          font-size: 36rpx;
        }
        
        h2 {
          font-size: 34rpx;
        }
        
        h3 {
          font-size: 32rpx;
        }
        
        a {
          color: #3498db;
          text-decoration: none;
        }
        
        ul, ol {
          padding-left: 40rpx;
          margin: 20rpx 0;
        }
        
        li {
          margin-bottom: 10rpx;
        }
        
        pre, code {
          background-color: #f5f7f9;
          border-radius: 4rpx;
          padding: 10rpx;
          font-family: monospace;
          font-size: 28rpx;
          overflow-x: auto;
        }
        
        blockquote {
          border-left: 8rpx solid #f0f0f0;
          padding: 10rpx 20rpx;
          color: #666;
          margin: 20rpx 0;
        }
        
        table {
          border-collapse: collapse;
          width: 100%;
          margin: 20rpx 0;
          
          th, td {
            border: 1rpx solid #e0e0e0;
            padding: 10rpx;
            text-align: left;
          }
          
          th {
            background-color: #f5f7f9;
          }
        }
      }
    }
    
    .article-tags {
      margin-top: 40rpx;
      border-top: 1rpx solid #f0f0f0;
      padding-top: 30rpx;
      display: flex;
      align-items: flex-start;
      
      .tag-title {
        font-size: 26rpx;
        color: #666;
        margin-right: 10rpx;
      }
      
      .tag-list {
        flex: 1;
        display: flex;
        flex-wrap: wrap;
        
        .tag-item {
          font-size: 24rpx;
          color: #3498db;
          background-color: rgba(52, 152, 219, 0.1);
          padding: 6rpx 16rpx;
          border-radius: 30rpx;
          margin-right: 16rpx;
          margin-bottom: 16rpx;
        }
      }
    }
    
    /* 相关文章推荐 */
    .related-articles {
      margin-top: 40rpx;
      border-top: 1rpx solid #f0f0f0;
      padding-top: 30rpx;
      
      .related-title {
        font-size: 32rpx;
        font-weight: 500;
        color: #333;
        margin-bottom: 20rpx;
      }
      
      .related-list {
        .related-item {
          display: flex;
          align-items: center;
          justify-content: space-between;
          padding: 24rpx 0;
          border-bottom: 1rpx solid #f0f0f0;
          
          &:last-child {
            border-bottom: none;
          }
          
          .related-item-title {
            font-size: 28rpx;
            color: #333;
          }
          
          .related-item-arrow {
            width: 32rpx;
            height: 32rpx;
            opacity: 0.3;
            
            image {
              width: 100%;
              height: 100%;
            }
          }
          
          &:active {
            opacity: 0.7;
          }
        }
      }
    }
  }
  
  /* 底部工具栏 */
  .bottom-toolbar {
    position: fixed;
    bottom: 0;
    left: 0;
    right: 0;
    height: 100rpx;
    background-color: #fff;
    display: flex;
    align-items: center;
    justify-content: space-around;
    box-shadow: 0 -2rpx 10rpx rgba(0, 0, 0, 0.05);
    z-index: 99;
    
    .toolbar-btn {
      display: flex;
      flex-direction: column;
      align-items: center;
      justify-content: center;
      padding: 0;
      background: none;
      line-height: 1;
      border: none;
      
      &::after {
        border: none;
      }
      
      image {
        width: 44rpx;
        height: 44rpx;
        margin-bottom: 6rpx;
      }
      
      text {
        font-size: 22rpx;
        color: #666;
      }
      
      &:active {
        opacity: 0.7;
      }
    }
  }
  
  /* 加载中样式 */
  .loading-container {
    display: flex;
    flex-direction: column;
    justify-content: center;
    align-items: center;
    height: 500rpx;
    
    .loading-icon {
      width: 60rpx;
      height: 60rpx;
      border: 4rpx solid #f3f3f3;
      border-top: 4rpx solid #3498db;
      border-radius: 50%;
      animation: spin 1s linear infinite;
      margin-bottom: 20rpx;
    }
    
    @keyframes spin {
      0% { transform: rotate(0deg); }
      100% { transform: rotate(360deg); }
    }
    
    text {
      font-size: 26rpx;
      color: #999;
    }
  }
  
  /* 空状态样式 */
  .empty-container {
    display: flex;
    flex-direction: column;
    justify-content: center;
    align-items: center;
    height: 500rpx;
    padding-top: 100rpx;
    
    image {
      width: 180rpx;
      height: 180rpx;
      margin-bottom: 20rpx;
    }
    
    text {
      font-size: 28rpx;
      color: #999;
    }
  }
} 