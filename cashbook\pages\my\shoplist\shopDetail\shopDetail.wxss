.sp1{
  display: grid;
  grid-template-columns: auto auto;
  align-items: center;
  font-size: 12px;
  gap: 5px;
  margin: 10px 0 ;
  justify-content: start;
}
.sp1 view{
  background-color: #bfccaa;
  border-radius: 5px;
  width: fit-content;
}
.sp1 view:nth-child(2){
    border: 1rpx solid #000;
    background-color: #fff;
    border-radius: 5px;
    width: fit-content;
  }
.sp1Active{
  background-color: #bfccaa;
}

.shopInfo{
  display: grid;
  grid-template-columns: 2fr 1fr;
  align-items: center;
  gap: 10px;
  margin: 10px 0 ;
  /* border: 1px solid; */
}
.SIleft{
  display: grid;
  grid-template-columns: repeat(4,auto);
  align-items: center;
}
.SIleft view{
  /* border: 1px solid; */
}
.SIleft view:nth-child(4){
  background-color: #f4af47;
  border-radius: 6px;
  text-align: center;
  font-size: 12px;
}
.SIright{
  text-align: right;
}
