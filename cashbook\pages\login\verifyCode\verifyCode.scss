.verify-container { 
  display: flex;
  flex-direction: column;
  align-items: center;
  padding: 40rpx;
  height: 100vh;
  background-color: #f8f8f8;
  position: relative;

  /* 确认按钮 */
  .confirm-btn {
    width: 100%;
    height: 90rpx;
    border-radius: 45rpx;
    background-color: #8dc63f;
    color: white;
    font-size: 32rpx;
    display: flex;
    align-items: center;
    justify-content: center;
    position: absolute;
    bottom: 120rpx;
    left: 40rpx;
    right: 40rpx;
    width: calc(100% - 80rpx);
  }
}

/* 返回按钮 */
.back-button {
  position: absolute;
  top: 40rpx;
  left: 40rpx;
  width: 60rpx;
  height: 60rpx;
  z-index: 10;
}

.back-button image {
  width: 40rpx;
  height: 40rpx;
}

/* 验证码标题 */
.verify-title {
  font-size: 36rpx;
  font-weight: bold;
  color: #333;
  margin-top: 160rpx;
  margin-bottom: 80rpx;
}

/* 验证码输入框容器 */
.code-input-container {
  width: 100%;
  display: flex;
  flex-direction: column;
  align-items: center;
}

.code-input-group {
  display: flex;
  justify-content: space-between;
  width: 80%;
  margin-bottom: 40rpx;
}

.code-input {
  width: 120rpx;
  height: 120rpx;
  background-color: #f0f0f0;
  border-radius: 20rpx;
  display: flex;
  justify-content: center;
  align-items: center;
  font-size: 40rpx;
  color: #333;
}

.code-input.active {
  border: 2rpx solid #8dc63f;
}

/* 隐藏真实输入框 */
.hidden-input {
  position: absolute;
  opacity: 0;
  width: 0;
  height: 0;
}

/* 清除按钮 */
.clear-btn {
  color: #4BA3E1;
  font-size: 28rpx;
  margin-bottom: 40rpx;
}

/* 倒计时提示 */
.countdown-tip {
  font-size: 28rpx;
  color: #999;
  margin-bottom: 120rpx;
}



/* 成功弹窗样式 */
.success-dialog {
  position: fixed;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background-color: rgba(0, 0, 0, 0.5);
  display: flex;
  justify-content: center;
  align-items: center;
  z-index: 9999;
}

.success-dialog-content {
  width: 80%;
  background-color: #fff;
  border-radius: 15px;
  padding: 20px;
  position: relative;
  box-shadow: 0 2px 10px rgba(0, 0, 0, 0.1);

  .confirm-btn {
    background-color: #a1d0b6;
    color: #fff;
    text-align: center;
    padding: 10px 0;
    border-radius: 30px;
    font-size: 16px;
  }
}

.close-icon {
  position: absolute;
  top: 10px;
  right: 15px;
  font-size: 24px;
  color: #999;
  cursor: pointer;
}

.success-title {
  text-align: center;
  font-size: 18px;
  font-weight: bold;
  margin-bottom: 15px;
}

.success-message {
  text-align: center;
  font-size: 14px;
  color: #999;
  margin-bottom: 20px;
}