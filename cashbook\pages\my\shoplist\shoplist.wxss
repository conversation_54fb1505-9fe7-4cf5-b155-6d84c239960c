

.sp1{
  display: grid;
  grid-template-columns: auto auto;
  align-items: center;
  font-size: 12px;
  gap: 5px;
  margin: 10px 0 ;
  justify-content: start;
}
.sp1 view{
  background-color: #bfccaa;
  border-radius: 5px;
  width: fit-content;
}
.sp1 view:nth-child(2){
    border: 1rpx solid #000;
    background-color: #fff;
    border-radius: 5px;
    width: fit-content;
  }
.sp1Active{
  background-color: #bfccaa;
}
.bottom_title{
    width: 100%;
    text-align: center;
    color: gray;
    font-size: 30rpx;
}
