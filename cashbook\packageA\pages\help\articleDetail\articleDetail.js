import { getArticle } from '../../../../api/basic/index';

Page({
  /**
   * 页面的初始数据
   */
  data: {
    articleId: null,
    article: null,
    loading: true
  },

  /**
   * 生命周期函数--监听页面加载
   */
  onLoad(options) {
    // 如果有传入文章ID，则加载文章详情
    if (options.id) {
      this.setData({
        articleId: options.id
      });
      
      // 加载文章详情
      this.loadArticleDetail();
    } else {
      // 没有指定文章ID，显示提示
      this.setData({
        loading: false
      });
      
      wx.showToast({
        title: '未指定文章',
        icon: 'none'
      });
    }
  },

  /**
   * 加载文章详情
   */
  async loadArticleDetail() {
    try {
      wx.showLoading({
        title: '加载中...',
      });
      
      // 请求文章详情数据
      const result = await getArticle({
        article_id: this.data.articleId
      });
      
      // 处理结果
      if (result && result.data) {
        this.setData({
          article: result.data,
          loading: false
        });
        
        // 设置页面标题
        wx.setNavigationBarTitle({
          title: result.data.title || '文章详情'
        });
      } else {
        // 如果是账单创建的文章ID，但API没有返回数据，则创建模拟数据
        if (this.data.articleId == 1) {
          const mockArticle = this.createMockArticle();
          this.setData({
            article: mockArticle,
            loading: false
          });
          
          wx.setNavigationBarTitle({
            title: '账单创建'
          });
        } else {
          this.setData({
            loading: false
          });
          
          wx.showToast({
            title: '未找到文章',
            icon: 'none'
          });
        }
      }
    } catch (error) {
      console.error('加载文章详情失败:', error);
      
      // 如果是账单创建的文章ID，但加载失败，则创建模拟数据
      if (this.data.articleId == 1) {
        const mockArticle = this.createMockArticle();
        this.setData({
          article: mockArticle,
          loading: false
        });
        
        wx.setNavigationBarTitle({
          title: '账单创建'
        });
      } else {
        this.setData({
          loading: false
        });
        
        wx.showToast({
          title: '加载文章失败',
          icon: 'none'
        });
      }
    } finally {
      wx.hideLoading();
    }
  },

  /**
   * 创建账单创建的模拟文章数据
   */
  createMockArticle() {
    return {
      id: 1,
      title: '账单创建',
      content: '<p>账单创建 点这里</p>',
      publish_time: '2023-06-01',
      view_count: 3680
    };
  },

  /**
   * 导航到相关文章
   */
  navigateToArticle(e) {
    const articleId = e.currentTarget.dataset.id;
    if (articleId) {
      wx.navigateTo({
        url: `/packageA/pages/help/articleDetail/articleDetail?id=${articleId}`
      });
    }
  },

  /**
   * 提供反馈
   */
  giveFeedback() {
    wx.showModal({
      title: '文章反馈',
      content: '这篇文章对您有帮助吗？欢迎提供您的反馈和建议',
      confirmText: '有帮助',
      cancelText: '无帮助',
      success: (res) => {
        if (res.confirm) {
          wx.showToast({
            title: '感谢您的反馈',
            icon: 'success'
          });
        } else {
          wx.navigateTo({
            url: '/packageA/pages/help/customerService/customerService'
          });
        }
      }
    });
  },

  /**
   * 返回上一页
   */
  goBack() {
    wx.navigateBack();
  },

  /**
   * 分享文章
   */
  onShareAppMessage() {
    if (this.data.article) {
      return {
        title: this.data.article.title,
        path: `/packageA/pages/help/articleDetail/articleDetail?id=${this.data.articleId}`
      };
    }
    
    return {
      title: '文章详情',
      path: '/packageA/pages/introduction/introduction'
    };
  }
}) 