/* 整体容器 */
.container {
  display: flex;
  flex-direction: column;
  min-height: 100vh;
  background-color: #f5f5f5;
  position: relative;
  padding-bottom: 50px;
}







/* 内容区域通用样式 */
.section {
  margin: 10px 15px;
  background-color: #ffffff;
  border-radius: 12px;
  overflow: hidden;
  box-shadow: 0 1px 3px rgba(0, 0, 0, 0.05);
}

.section-title {
  display: flex;
  align-items: center;
  padding: 12px 15px;
  border-bottom: 1px solid #f0f0f0;
}

.section-indicator {
  width: 3px;
  height: 16px;
  background-color: #4CAF50;
  margin-right: 8px;
  border-radius: 1.5px;
}

/* 用户信息区域 */
.user-section {
  margin-top: 54px;
}

.user-info {
  display: flex;
  padding: 15px;
  align-items: center;
}

.user-avatar {
  position: relative;
  width: 60px;
  height: 60px;
  border-radius: 50%;
  overflow: hidden;
  background-color: #e0e9f2;
}

.user-avatar image {
  width: 100%;
  height: 100%;
  object-fit: cover;
}

.change-avatar {
  position: absolute;
  bottom: 0;
  left: 0;
  right: 0;
  background-color: rgba(0, 0, 0, 0.5);
  color: #ffffff;
  font-size: 10px;
  text-align: center;
  padding: 2px 0;
}

.user-details {
  flex: 1;
  margin-left: 15px;
}

.user-greeting {
  font-size: 16px;
  font-weight: 500;
  margin-bottom: 5px;
}

.user-join-date {
  font-size: 12px;
  color: #999999;
}

.user-decoration {
  width: 80px;
  height: 80px;
}

.user-decoration image {
  width: 100%;
  height: 100%;
  object-fit: contain;
}

/* 会员信息区域 */
.member-info {
  display: flex;
  align-items: center;
  padding: 15px;
}

.vip-tag {
  color: #f0dfc1;
  font-size: 14px;
  margin-right: 10px;
}

.member-status {
  flex: 1;
  font-size: 14px;
  color: #666666;
}

.member-arrow {
  width: 16px;
  height: 16px;
}

.member-arrow image {
  width: 100%;
  height: 100%;
}

/* 个人信息列表 */
.info-item {
  display: flex;
  align-items: center;
  padding: 15px;
  border-bottom: 1px solid #f0f0f0;
}

.info-item:last-child {
  border-bottom: none;
}

.info-icon {
  width: 24px;
  height: 24px;
  margin-right: 15px;
}

.info-icon image {
  width: 100%;
  height: 100%;
  object-fit: contain;
}

.info-content {
  flex: 1;
  display: flex;
  flex-direction: column;
}

.info-label {
  font-size: 14px;
  color: #333333;
  margin-bottom: 3px;
}

.info-value {
  font-size: 14px;
  color: #999999;
}

.info-arrow {
  width: 16px;
  height: 16px;
}

.info-arrow image {
  width: 100%;
  height: 100%;
}


/* 昵称修改弹窗 */
.nickname-dialog-mask {
  position: fixed;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background-color: rgba(0, 0, 0, 0.5);
  display: flex;
  justify-content: center;
  align-items: center;
  z-index: 1000;
}

.nickname-dialog {
  width: 80%;
  background-color: #fff;
  border-radius: 12px;
  overflow: hidden;
  display: flex;
  flex-direction: column;
}

.nickname-dialog-header {
  position: relative;
  padding: 20px;
  text-align: center;
  border-bottom: 1px solid #f0f0f0;
}

.nickname-dialog-title {
  font-size: 16px;
  font-weight: 500;
}

.nickname-dialog-close {
  position: absolute;
  left: 20px;
  top: 50%;
  transform: translateY(-50%);
  font-size: 24px;
  color: #999;
  width: 24px;
  height: 24px;
  display: flex;
  align-items: center;
  justify-content: center;
}

.nickname-dialog-content {
  padding: 20px;
}

.nickname-input-box {
  background-color: #f5f5f5;
  border-radius: 8px;
  padding: 10px 15px;
}

.nickname-input {
  width: 100%;
  height: 24px;
  font-size: 14px;
}

.nickname-dialog-footer {
  padding: 0 20px 20px;
}

.save-btn {
  background-color: #a5ddb9;
  color: #fff;
  border-radius: 24px;
  font-size: 16px;
  padding: 10px 0;
  width: 100%;
  border: none;
  outline: none;
}

