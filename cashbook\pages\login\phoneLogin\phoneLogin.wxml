<view class="login-container">
  <!-- 返回按钮 -->
  <view class="back-button" bindtap="goBack">
    <image src="/static/icon/back.png" mode="aspectFit"></image>
  </view>
  
  <!-- 登录表单 -->
  <view class="login-form">
    <view class="form-title">请输入账号密码</view>
    
    <!-- 账号输入框 -->
    <view class="input-group">
      <input class="form-input" type="text" placeholder="手机号/账号" bindinput="onAccountInput" value="{{account}}"/>
    </view>
    
    <!-- 密码输入框 -->
    <view class="input-group">
      <input class="form-input" type="{{showPassword ? 'text' : 'password'}}" placeholder="密码" bindinput="onPasswordInput" value="{{password}}"/>
      <view class="password-toggle" bindtap="togglePasswordVisibility">
        <image src="{{showPassword ? '/static/icon/eye_open.png' : '/static/icon/eye_close.png'}}" mode="aspectFit"></image>
      </view>
    </view>
    
    <!-- 登录按钮 -->
    <button class="login-btn" bindtap="login">登录</button>
    
    <!-- 底部链接 -->
    <view class="bottom-links">
      <view class="register-link" bindtap="toRegister">
        <text>没有账号进行</text>
        <text class="highlight-text">注册</text>
      </view>
      <view class="forgot-password" bindtap="toForgotPassword">
        <text class="highlight-text">找回密码?</text>
      </view>
    </view>
  </view>
  
  <!-- 装饰图 -->
  <view class="decoration-image">
    <image src="/static/icon/login_decoration.png" mode="aspectFit"></image>
  </view>
  
  <!-- 隐私政策 -->
  <view class="privacy-policy">
    <checkbox-group bindchange="checkboxChange">
      <label class="checkbox">
        <checkbox value="agree" checked="{{isAgree}}" color="#8dc63f" />
        <text>我已阅读并同意</text>
        <text class="policy-link" bindtap="showPrivacyPolicy">《隐私政策》</text>
      </label>
    </checkbox-group>
  </view>
</view>