<!-- packageA/pages/help/videoDetail/videoDetail.wxml -->
<view class="video-detail-container">
  <!-- 顶部导航 -->
  <view class="nav-bar">
    <view class="close-btn" bindtap="goBack">
      <text class="close-icon">×</text>
    </view>
    <view class="video-title">{{title || '账单标签'}}</view>
  </view>
  <!-- 功能标题 -->
  <view class="feature-title">
    <text class="dot"></text>
    <text class="title-text">标签功能</text>
  </view>
  <!-- 视频播放区域 -->
  <view class="video-player-container">
    <video id="myVideo" src="{{video}}" controls="{{false}}" autoplay="{{true}}" show-center-play-btn="{{false}}" show-play-btn="{{false}}" show-fullscreen-btn="{{false}}" bindended="onVideoEnded" binderror="onVideoError" bindtimeupdate="onTimeUpdate" class="video-player"></video>
  </view>
  <!-- 自定义视频控制器 -->
  <view class="custom-video-controls">
    <view class="progress-bar">
      <view class="progress-bg"></view>
      <view class="progress-current" style="width: {{videoProgress}}%"></view>
    </view>
    <view class="control-buttons">
      <view class="playback-rate" bindtap="changePlaybackRate">{{playbackRate}}x</view>
      <view class="play-pause-btn" bindtap="togglePlayPause">
        <text class="{{isPlaying ? 'pause-icon' : 'play-icon'}}">{{isPlaying ? '||' : '▶'}}</text>
      </view>
    </view>
  </view>
  <!-- 加载中 -->
  <view class="loading-container" wx:if="{{loading}}">
    <view class="loading-icon"></view>
    <text>视频加载中...</text>
  </view>
</view>