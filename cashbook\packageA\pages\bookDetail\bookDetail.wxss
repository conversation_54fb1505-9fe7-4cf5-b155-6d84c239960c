.container {
  position: relative;
  width: 100%;
  display: flex;
  flex-direction: column;
  padding: 0;
}

/* 背景图 */
.bg-image {
  position: fixed;
  top: 0;
  left: 0;
  width: 100%;
  height: 50vh;
  /* 使用视口高度单位而非百分比 */
  z-index: -1;
  will-change: transform;
  /* 添加will-change优化视差滚动性能 */
}

/* 顶部固定白色背景 */
.fixed-white-header {
  position: fixed;
  top: 0;
  left: 0;
  width: 100%;
  height: 90px;
  /* 与导航栏高度一致 */
  background-color: #fff;
  z-index: 100;
  transition: opacity 0.3s ease;
  box-shadow: 0 2px 10px rgba(0, 0, 0, 0.05);
}

.header-placeholder {
  width: 100%;
  height: 100%;
}

/* 顶部安全区域 - 避开微信小程序顶部系统按钮 */
.safe-area {
  width: 100%;
  height: 87px;
}

/* 顶部信息区 */
.header {
  /* 顶部信息区固定定位 */
  position: fixed;
  justify-content: flex-start;
  /* 改为靠左对齐，因为只有一个按钮 */
  padding: 0rpx 40rpx;
  width: 100%;
  box-sizing: border-box;
  min-height: 80rpx;
  /* 添加最小高度确保按钮可见 */
  z-index: 1;
  /* 确保在背景图上方 */
}

.close-btn {
  width: 60rpx;
  height: 60rpx;
  display: flex;
  align-items: center;
  justify-content: center;
  background-color: rgba(0, 0, 0, 0.2);
  border-radius: 50%;
  backdrop-filter: blur(5px);
}

/* 编辑按钮 - 右下角浮动样式 */
.edit-btn-corner {
  position: fixed;
  top: 160rpx;
  /* 调整垂直位置 */
  right: 40rpx;
  /* 距离右边缘的距离 */
  width: 60rpx;
  height: 60rpx;
  display: flex;
  align-items: center;
  justify-content: center;
  background-color: rgba(0, 0, 0, 0.2);
  border-radius: 50%;
  z-index: 10;
  backdrop-filter: blur(5px);
  /* 确保在其他元素上方 */
}

/* 月度统计信息 - 更新为更像图片中的样式 */
.monthly-stats {
  padding: 40rpx 40rpx;
  color: #fff;
  margin-top: 40rpx;
  /* 增加顶部边距，让内容在背景中更居中 */
}

.stats-label {
  font-size: 28rpx;
  opacity: 0.9;
  font-weight: 400;
  margin-bottom: 10rpx;
}

.stats-amount {
  font-size: 72rpx;
  font-weight: 600;
  padding: 10rpx 0 30rpx;
  letter-spacing: 2rpx;
  border-bottom: 1px dotted rgba(255, 255, 255, 0.3);
}

.sub-stats {
  padding: 20rpx 0;
  display: flex;
  flex-direction: column;
}

.sub-stats-row {
  display: flex;
  padding: 16rpx 0;
  /* 增加行间距 */
  justify-content: space-between;
  align-items: center;
  border-bottom: 1rpx dotted rgba(255, 255, 255, 0.2);
}

.sub-stats-label {
  font-size: 28rpx;
  opacity: 0.9;
  font-weight: 400;
}

.sub-stats-value {
  font-size: 28rpx;
  font-weight: 500;
}

.budget-wrapper {
  display: flex;
  align-items: center;
}

.budget-wrapper .sub-stats-value {
  margin-right: 10rpx;
}

/* 更新提示区 */
.update-tip {
  margin: 40rpx 40rpx;
  /* 增加上下边距 */
  background: rgba(255, 255, 255, 0.2);
  border-radius: 30rpx;
  padding: 10rpx 20rpx;
  display: flex;
  align-items: center;
  width: fit-content;
}

.tip-icon {
  width: 40rpx;
  height: 40rpx;
  border-radius: 50%;
  overflow: hidden;
  margin-right: 10rpx;
}

.tip-icon image {
  width: 100%;
  height: 100%;
}

.tip-text {
  color: #fff;
  font-size: 24rpx;
}

/* 标签页导航 - Vant Tabs 自定义样式 */
.van-tabs {
  width: 100%;
  background-color: #fff;
}

/* 使用动态颜色，不再硬编码 */
.van-tabs__line {
  /* 底部指示线样式由JS设置的color属性控制，不需要在这里设置背景色 */
  height: 6rpx !important;
  width: 60rpx !important;
  border-radius: 3rpx !important;
}

.van-tab {
  font-size: 28rpx !important;
  color: #999 !important;
  line-height: 84rpx !important;
}

.van-tab--active {
  font-weight: 500 !important;
  /* 活跃标签的颜色由JS设置的color属性控制 */
}

/* van-ellipsis的颜色现在通过title-active-color属性控制 */

.van-tabs__nav {
  background-color: #fff !important;
}

/* 设置滑动选项卡的样式 */
.van-tabs--card .van-tabs__wrap {
  height: 84rpx !important;
}

.van-tabs__scroll {
  background-color: #fff !important;
}

/* 废弃的旧标签页导航样式 */
/* .tab-nav {
  display: flex;
  background-color: #fff;
}

.tab-item {
  flex: 1;
  text-align: center;
  font-size: 28rpx;
  color: #999;
  padding: 30rpx 0;
  position: relative;
}

.tab-item.active1 {
  color: #FF7BAC;
  font-weight: 500;
}

.tab-item.active1::after {
  content: "";
  position: absolute;
  bottom: 0;
  left: 25%;
  width: 50%;
  height: 6rpx;
  background-color: #FF7BAC;
  border-radius: 3rpx;
} */

/* 日期选择器 */
.date-selector {
  display: flex;
  align-items: center;
  padding: 30rpx 40rpx;
}

.date-text {
  font-size: 28rpx;
  color: #333;
  margin-right: 10rpx;
}

/* 账单明细标题 */
.bill-title {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 0 40rpx 20rpx;
  font-size: 28rpx;
  color: #666;
}

.time-btn {
  font-size: 24rpx;
  color: #FF7BAC;
  background-color: #FFF0F5;
  padding: 6rpx 20rpx;
  border-radius: 20rpx;
}

/* 空状态 */
.empty-state {
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  margin-top: 60rpx;
}

.empty-icon {
  width: 240rpx;
  height: 240rpx;
  margin-bottom: 30rpx;
}

.empty-text {
  font-size: 28rpx;
  color: #999;
}

/* 添加按钮 */
.add-btn {
  position: fixed;
  right: 40rpx;
  bottom: 60rpx;
  width: 100rpx;
  height: 100rpx;
  /* Background-color now set via inline style */
  border-radius: 50%;
  display: flex;
  align-items: center;
  justify-content: center;
  color: #fff;
  font-size: 60rpx;
  box-shadow: 0 6rpx 20rpx rgba(255, 123, 172, 0.4);
  z-index: 1000;
}

/* 账本头部 */
.book-header {
  display: flex;
  align-items: center;
  margin-bottom: 40rpx;
}

.book-cover {
  width: 160rpx;
  height: 160rpx;
  border-radius: 20rpx;
  overflow: hidden;
  margin-right: 30rpx;
  box-shadow: 0 4rpx 12rpx rgba(0, 0, 0, 0.1);
}

.book-cover image {
  width: 100%;
  height: 100%;
  object-fit: cover;
}

.book-abbr {
  width: 100%;
  height: 100%;
  display: flex;
  align-items: center;
  justify-content: center;
  font-size: 64rpx;
  font-weight: bold;
  color: #fff;
}

.book-info {
  flex: 1;
}

.book-name {
  font-size: 36rpx;
  font-weight: bold;
  color: #333;
  margin-bottom: 12rpx;
}

.book-notes {
  font-size: 28rpx;
  color: #666;
  margin-bottom: 16rpx;
  max-height: 80rpx;
  overflow: hidden;
  text-overflow: ellipsis;
  display: -webkit-box;
  -webkit-line-clamp: 2;
  -webkit-box-orient: vertical;
}

.book-meta {
  display: flex;
  flex-wrap: wrap;
}

.meta-item {
  display: flex;
  align-items: center;
  margin-right: 20rpx;
  margin-bottom: 10rpx;
  font-size: 24rpx;
  color: #999;
}

.meta-item van-icon {
  margin-right: 6rpx;
}

/* 账本统计 */
.book-stats {
  margin-bottom: 40rpx;
}

.stats-card {
  background-color: #fff;
  border-radius: 16rpx;
  box-shadow: 0 4rpx 12rpx rgba(0, 0, 0, 0.05);
  padding: 30rpx 0;
  display: flex;
  justify-content: space-around;
}

.stats-item {
  flex: 1;
  text-align: center;
}

.day-name {
  font-size: 24rpx;
  color: #000;
  margin-right: 20rpx;
}

.day-notes {
  font-size: 24rpx;
  color: #666;
}

.stats-value {
  font-size: 36rpx;
  font-weight: bold;
  color: #333;
  margin-bottom: 10rpx;
}

.stats-value.income {
  color: #4CAF50;
}

.stats-value.expense {
  color: #F44336;
}

.stats-divider {
  width: 2rpx;
  background-color: #eee;
  margin: 10rpx 0;
}

/* 操作按钮 */
.action-panel {
  background-color: #fff;
  border-radius: 16rpx;
  box-shadow: 0 4rpx 12rpx rgba(0, 0, 0, 0.05);
  padding: 30rpx;
  margin-bottom: 40rpx;
}

.action-row {
  display: flex;
  justify-content: space-around;
  margin-bottom: 30rpx;
}

.action-row:last-child {
  margin-bottom: 0;
}

.action-btn {
  display: flex;
  flex-direction: column;
  align-items: center;
  width: 33.33%;
}

.action-btn van-icon {
  margin-bottom: 10rpx;
}

.action-btn text {
  font-size: 26rpx;
  color: #666;
}

.action-btn.warning text {
  color: #F44336;
}

/* 分类部分 */
.category-section {
  background-color: #fff;
  border-radius: 16rpx;
  box-shadow: 0 4rpx 12rpx rgba(0, 0, 0, 0.05);
  padding: 30rpx;
}

.section-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 20rpx;
}

.section-title {
  font-size: 32rpx;
  font-weight: bold;
  color: #333;
}

.section-action {
  display: flex;
  align-items: center;
  font-size: 26rpx;
  color: #666;
}

.section-action van-icon {
  margin-left: 6rpx;
}

.empty-tip {
  padding: 40rpx 0;
  text-align: center;
  color: #999;
  font-size: 28rpx;
}

.category-grid {
  display: flex;
  flex-wrap: wrap;
}

.category-item {
  width: 25%;
  display: flex;
  flex-direction: column;
  align-items: center;
  margin-bottom: 30rpx;
}

.category-icon {
  width: 80rpx;
  height: 80rpx;
  border-radius: 50%;
  display: flex;
  align-items: center;
  justify-content: center;
  margin-bottom: 10rpx;
}

.category-icon image {
  width: 60%;
  height: 60%;
  object-fit: contain;
}

.category-icon text {
  color: #fff;
  font-size: 32rpx;
  font-weight: bold;
}

.category-name {
  font-size: 24rpx;
  color: #666;
  text-align: center;
  max-width: 120rpx;
  overflow: hidden;
  text-overflow: ellipsis;
  white-space: nowrap;
}

/* 删除确认弹窗 */
.confirm-dialog {
  width: 560rpx;
  padding: 40rpx 30rpx;
}

.confirm-title {
  font-size: 32rpx;
  font-weight: bold;
  color: #333;
  text-align: center;
  margin-bottom: 20rpx;
}

.confirm-content {
  font-size: 28rpx;
  color: #666;
  text-align: center;
  margin-bottom: 30rpx;
}

.confirm-actions {
  display: flex;
  border-top: 1rpx solid #eee;
}

.confirm-btn {
  flex: 1;
  text-align: center;
  padding: 20rpx 0;
  font-size: 30rpx;
}

.confirm-btn.cancel {
  color: #666;
}

.confirm-btn.confirm {
  color: #F44336;
  font-weight: bold;
}

/* 滚动区域 */
.bill-scroll-view {
  flex: 1;
  height: 33vh;
  /* 设置适当的高度，这里使用视口高度的60% */
  width: 100%;
  box-sizing: border-box;
}

/* 账单列表 */
.bill-list {
  width: 100%;
  box-sizing: border-box;
  padding: 0 20rpx;
  height: 100vh;
}

/* 一列 */
.oneColonm {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 20rpx 40rpx;
  font-size: 28rpx;
  color: #333;
}

.oneColonm .time {
  font-size: 24rpx;
  /* Color and background-color now set via inline style */
  padding: 6rpx 20rpx;
  border-radius: 20rpx;
}

/* 日期显示组件 */
.day-display {
  display: flex;
  align-items: center;
  background-color: rgba(255, 255, 255, 0.8);
  border-radius: 50rpx;
  padding: 10rpx 20rpx 10rpx 10rpx;
  margin: 30rpx 0;
  width: fit-content;
}

.day-icon {
  width: 50rpx;
  height: 50rpx;
  border-radius: 50%;
  overflow: hidden;
  margin-right: 20rpx;
  display: flex;
  align-items: center;
  justify-content: center;
}

.day-info {
  font-size: 24rpx;
  color: #333;
  font-weight: 500;
}

.day-icon-image {
  width: 100%;
  height: 100%;
  border-radius: 50%;
}

/* 年月选择器 */
.year-month-selector {
  display: flex;
  align-items: center;
  padding: 15rpx 0;
  margin: 10rpx 40rpx;
  font-size: 32rpx;
  font-weight: 500;
  color: #333;
  cursor: pointer;
}

.year-month-selector text {
  margin-right: 8rpx;
}

.year-month-selector-item {
  display: inline-block;
}

.content-container {
  background-color: #fff;
}