// packageA/pages/bookSetting/billTransfer/billTransfer.js
import { getAccountBookList,getAccountBookDetail,transferBook } from '../../../../api/book/index';
  const util = require('../../../../utils/index.js');
  Page({

  /**
   * 页面的初始数据
   */
  data: {
    sourceBook: '默认账本',
    targetBook: '', // 目标账本
    targetBookImage: '', // 目标账本图片
    bookList: [], // 实际账本列表，从API获取
    showBookSelector: false, // 控制账本选择器显示状态
    sourceBookId: '', // 源账本ID
    selectedTargetBookId: '', // 选中的目标账本ID
    accountBookDetail: {}, // 源账本详情
    username: '', // 用户名输入字段
    inputFocusStates: {} // 存储输入框焦点状态
  },

  /**
   * 生命周期函数--监听页面加载
   */
  onLoad(options) {
    console.log(options);
    
    // 如果有传入源账本参数
    if (options.id) {
      this.setData({
        sourceBookId: options.id
      });
      // 根据账本ID获取账本信息
      this.getBookDetailInfo(options.id);
    }

    // 获取账本列表
    this.getBookList();
  },

  /**
   * 处理输入框值变化
   */
  onInputChange: function(e) {
    const { field, value } = e.detail;
    
    // 使用动态属性名更新对应字段的值
    this.setData({
      [field]: value
    });
    
    console.log(`字段 ${field} 的值已更改为: ${value}`);
    
    // 根据不同字段执行特定逻辑
    if (field === 'username') {
      // 这里可以添加针对用户名的特定处理
      // 例如: 验证格式、检查长度等
    }
  },
  
  /**
   * 处理输入框获取焦点
   */
  onInputFocus: function(e) {
    const { field } = e.detail;
    
    // 更新对应字段的焦点状态
    this.setData({
      [`inputFocusStates.${field}`]: true
    });
  },
  
  /**
   * 处理输入框失去焦点
   */
  onInputBlur: function(e) {
    const { field, value } = e.detail;
    
    // 更新对应字段的焦点状态
    this.setData({
      [`inputFocusStates.${field}`]: false
    });
    
    // 可以在这里添加输入值的验证逻辑
    if (field === 'username' && !value) {
      // 用户名为空的处理
      wx.showToast({
        title: '用户名不能为空',
        icon: 'none'
      });
    }
  },

    // 获取账本列表
    getBookList: function () {
      wx.showLoading({
        title: '加载中...',
      });
  
      getAccountBookList().then(res => {
        wx.hideLoading();
  
        if (res && res.code === 1) {
          const bookList = res.data || [];
          
          // 处理数据，为每个账本添加背景色
          bookList.forEach((item, index) => {
            // 设置图片路径
            item.image = item.image ? util.getImageUrl(item.image) : '';
            
            // 如果没有指定背景色，从颜色数组中循环选择
            // if (!item.bgColor) {
            //   item.bgColor = this.data.bookColors[index % this.data.bookColors.length];
            // }
            
            // 设置默认账本标识
            item.isDefault = item.is_default === "1";
          });
          
          // 过滤掉当前源账本，不能迁移到自己
          const filteredBookList = bookList.filter(book => book.id !== this.data.sourceBookId);
          
          this.setData({
            bookList: filteredBookList
          });
        } else {
          // API请求失败，使用示例数据
          console.error('获取账本列表失败:', res);
          // 非会员状态下不提示错误
          if (this.data.isVip) {
          wx.showToast({
            title: '获取账本列表失败',
            icon: 'none'
          });
          }
        }
      }).catch(err => {
        wx.hideLoading();
        // 非会员状态下不提示错误
        if (this.data.isVip) {
        wx.showToast({
          title: '网络请求失败',
          icon: 'none'
        });
        }
        console.error('获取账本列表失败:', err);
      });
    },

    /**
     * 获取账本详情信息
     */
    getBookDetailInfo(bookId) {
      wx.showLoading({
        title: '加载中...',
      });
      
      getAccountBookDetail({id: bookId}).then(res => {
        wx.hideLoading();
        
        if (res && res.code === 1) {
          const bookDetail = res.data || {};
          console.log(bookDetail,'bookDetail');
          
          
          // 设置封面图片完整路径
          if (bookDetail.image) {
            
            bookDetail.cover_img = util.getImageUrl(bookDetail.image);
          }
          
          this.setData({
            accountBookDetail: bookDetail,
            sourceBook: bookDetail.name || '默认账本'
          });
        } else {
          console.error('获取账本详情失败:', res);
          wx.showToast({
            title: '获取账本详情失败',
            icon: 'none'
          });
        }
      }).catch(err => {
        wx.hideLoading();
        wx.showToast({
          title: '网络请求失败',
          icon: 'none'
        });
        console.error('获取账本详情失败:', err);
      });
    },

      // 关闭账本选择器
  closeBookSelector() {
    this.setData({
      showBookSelector: false
    });
  },

  //  选择账本
  onBookSelect(e) {
    console.log('账本选择：', e.detail);
    
    const selectedBook = e.detail;
    // 检查返回的数据结构，根据实际情况提取账本信息
    if (selectedBook) {
      let bookId = '';
      let bookName = '';
      let bookImage = '';
      
      // 根据返回的数据结构提取信息
      if (selectedBook.id) {
        // 如果是直接返回账本对象
        bookId = selectedBook.id;
        bookName = selectedBook.name;
        bookImage = selectedBook.image; // 获取图片路径
      } else if (selectedBook.bookId) {
        // 如果是通过bookId返回
        bookId = selectedBook.bookId;
        // 查找对应的账本
        const book = this.data.bookList.find(item => item.id === bookId || item.id === String(bookId));
        if (book) {
          bookName = book.name;
          bookImage = book.image; // 获取图片路径
        }
      }
      
      if (bookId) {
        // 更新选中的账本信息
        this.setData({
          selectedTargetBookId: bookId,
          targetBook: bookName,
          targetBookImage: bookImage, // 设置目标账本图片
          showBookSelector: false
        });
        
        console.log('已选择账本：', bookName, bookId, bookImage);
      }
    }
  },

  /**
   * 选择目标账本
   */
  selectTargetBook() {
    this.setData({
      showBookSelector: true
    });
  },

  /**
   * 确认迁移
   */
  confirmTransfer() {
    // 验证是否选择了目标账本
    if (!this.data.targetBook) {
      wx.showToast({
        title: '请选择目标账本',
        icon: 'none'
      });
      return;
    }
    
    // 确认对话框
    wx.showModal({
      title: '确认迁移',
      content: `确定要将${this.data.sourceBook}的账单迁移到${this.data.targetBook}吗？`,
      success: (res) => {
        if (res.confirm) {
          // 确认迁移
          this.doTransfer();
        }
      }
    });
  },
  
  /**
   * 执行迁移操作
   */
  doTransfer() {
    // 显示加载中
    wx.showLoading({
      title: '迁移中...',
      mask: true
    });

    const params = {
      accountbook_id: this.data.sourceBookId,
      migrate_id: this.data.selectedTargetBookId
    };
    
    // 调用迁移接口
    transferBook(params).then(res => {
      wx.hideLoading();
      
      if (res && res.code === 1) {
        // 显示成功提示
        wx.showToast({
          title: '迁移成功',
          icon: 'success',
          duration: 2000
        });
        // 延迟返回上一页
        setTimeout(() => {
          wx.navigateBack();
        }, 1000);
      } else {
        wx.showToast({
          title: res.msg || '迁移失败',
          icon: 'none'
        });
      }
    }).catch(err => {
      wx.hideLoading();
      wx.showToast({
        title: '网络请求失败',
        icon: 'none'
      });
      console.error('迁移账本失败:', err);
    });
  },
  
  /**
   * 返回上一页
   */
  goBack() {
    wx.navigateBack();
  },

  /**
   * 生命周期函数--监听页面初次渲染完成
   */
  onReady() {

  },

  /**
   * 生命周期函数--监听页面显示
   */
  onShow() {

  },

  /**
   * 生命周期函数--监听页面隐藏
   */
  onHide() {

  },

  /**
   * 生命周期函数--监听页面卸载
   */
  onUnload() {

  },

  /**
   * 页面相关事件处理函数--监听用户下拉动作
   */
  onPullDownRefresh() {

  },

  /**
   * 页面上拉触底事件的处理函数
   */
  onReachBottom() {

  },

  /**
   * 用户点击右上角分享
   */
  onShareAppMessage() {

  }
})