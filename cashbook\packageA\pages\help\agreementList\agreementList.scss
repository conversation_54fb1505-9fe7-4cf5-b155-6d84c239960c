/* packageA/pages/help/agreementList/agreementList.wxss */
.agreement-list-container {
  background-color: #f5f5f5;
  min-height: 100vh;
  padding: 0 0 30rpx 0;
  position: relative;
  
  .nav {
    margin-bottom: 20rpx;
    position: fixed;
    top: 0;
    left: 0;
    right: 0;
    z-index: 200;
  }
  
  // 导航栏占位符，防止内容被遮挡
  .nav-placeholder {
    height: 180rpx; // 根据导航栏实际高度调整
  }
  
  // 加载中提示
  .loading, .empty-state {
    text-align: center;
    padding: 50rpx 0;
    color: #999999;
    font-size: 28rpx;
  }
  
  // 协议列表
  .agreement-list {
    padding: 20rpx 30rpx;
    
    // 协议项
    .agreement-item {
      background-color: #ffffff;
      border-radius: 16rpx;
      padding: 30rpx;
      margin-bottom: 20rpx;
      box-shadow: 0 2rpx 10rpx rgba(0, 0, 0, 0.05);
      
      // 头部区域（标题和箭头）
      .item-header {
        display: flex;
        justify-content: space-between;
        align-items: center;
        margin-bottom: 15rpx;
        
        .item-title {
          font-size: 32rpx;
          font-weight: bold;
          color: #333333;
          flex: 1;
        }
        
        .item-arrow {
          width: 40rpx;
          height: 40rpx;
          display: flex;
          align-items: center;
          justify-content: center;
          color: #999999;
        }
      }
      
      // 描述文本
      .item-desc {
        font-size: 28rpx;
        color: #666666;
        margin-bottom: 15rpx;
        // 最多显示两行
        overflow: hidden;
        text-overflow: ellipsis;
        display: -webkit-box;
        -webkit-line-clamp: 2;
        -webkit-box-orient: vertical;
      }
      
      // 时间信息
      .item-time {
        font-size: 24rpx;
        color: #999999;
      }
    }
  }
}