.box{
  position: fixed;
  bottom: 0px;
  left: 0;
  width: 100%;
  background-color: #fefcfd;
  padding: 10px 0px 20px;
  font-size: 36rpx;
  height: 50px;
}
.list{
  display: grid;
  grid-template-columns: repeat(4,1fr);
  align-self: center;
  text-align: center;
  align-items: end;
}
.icon{

}
.icon image{
  width: 30px;
  height: 30px;
  object-fit: cover;
}
.item{
  color: #d0cecf;

}
.active{
  color: #709b31;
  /* background-color: rgb(226, 90, 90); */
}

.vipinfo{
  position: absolute;
  top: -35px;
  left: 50%;
  transform: translateX(-50%);
  background-color: #a1b386;
  border-radius: 10px 10px 0 0;
  width: fit-content;
  padding: 5px 10px;
  display: grid;
  grid-template-columns: auto auto auto;
  gap: 10px;
}

.activeInfo{
  position: absolute;
  top: -50px;
  right: 0;
  width: 50px;
  height: 50px;
  /* border: 1px solid red; */
}
.activeInfo image{
     width: 50px;
     height: 50px;
}

/* 卡片 */
.card{
  border-radius: 10px;
  position: fixed;
  bottom: 0;
  left: 0;
  width: 100%;
  z-index: 9999;
  background-color: #fff;
  /* position: fixed;
  bottom: 0;
  z-index: 99999;
  width: 100%; */
}
.card-header{
  display: grid;
  grid-template-columns: 1fr 1fr 1fr;
  align-self: center;
  align-items: center;
  padding: 10px;
  /* border: 1px solid; */
}
.card-header view:nth-child(1){
 background-color: #e4e4e4;
 color: #7c7c7c;
 display: grid;
 align-self: center;
 text-align: center;
}
.card-header view:nth-child(2){
  place-self: center;
 }
.card-header view:nth-child(3){
   justify-self: end;
  font-size: 24rpx;
  color: #b2b2b2;
  background-color: #f4f4f4;
  padding: 2px;
  border-radius: 5px;
}

.close{
  width: 30px;
  height: 30px;
  border-radius: 50%;
  background-color: #fff;
}

.card-content{
  margin: 30px 0;
  text-align: center;
}
.card-content view:nth-child(1){
  font-size: 48rpx;
}
.card-content view:nth-child(2){
  font-size: 38rpx;
}
.joninNow{
  background-color: #d2dec6;
  margin:  0 20px 20px 20px;
  padding: 10px 0;
  text-align: center;
  border-radius: 15px;
}

.mask {
  position: fixed;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
  background-color: rgba(0, 0, 0, 0.5); /* 半透明黑色背景 */
  z-index: 99; /* 确保遮罩层在最上层 */
}