// pages/accountSecurity/verifyPhone/verifyPhone.js
Page({
  data: {
    phoneNumber: '183****7863', // 默认手机号，实际应从上一页传入或从用户信息获取
    codeValues: ['', '', '', ''], // 验证码输入值
    currentFocus: 0 // 当前聚焦的输入框
  },

  onLoad: function(options) {
    // 如果有传入的手机号，则使用传入的手机号
    if (options.phone) {
      this.setData({
        phoneNumber: options.phone
      });
    }
  },

  // 输入验证码
  inputCode: function(e) {
    const { index } = e.currentTarget.dataset;
    const { value } = e.detail;
    
    // 更新当前输入框的值
    const codeValues = [...this.data.codeValues];
    codeValues[index] = value;
    
    // 自动聚焦下一个输入框
    let nextFocus = index;
    if (value && index < 3) {
      nextFocus = index + 1;
    }
    
    this.setData({
      codeValues,
      currentFocus: nextFocus
    });
  },

  // 清除验证码
  clearCode: function() {
    this.setData({
      codeValues: ['', '', '', ''],
      currentFocus: 0
    });
  },

  // 验证验证码
  verifyCode: function() {
    const code = this.data.codeValues.join('');
    
    // 验证码长度检查
    if (code.length !== 4) {
      wx.showToast({
        title: '请输入完整验证码',
        icon: 'none'
      });
      return;
    }
    
    // 这里应该调用API验证验证码
    // 模拟验证成功
    wx.navigateTo({
      url: '/pages/accountSecurity/resetPassword/resetPassword'
    });
  },

  // 返回上一页
  goBack: function() {
    wx.navigateBack();
  }
});