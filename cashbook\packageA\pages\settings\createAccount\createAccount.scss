/* 新建账户页面样式 */

/* 顶部状态栏 */
.status-bar {
  width: 100%;
  background-color: #ffffff;
}

/* 顶部导航栏 */
.navigation-bar {
  display: flex;
  align-items: center;
  padding: 0 30rpx;
  height: 88rpx;
  background-color: #ffffff;
  position: relative;

  .nav-left {
    width: 60rpx;
    height: 60rpx;
    display: flex;
    align-items: center;
    justify-content: center;
  }

  .nav-title {
    position: absolute;
    left: 0;
    right: 0;
    text-align: center;
    font-size: 36rpx;
    font-weight: 500;
    color: #333333;
    pointer-events: none;
  }
}

/* 主内容区域 */
.create-account-container {
  padding: 0 30rpx;
  background-color: #f8f8f8;
  min-height: calc(100vh - 88rpx);
  padding-bottom: 120rpx; /* 为固定底部按钮留出空间 */

  /* 区块标题 */
  .section-title {
    font-size: 32rpx;
    font-weight: 500;
    color: #333333;
    margin: 30rpx 0 20rpx;
  }

  /* 带侧边栏的区块标题 */
  .side-bar-section {
    display: flex;
    align-items: center;
    margin: 30rpx 0 20rpx;

    .side-bar {
      width: 8rpx;
      height: 32rpx;
      border-radius: 4rpx;
      margin-right: 10rpx;
      /* 背景颜色通过内联样式动态设置 */
    }

    .section-title {
      font-size: 32rpx;
      font-weight: 500;
      color: #333333;
      margin: 0;
    }
  }

  /* 资产类型选择器 */
  .asset-type-selector {
    display: flex;
    justify-content: space-between;
    align-items: center;
    padding: 20rpx 30rpx;
    background-color: #ffffff;
    border-radius: 16rpx;
    margin-bottom: 30rpx;

    .asset-type-left {
      display: flex;
      align-items: center;

      .asset-icon {
        width: 80rpx;
        height: 80rpx;
        border-radius: 40rpx;
        overflow: hidden;
        margin-right: 20rpx;
        display: flex;
        align-items: center;
        justify-content: center;
        background-color: #f5f5f5;

        image {
          width: 60%;
          height: 60%;
          object-fit: contain;
        }
      }

      .asset-name {
        font-size: 32rpx;
        color: #333333;
      }
    }

    .asset-type-right {
      display: flex;
      align-items: center;

      .arrow-icon {
        color: #cccccc;
        font-size: 32rpx;
      }
    }
  }

  /* 表单项 */
  .form-item {
    position: relative;
    margin-bottom: 20rpx;

    .form-input {
      width: 100%;
      height: 90rpx;
      background-color: #ffffff;
      border-radius: 16rpx;
      padding: 0 30rpx;
      font-size: 32rpx;
      box-sizing: border-box;
      color: #333333;

      &::placeholder {
        color: #999999;
      }
    }

    .input-icon {
      position: absolute;
      right: 30rpx;
      top: 50%;
      transform: translateY(-50%);

      .icon-image {
        width: 40rpx;
        height: 40rpx;
        opacity: 0.5;
      }
    }
  }

  /* 选择器项 */
  .selector-item {
    display: flex;
    justify-content: space-between;
    align-items: center;
    padding: 0 30rpx;
    height: 90rpx;
    background-color: #ffffff;
    border-radius: 16rpx;

    .selector-text {
      font-size: 32rpx;
      color: #999999;
    }

    .selector-button {
      padding: 10rpx 30rpx;
      font-size: 28rpx;
      border-radius: 30rpx;
      /* 背景颜色和文字颜色通过内联样式动态设置 */
    }
  }

  /* 分隔线 */
  .section-divider {
    display: flex;
    align-items: center;
    margin: 40rpx 0 20rpx;

    .divider-line {
      flex: 1;
      height: 1rpx;
      background-color: #eeeeee;
    }

    .divider-text {
      padding: 0 20rpx;
      font-size: 28rpx;
      color: #999999;
    }
  }

  /* 开关项 */
  .toggle-item {
    display: flex;
    justify-content: space-between;
    align-items: center;
    padding: 20rpx 0;

    .toggle-left {
      .toggle-title {
        font-size: 32rpx;
        color: #333333;
        margin-bottom: 6rpx;
      }

      .toggle-desc {
        font-size: 24rpx;
        color: #999999;
      }
    }

    .toggle-right {
      switch {
        transform: scale(0.9);
        /* 开关颜色通过内联样式动态设置 */
      }
    }
  }

  /* 额外描述文本 */
  .toggle-desc-extra {
    font-size: 24rpx;
    color: #999999;
    line-height: 1.6;
    margin: 10rpx 0 30rpx;
  }
}

/* 保存按钮 - 固定在底部 */
.save-button {
  position: fixed;
  left: 30rpx;
  right: 30rpx;
  bottom: 40rpx;
  height: 90rpx;
  border-radius: 45rpx;
  display: flex;
  align-items: center;
  justify-content: center;
  font-size: 34rpx;
  color: #ffffff;
  z-index: 100;
  /* 背景颜色通过内联样式动态设置 */
}

/* 资产类型选择弹窗 */
.asset-type-popup {
  padding: 0 15rpx;

  /* 顶部标题和标签区域 */
  .asset-type-header {
    display: flex;
    justify-content: space-between;
    align-items: center;
    height: 100rpx;
    padding: 0 15rpx;
    border-bottom: 1rpx solid #f0f0f0;

    .asset-type-title {
      font-size: 32rpx;
      font-weight: 500;
      color: #333333;
    }

    .asset-type-tab {
      padding: 8rpx 24rpx;
      font-size: 28rpx;
      color: #ffffff;
      background-color: #2c3e50;
      border-radius: 30rpx;
    }
  }

  /* 资产类型列表 */
  .asset-list {
    max-height: 40vh;
    overflow-y: auto;
    padding: 20rpx 0;

    .asset-item {
      display: flex;
      align-items: center;
      padding: 20rpx 15rpx;
      border-bottom: 1rpx solid #f5f5f5;

      &:last-child {
        border-bottom: none;
      }

      .asset-item-left {
        display: flex;
        align-items: center;
        width: 100%;

        .asset-icon-container {
          width: 70rpx;
          height: 70rpx;
          border-radius: 35rpx;
          display: flex;
          align-items: center;
          justify-content: center;
          margin-right: 20rpx;
          background-color: #f5f5f5;

          .asset-icon-image {
            width: 60%;
            height: 60%;
            object-fit: contain;
          }
        }

        .asset-info {
          flex: 1;

          .asset-name {
            font-size: 28rpx;
            color: #333333;
            margin-bottom: 4rpx;
          }

          .asset-type {
            font-size: 22rpx;
            color: #999999;
          }
        }
      }
    }
  }
}

/* 信用卡额外样式 */
.form-desc {
  font-size: 24rpx;
  color: #999999;
  padding: 10rpx 0 30rpx;
  line-height: 1.5;
}

.form-date-item {
  display: flex;
  justify-content: space-between;
  align-items: center;
  height: 90rpx;
  background-color: #ffffff;
  border-radius: 16rpx;
  padding: 0 30rpx;
  margin-bottom: 20rpx;

  .form-date-label {
    font-size: 32rpx;
    color: #333333;
  }

  .form-date-value {
    font-size: 32rpx;
    color: #333333;
    font-weight: 500;
  }
}

/* 自定义分组弹窗样式 */
.custom-group-popup {
  width: 100%;

  .group-item {
    width: 100%;
    height: 100rpx;
    line-height: 100rpx;
    text-align: center;
    font-size: 32rpx;
    color: #333333;
    border-bottom: 1rpx solid #f5f5f5;

    &:last-child {
      border-bottom: none;
    }
  }
}

/* 自定义图片名称弹窗样式 */
.custom-image-popup {
  padding: 24rpx;
  width: 600rpx;
  border-radius: 16rpx;
}

.custom-image-title {
  text-align: center;
  font-size: 32rpx;
  font-weight: 500;
  margin-bottom: 30rpx;
  position: relative;
  display: flex;
  justify-content: center;
  align-items: center;
}

.crown-icon {
  margin-left: 10rpx;
  width: 40rpx;
  height: 40rpx;
}

.crown-icon image {
  width: 100%;
  height: 100%;
}

.custom-image-form {
  display: flex;
  flex-direction: column;
  gap: 20rpx;
}

.custom-image-form .form-item {
  width: 100%;
}

.custom-image-form .form-input {
  width: 100%;
  height: 90rpx;
  background-color: #f5f5f5;
  border-radius: 16rpx;
  padding: 0 20rpx;
  font-size: 28rpx;
  box-sizing: border-box;
}

.custom-save-button {
  width: 100%;
  height: 90rpx;
  background: linear-gradient(to right, #8fda9d, #52c873);
  border-radius: 45rpx;
  color: #fff;
  font-size: 32rpx;
  display: flex;
  justify-content: center;
  align-items: center;
  margin-top: 30rpx;
}

/* 自定义分组输入区域 */
.group-name-section {
  background-color: #ffffff;
  border-radius: 16rpx;
  padding: 0 30rpx;
  margin-bottom: 30rpx;
  position: relative;
}

.group-name-label {
  font-size: 28rpx;
  color: #999999;
  padding-top: 15rpx;
  transition: all 0.3s ease;

  &.focused {
    font-size: 24rpx;
    position: absolute;
    top: 0;
    left: 30rpx;
    padding-top: 8rpx;
  }
}

.group-name-input-container {
  display: flex;
  justify-content: space-between;
  align-items: center;
  height: 80rpx;
  position: relative;
}

.group-name-input {
  flex: 1;
  height: 100%;
  font-size: 32rpx;
  color: #333333;
  padding-top: 10rpx;
}

.group-name-button {
  padding: 8rpx 30rpx;
  font-size: 28rpx;
  border-radius: 30rpx;
  margin-left: 10rpx;
  background-color: rgba(32, 204, 82, 0.08);
  color: #20cc52;
}

/* 分组名称输入包装器 */
.group-name-input-wrapper {
  flex: 1;
  position: relative;
}

.label-text {
  font-size: 28rpx;
  color: #999999;
  padding-top: 5rpx;
}

.group-name-input {
  width: 100%;
  height: 70rpx;
  font-size: 32rpx;
  color: #333333;

  &.focused {
    padding-top: 20rpx;
  }
}

.group-name-button {
  padding: 8rpx 30rpx;
  font-size: 28rpx;
  border-radius: 30rpx;
  margin-left: 10rpx;
  background-color: rgba(32, 204, 82, 0.08);
  color: #20cc52;
}

/* 报销和借出特有样式 */
.status-selector {
  display: flex;
  background-color: #ffffff;
  border-radius: 16rpx;
  margin-bottom: 30rpx;
  overflow: hidden;

  .status-item {
    flex: 1;
    height: 90rpx;
    display: flex;
    align-items: center;
    justify-content: center;
    font-size: 32rpx;
    color: #333333;
    position: relative;

    &.active {
      color: #20cc52;
      font-weight: 500;

      &:after {
        content: '';
        position: absolute;
        bottom: 0;
        left: 50%;
        transform: translateX(-50%);
        width: 40rpx;
        height: 4rpx;
        background-color: #20cc52;
        border-radius: 2rpx;
      }
    }
  }
}

/* 银行选择弹窗样式 */
.bank-selector-popup {
  width: 100%;
  height: 100%;
  display: flex;
  flex-direction: column;
  background-color: #fff;
  border-radius: 20rpx 20rpx 0 0;
  overflow: hidden;
}

/* 顶部标题区域 */
.bank-popup-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 30rpx 30rpx 20rpx;
  border-bottom: 1rpx solid #f5f5f5;
  flex-shrink: 0;
}

.bank-popup-title {
  font-size: 34rpx;
  font-weight: 500;
  color: #333;
}

.bank-popup-custom {
  font-size: 28rpx;
  color: #333;
  padding: 8rpx 20rpx;
  background-color: #f5f5f5;
  border-radius: 30rpx;
}

/* 搜索区域 */
.bank-search-container {
  padding: 20rpx 30rpx;
  background-color: #fff;
  flex-shrink: 0;
}

.bank-search-box {
  display: flex;
  align-items: center;
  height: 80rpx;
  background-color: #f5f5f5;
  border-radius: 40rpx;
  padding: 0 30rpx;
}

.bank-search-icon {
  width: 40rpx;
  height: 40rpx;
  margin-right: 20rpx;
  display: flex;
  align-items: center;
  justify-content: center;
  opacity: 0.5;
}

.bank-search-icon image {
  width: 80%;
  height: 80%;
}

.bank-search-input {
  flex: 1;
  height: 80rpx;
  font-size: 28rpx;
  color: #333;
}

.bank-search-input::placeholder {
  color: #999;
}

/* 银行列表区域 */
.bank-list-container {
  flex: 1;
  position: relative;
  overflow: hidden;
  padding-right: 40rpx; /* 为字母索引留出空间 */
  display: flex;
  flex-direction: column;
  min-height: 200px; /* 确保有足够高度进行滚动 */
}

.bank-list {
  height: 400px;
  // flex:1;
  padding: 0 30rpx;
  box-sizing: border-box;
  overflow-y: auto; /* 确保可以垂直滚动 */
}

.bank-initial {
  padding: 16rpx 0;
  font-size: 28rpx;
  color: #999;
  background-color: #f8f8f8;
}

.bank-item {
  display: flex;
  align-items: center;
  padding: 24rpx 0;
  border-bottom: 1rpx solid #f5f5f5;
}

.bank-icon {
  width: 60rpx;
  height: 60rpx;
  margin-right: 30rpx;
  border-radius: 50%;
  overflow: hidden;
  display: flex;
  align-items: center;
  justify-content: center;
}

.bank-icon image {
  width: 80%;
  height: 80%;
  object-fit: contain;
}

.bank-name {
  font-size: 30rpx;
  color: #333;
  flex: 1;
}

.bank-favorite {
  width: 40rpx;
  height: 40rpx;
  display: flex;
  align-items: center;
  justify-content: center;
}

.bank-favorite image {
  width: 80%;
  height: 80%;
  object-fit: contain;
}

/* 右侧字母索引 */
.letter-index {
  position: fixed;
  right: 20rpx;
  top: 50%;
  transform: translateY(-50%);
  display: flex;
  flex-direction: column;
  align-items: center;
  z-index: 10;
  background-color: transparent;
}

.letter-item {
  padding: 4rpx 6rpx;
  font-size: 24rpx;
  color: #999;
  line-height: 1.2;
}

.letter-item.active {
  color: #333;
  font-weight: bold;
}
