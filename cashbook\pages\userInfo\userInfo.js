// pages/userInfo/userInfo.js
import { getUserInfo, updateUserInfo } from '../../api/user/index';
import uploadFile from '../../api/upload';

Page({
  data: {
    userInfo: {
      avatar: '/static/icon/avator.png',
      nickname: '白色风车',
      joinDate: '2025年3月加入记账队伍',
      memberCode: '7973750784',
      joinTime: '2025-03-29'
    },
    isVip: false,
    showNicknameDialog: false,
    tempNickname: ''
  },

  onLoad: function(options) {
    // 页面加载时获取用户信息
    this.getUserInfo();
  },

  // 获取用户信息
  getUserInfo: function() {
    wx.showLoading({
      title: '加载中...',
    });
    
    // 调用封装的用户信息接口
    getUserInfo().then(res => {
      wx.hideLoading();
      
      if (res && res.code === 1) {
        const userData = res.data;
        
        // 格式化加入时间
        const joinDate = this.formatJoinDate(userData.id);
        
        // 更新用户信息
        this.setData({
          userInfo: {
            avatar: userData.avatar || '/static/icon/avator.png',
            nickname: userData.nickname || '用户',
            mobile: userData.mobile || '',
            memberCode: userData.id || '',
            joinTime: this.formatDate(userData.id),
            joinDate: joinDate
          },
          isVip: userData.is_vip === "1"
        });
      } else {
        wx.showToast({
          title: '获取用户信息失败',
          icon: 'none'
        });
      }
    }).catch(err => {
      wx.hideLoading();
      wx.showToast({
        title: '网络请求失败',
        icon: 'none'
      });
      console.error('获取用户信息失败:', err);
    });
  },

  // 格式化加入日期（年月）
  formatJoinDate: function(timestamp) {
    if (!timestamp) return '加入记账队伍';
    
    const date = new Date(timestamp * 1000);
    const year = date.getFullYear();
    const month = date.getMonth() + 1;
    
    return `${year}年${month}月加入记账队伍`;
  },

  // 格式化日期为 YYYY-MM-DD
  formatDate: function(timestamp) {
    if (!timestamp) return '';
    
    const date = new Date(timestamp * 1000);
    const year = date.getFullYear();
    const month = (date.getMonth() + 1).toString().padStart(2, '0');
    const day = date.getDate().toString().padStart(2, '0');
    
    return `${year}-${month}-${day}`;
  },

  // 返回上一页
  goBack: function() {
    wx.navigateBack({
      delta: 1
    });
  },

  // 复制会员编码
  copyMemberCode: function() {
    wx.setClipboardData({
      data: this.data.userInfo.memberCode.toString(),
      success: function() {
        wx.showToast({
          title: '会员编码已复制',
          icon: 'success',
          duration: 2000
        });
      }
    });
  },

  // 显示昵称修改弹窗
  showNicknameDialog: function() {
    this.setData({
      showNicknameDialog: true,
      tempNickname: this.data.userInfo.nickname
    });
  },

  // 隐藏昵称修改弹窗
  hideNicknameDialog: function() {
    this.setData({
      showNicknameDialog: false
    });
  },

  // 阻止事件冒泡
  stopPropagation: function() {
    return;
  },

  // 监听昵称输入
  onNicknameInput: function(e) {
    this.setData({
      tempNickname: e.detail.value
    });
  },

  // 保存昵称
  saveNickname: function() {
    const newNickname = this.data.tempNickname;
    
    if (!newNickname.trim()) {
      wx.showToast({
        title: '昵称不能为空',
        icon: 'none'
      });
      return;
    }
    
    wx.showLoading({
      title: '保存中...',
    });
    
    // 调用更新用户信息接口
    updateUserInfo({
      nickname: newNickname
    }).then(res => {
      wx.hideLoading();
      
      if (res && res.code === 1) {
        // 更新本地显示
        this.setData({
          'userInfo.nickname': newNickname,
          showNicknameDialog: false
        });
        
        // 更新缓存中的用户信息
        const userInfo = wx.getStorageSync('userInfo') || {};
        userInfo.nickname = newNickname;
        wx.setStorageSync('userInfo', userInfo);
        
        wx.showToast({
          title: '昵称修改成功',
          icon: 'success'
        });
      } else {
        wx.showToast({
          title: res.msg || '昵称修改失败',
          icon: 'none'
        });
      }
    }).catch(err => {
      wx.hideLoading();
      wx.showToast({
        title: '网络请求失败',
        icon: 'none'
      });
      console.error('修改昵称失败:', err);
    });
  },

  // 编辑昵称（原方法，现在改为显示弹窗）
  editNickname: function() {
    this.showNicknameDialog();
  },

  // 更换头像
  changeAvatar: function() {
    wx.chooseImage({
      count: 1,
      sizeType: ['compressed'],
      sourceType: ['album', 'camera'],
      success: (res) => {
        const tempFilePaths = res.tempFilePaths;
        
        // 显示上传中提示
        wx.showLoading({
          title: '上传中...',
        });
        
        // 使用封装的uploadFile函数上传头像
        uploadFile({
          url: 'common/upload',  // 注意这里使用相对路径
          filePath: tempFilePaths[0],
          name: 'file',
          formData: {
            // 可以添加其他表单数据
          }
        }).then(data => {
          if (data.code === 1) {
            // 上传成功，获取服务器返回的头像URL
            const avatarUrl = data.data.fullurl || data.data.path || data.data.avatar;
            
            // 调用updateUserInfo接口更新用户头像
            return updateUserInfo({
              avatar: avatarUrl
            }).then(updateRes => {
              wx.hideLoading();
              
              if (updateRes && updateRes.code === 1) {
                // 更新本地显示
                this.setData({
                  'userInfo.avatar': avatarUrl
                });
                
                // 更新缓存中的用户信息
                const userInfo = wx.getStorageSync('userInfo') || {};
                userInfo.avatar = avatarUrl;
                wx.setStorageSync('userInfo', userInfo);
                
                wx.showToast({
                  title: '头像更新成功',
                  icon: 'success'
                });
              } else {
                wx.showToast({
                  title: updateRes.msg || '头像更新失败',
                  icon: 'none'
                });
              }
              
              return updateRes;
            });
          } else {
            wx.hideLoading();
            wx.showToast({
              title: data.msg || '头像上传失败',
              icon: 'none'
            });
            return Promise.reject(new Error('上传失败'));
          }
        }).catch(err => {
          wx.hideLoading();
          wx.showToast({
            title: '头像上传失败',
            icon: 'none'
          });
          console.error('头像上传失败:', err);
        });
      }
    });
  }
});