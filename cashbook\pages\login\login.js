// pages/login/login.js
Page({
  data: {
    isAgree: false
  },

onLoad: async function(options) {
    // 页面加载时的逻辑
  },

  // 微信登录
  wechatLogin: function() {
    if (!this.data.isAgree) {
      wx.showToast({
        title: '请先同意隐私政策',
        icon: 'none'
      });
      return;
    }
    
    wx.showLoading({
      title: '登录中...',
    });
    
    // 调用微信登录接口
    wx.login({
      success: (res) => {
        if (res.code) {
          // 获取到登录code后，调用后端接口
          const loginData = {
            code: res.code,
            // iv和encryptedData暂时不传，但保留相关逻辑
            // iv: '',
            // encryptedData: '',
            // from_user_id: ''
          };
          
          // 导入登录API
          const { wxLogin } = require('../../api/login/index');
          
          // 调用登录接口
          wxLogin(loginData).then(response => {
            wx.hideLoading();
            
            if (response.code === 1) {
              // 登录成功，保存用户信息
              const userInfo = response.data.userinfo;
              wx.setStorageSync('token', userInfo.token);
              wx.setStorageSync('userInfo', userInfo);
              
              // 登录成功后的处理
              this.loginSuccess();
            } else {
              wx.showToast({
                title: response.msg || '登录失败',
                icon: 'none'
              });
            }
          }).catch(error => {
            wx.hideLoading();
            wx.showToast({
              title: '登录失败，请稍后重试',
              icon: 'none'
            });
            console.error('登录失败:', error);
          });
        } else {
          wx.hideLoading();
          wx.showToast({
            title: '登录失败',
            icon: 'none'
          });
        }
      },
      fail: () => {
        wx.hideLoading();
        wx.showToast({
          title: '登录失败',
          icon: 'none'
        });
      }
    });
  },

  // 手机号登录
  phoneLogin: function() {
    if (!this.data.isAgree) {
      wx.showToast({
        title: '请先同意隐私政策',
        icon: 'none'
      });
      return;
    }
    
    // 跳转到手机号登录页面
    wx.navigateTo({
      url: '/pages/login/phoneLogin/phoneLogin',
    });
  },

  // 隐私政策勾选变化
  checkboxChange: function(e) {
    this.setData({
      isAgree: e.detail.value.length > 0
    });
  },

  // 显示隐私政策
  showPrivacyPolicy: function() {
    wx.navigateTo({
      url: '/pages/login/privacy/privacy',
    });
  },

  // 登录成功后的处理
  loginSuccess: function() {
    // 设置登录状态
    wx.setStorageSync('isLoggedIn', true);
    
    // 返回上一页或首页
    const pages = getCurrentPages();
    if (pages.length > 1) {
      wx.navigateBack();
    } else {
      wx.switchTab({
        url: '/pages/index2/index2',
      });
    }
  }
})