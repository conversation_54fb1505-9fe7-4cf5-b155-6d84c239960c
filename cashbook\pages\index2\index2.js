const app = getApp()
import { getUserAccounts, getUserAccountDetail } from '../../api/account/index'

Page({
  /**
   * 页面的初始数据
   */
  data: {
    selectedColor: '',
    // 当前激活的标签：bill(账单) 或 asset(资产)
    activeTab: 'bill',
    list: [
      {
        isTransfer: true,
        time: '5月8日',
        istoday: '昨天',
        desc: '【资产初始化】初期余额*****************',
        amount: '100.00',
        remark: '人人认可阿斯达啊大大撒阿萨大大',
        payAccount: '100.00',
        date: '2023-05-08'
      },
      {
        isTransfer: true,
        time: '5月10日',
        istoday: '今天',
        desc: '【资产初始化】初期余额*****************',
        amount: '************.00',
        remark: '人人认可',
        payAccount: '************.00',
        date: '2023-05-10'
      },
      {
        isTransfer: true,
        time: '5月10日',
        istoday: '今天',
        desc: '【资产初始化】初期余额100.0',
        amount: '100.00',
        remark: '豪华地区哦',
        payAccount: '100.00',
        date: '2023-05-10'
      }
    ],
    showCard: false,
    showAccount: true,
    vipDialog: false,
    swpCurrent: 0, //首页list切换
    sortType: 'time', // 初始排序类型
    swpTabCurrent: 0, //卡片切换
    swperHeight: null,
    mySclace: 'none', //导航栏样式
    chagePosition: 'none',
    navHeight: null,
    blockHeight: 200,
    cashbooks: [
      { id: 1, name: '默认账本' }
      // 如果有多个账本，可以添加更多账本信息
      // { id: 2, name: '购物账本' }
    ],
    showBookSelector: false,
    bookList: [],
    selectedBookIds: [],
    allBooksSelected: false,
    isCardMode: true,
    isMultiSelect: false,
    currentBookName: '全部账本',
    currentBookId: 0,
    loading: false,

    // 资产列表数据
    assetList: [],
    // 资产统计数据
    assetStats: {
      netAssets: '0.00',
      totalAssets: '0.00',
      totalLiabilities: '0.00'
    },
    loadingAssets: false
  },

  // 切换activeTab（账单/资产）
  toggleActiveTab() {
    const newTab = this.data.activeTab === 'bill' ? 'asset' : 'bill'
    this.setData({
      activeTab: newTab,
      swpTabCurrent: newTab === 'bill' ? 0 : 1
    })
  },

  // 监听轮播图变化
  onSwiperChange(e) {
    const current = e.detail.current
    this.setData({
      swpTabCurrent: current,
      activeTab: current === 0 ? 'bill' : 'asset'
    })
  },

  getScroll(e) {
    const height = e.detail.scrollTop
    // 简化滚动处理逻辑
    if (height < 10) {
      this.setData({
        mySclace: 'none'
      })
    } else {
      this.setData({
        mySclace: 'mySclace'
      })
    }
  },
  sort1() {
    this.setData({
      sortType: 'amount'
    })
  },
  sort2() {
    this.setData({
      sortType: 'time'
    })
  },
  // 新增切换排序的函数
  toggleSort() {
    // 切换排序类型
    const newSortType = this.data.sortType === 'amount' ? 'time' : 'amount'
    console.log('切换排序类型为:', newSortType)
    this.setData({
      sortType: newSortType
    })
  },
  tabswpAdd() {
    this.setData({
      swpTabCurrent: this.data.swpTabCurrent + 1,
      activeTab: 'asset'
    })
  },
  tabswpAdd2() {
    this.setData({
      swpTabCurrent: this.data.swpTabCurrent - 1,
      activeTab: 'bill'
    })
  },

  getViewHeight(args) {
    // 这个函数可以根据需要保留或简化
  },
  /**
   * 生命周期函数--监听页面加载
   */
  onLoad(options) {
    console.log('onLoad首页开始进行加兹安')

    this.getViewHeight()
    console.log(app.globalData.BASE_URL, 'app.globalData.BASE_URL')
    this.setData({
      selectedColor: app.globalData.selectedColor || '#8dc63f'
    })

    // 加载资产列表数据
    this.loadAssetList()
  },

  // 加载资产列表数据
  loadAssetList() {
    this.setData({ loadingAssets: true })

    // 调用API获取用户账户数据
    getUserAccounts({ show_type: 'group', status: 'normal' })
      .then((res) => {
        console.log('获取资产列表成功:', res)
        if (res.code === 1 && res.data) {
          // 处理API返回的数据
          const formattedAssetList = this.formatAssetData(res.data)

          // 更新资产统计数据
          this.setData({
            assetList: formattedAssetList,
            assetStats: {
              netAssets: res.data.netassets || '0.00',
              totalAssets: res.data.capital || '0.00',
              totalLiabilities: res.data.liabilities || '0.00'
            },
            loadingAssets: false
          })
        } else {
          wx.showToast({
            title: res.msg,
            icon: 'none'
          })
          this.setData({ loadingAssets: false })
        }
      })
      .catch((err) => {
        console.error('获取资产列表失败:', err)
        wx.showToast({
          title: err.msg,
          icon: 'none'
        })
        this.setData({ loadingAssets: false })
      })
  },

  // 格式化API返回的资产数据为组件所需格式
  formatAssetData(apiData) {
    const formattedList = []

    if (!apiData || !apiData.data || !Array.isArray(apiData.data)) {
      return formattedList
    }

    // 遍历API返回的分组数据
    apiData.data.forEach((group) => {
      if (group.data && Array.isArray(group.data)) {
        // 遍历每个分组中的账户
        group.data.forEach((account) => {
          // 转换账户数据为组件所需格式
          const assetItem = {
            id: account.id,
            type: this.getAssetType(account.account_name, account.account_type),
            name: account.name,
            subType: account.account_name,
            bankName: account.name,
            cardNumber: account.cardnum || '',
            amount: account.money || '0.00',
            icon: account.image || '/assets/icons/bank/default.png',
            isNegative: account.account_type === 'liabilities',
            repaymentDay: account.repayment_date || '',
            repaymentDate: account.repayment_date ? `${account.repayment_date}号` : ''
          }

          // 如果是信用卡或其他负债类型，添加可用额度
          if (account.account_type === 'liabilities' && account.debt) {
            const debt = parseFloat(account.debt) || 0
            const money = parseFloat(account.money) || 0
            const availableLimit = (debt - Math.abs(money)).toFixed(2)
            assetItem.availableLimit = availableLimit
          }

          formattedList.push(assetItem)
        })
      }
    })

    return formattedList
  },

  // 根据账户名称和类型确定资产类型
  getAssetType(accountName, accountType) {
    if (accountName === '信用卡') {
      return 'credit'
    } else if (accountName === '借记卡' || accountName === '储蓄卡') {
      return 'debit'
    } else if (accountName === '借入' || accountName === '借出') {
      return 'person'
    } else if (accountName === '花呗' || accountName === '白条' || accountName === '支付宝' || accountName === '微信') {
      return 'online'
    } else {
      // 根据账户类型判断
      if (accountType === 'liabilities') {
        return 'credit'
      } else {
        return 'debit'
      }
    }
  },

  // 点击资产项
  onAssetItemClick(e) {
    const { id, type } = e.detail
    console.log('点击资产项:', id, type)

    // 示例：获取账户详情
    this.getAccountDetail(id)
  },

  // 获取账户详情示例
  getAccountDetail(accountId) {
    wx.showLoading({
      title: '加载中...'
    })

    getUserAccountDetail({ user_account_id: accountId })
      .then((res) => {
        console.log('获取账户详情成功:', res)
        if (res.code === 1 && res.data) {
          // 处理API返回的数据
          const accountData = res.data
          console.log('账户详情数据:', accountData)

          // 这里可以根据需要处理数据
          // 例如：显示账户详情弹窗、跳转到详情页等
        } else {
          wx.showToast({
            title: res.msg,
            icon: 'none'
          })
        }
        wx.hideLoading()
      })
      .catch((err) => {
        console.error('获取账户详情失败:', err)
        wx.showToast({
          title: err.msg,
          icon: 'none'
        })
        wx.hideLoading()
      })
  },

  // 添加资产
  onAddAsset() {
    wx.navigateTo({
      url: '/packageA/pages/settings/assetManagement/assetManagement'
    })
  },

  // 资产设置
  onAssetSetting() {
    console.log('资产设置')
    wx.navigateTo({
      url: '/packageA/pages/settings/assetManagement/assetManagement'
    })
  },

  // 处理资产隐藏事件
  onHideAsset(e) {
    const { id } = e.detail
    console.log('隐藏资产:', id)
    // 这里可以调用API隐藏资产
    wx.showToast({
      title: '已隐藏资产',
      icon: 'success'
    })
    // 重新加载资产列表
    this.loadAssetList()
  },

  // 处理资产编辑事件
  onEditAsset(e) {
    const { id } = e.detail
    console.log('编辑资产:', id)
    wx.navigateTo({
      url: `/packageA/pages/settings/editAccount/editAccount?id=${id}`
    })
  },

  // 处理资产删除事件
  onDeleteAsset(e) {
    const { id } = e.detail
    console.log('删除资产:', id)

    // 删除成功后重新加载资产列表
    this.loadAssetList()
  },

  showVip() {
    this.setData({
      vipDialog: !this.data.vipDialog
    })
  },
  navtoDetail() {
    wx.navigateTo({
      url: '/pages/budget/budget'
    })
  },
  // 切换账本的功能
  switchCashbook() {
    if (this.data.cashbooks.length <= 1) {
      // 如果只有一个账本，直接进入账本详情
      wx.navigateTo({
        url: '/pages/budget/budget'
      })
    } else {
      // 如果有多个账本，打开账本选择页面
      this.openBookSelector()
    }
  },
  // 打开账本选择器
  openBookSelector() {
    const that = this
    wx.getStorage({
      key: 'bookList',
      success: function (res) {
        const bookList = res.data || []
        // 从缓存中读取默认账本设置
        wx.getStorage({
          key: 'defaultBookSettings',
          success: function (settingsRes) {
            const bookSettings = settingsRes.data || {}
            that.setData({
              showBookSelector: true,
              bookList: bookList,
              selectedBookIds: bookSettings.bookIds || [],
              allBooksSelected: bookSettings.allBooksSelected || false,
              isCardMode: true,
              isMultiSelect: false
            })
          },
          fail: function () {
            that.setData({
              showBookSelector: true,
              bookList: bookList,
              selectedBookIds: [],
              allBooksSelected: false,
              isCardMode: true,
              isMultiSelect: false
            })
          }
        })
      },
      fail: function () {
        // 没有缓存的账本列表，从后端获取
        wx.request({
          url: 'YOUR_API_URL/getAccountBookList',
          method: 'GET',
          success: function (res) {
            if (res.statusCode === 200 && res.data.code === 1) {
              const bookList = res.data.data || []
              that.setData({
                showBookSelector: true,
                bookList: bookList,
                selectedBookIds: [],
                allBooksSelected: false,
                isCardMode: true,
                isMultiSelect: false
              })
              // 缓存账本列表
              wx.setStorage({
                key: 'bookList',
                data: bookList
              })
            }
          },
          fail: function () {
            wx.showToast({
              title: '获取账本列表失败',
              icon: 'none'
            })
          }
        })
      }
    })
  },
  // 关闭账本选择器
  closeBookSelector() {
    this.setData({
      showBookSelector: false
    })
  },
  // 选择账本
  selectBook(e) {
    const bookId = e.currentTarget.dataset.id
    const bookName = e.currentTarget.dataset.name

    this.setData({
      currentBookId: bookId,
      currentBookName: bookName,
      showBookSelector: false
    })

    // 刷新数据
    this.refreshData(bookId)
  },
  // 选择全部账本
  selectAllBooks() {
    this.setData({
      currentBookId: 0,
      currentBookName: '全部账本',
      showBookSelector: false,
      allBooksSelected: true
    })

    // 缓存默认账本设置
    wx.setStorage({
      key: 'defaultBookSettings',
      data: {
        bookIds: [],
        allBooksSelected: true
      }
    })

    // 刷新数据
    this.refreshData(0)
  },
  // 刷新数据
  refreshData(bookId) {
    this.setData({
      loading: true
    })

    // 这里应该根据选择的账本ID请求新的数据
    // 模拟请求延迟
    setTimeout(() => {
      this.setData({
        loading: false
      })
    }, 500)
  },
  setRemark() {
    wx.navigateTo({
      url: '/pages/inputMoney/inputMoney'
    })
  },
  ceshi(e) {
    console.log(e)
  },
  toggle() {
    this.setData({
      showAccount: !this.data.showAccount
    })
  },
  getMask(e) {
    console.log(e.detail)
    this.setData({
      showCard: e.detail
    })
  },
  onViewScroll(e) {
    console.log(e.detail.scrollTop)
  },
  getcateID(e) {
    console.log(e.detail)
  },
  onReady() {
    // 页面渲染完成
  },
  onShow() {
    // 页面显示
    // 每次显示页面时重新加载资产列表
    this.loadAssetList()
  },
  onHide() {
    // 页面隐藏
  },
  onUnload() {
    // 页面关闭
  },
  onPullDownRefresh() {
    // 页面下拉刷新
    this.loadAssetList()
    wx.stopPullDownRefresh()
  },
  onReachBottom() {
    // 页面上拉触底
  },
  onShareAppMessage() {
    // 用户点击右上角分享
  }
})
