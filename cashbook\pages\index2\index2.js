const app = getApp();
import { getUsers, createUser } from '../../api/home/<USER>';
Page({

  /**
   * 页面的初始数据
   */
  data: {
    selectedColor: '',
    list: [{
        id: 1,
        time: '4月19号',
        istoday: '今天',
        payAccount: '203',
        payList: [{
            id: 1,
            type: '住宿',
            mark: '出差',
            account: '35'
          },
          {
            id: 2,
            type: '餐饮',
            mark: '无',
            account: '35'
          }
        ]
      },
      {
        id: 1,
        time: '4月20号',
        istoday: '今天',
        payAccount: '203',
        payList: [{
            id: 1,
            type: '住宿',
            mark: '出差',
            account: '35'
          },
          {
            id: 2,
            type: '餐饮',
            mark: '无',
            account: '35'
          }
        ]

      },


    ],
    showCard:false,
    showAccount:true,
    vipDialog:false,
    swpCurrent:0, //首页list切换
    swpTabCurrent:0, //卡片切换
    swperHeight:null,
    mySclace:'none',//导航栏样式
    chagePosition:'none',
    navHeight:null,
    blockHeight:200,
  },
  getScroll(e){
    const height=e.detail.scrollTop;
    console.log('e',height);
    // return;
    // 回到顶部
    if(height<10){
      this.setData({
        mySclace:'none',
        navHeight:140+height,
        blockHeight:this.data.blockHeight+height
      })
    }
    // 开始滚动
    this.setData({
      mySclace:'mySclace',
      navHeight:140-height,
      blockHeight:this.data.blockHeight-height
    })
 
  },
  sort1(){
    this.setData({
      swpCurrent:1
    })
  },
  sort2(){
    this.setData({
      swpCurrent:0
    })
  },
  tabswpAdd(){
     this.setData({
      swpTabCurrent:this.data.swpTabCurrent+1
     })
  },
  tabswpAdd2(){
    this.setData({
     swpTabCurrent:this.data.swpTabCurrent-1
    })
 },

  getViewHeight (args) {
    if(args==null){
      args='#box1'
    }
    // 创建一个选择器查询对象
    const query = wx.createSelectorQuery();
    // 选择目标 view 标签
    query.select(args).boundingClientRect();
    // 执行查询操作
    query.exec((res) => {
      if (res[0]) {
        // 获取 view 的高度
        const height = res[0].height;
        this.setData({
          swperHeight: height
        });
        // console.log('view 的高度为：', height);
      }
    });
  },
  /**
   * 生命周期函数--监听页面加载
   */
  onLoad(options) {
     this.getViewHeight();
     console.log(app.globalData.BASE_URL,'app.globalData.BASE_URL');
     this.setData({
      selectedColor: app.globalData.selectedColor
      
    })

    getUsers().then(res=>{
      console.log('getuser',res);
    })
  },
  showVip(){
     this.setData({
      vipDialog:!this.data.vipDialog
     })
  },
  navtoDetail(){
    wx.navigateTo({
      url: '/pages/budget/budget',
    })
  },
  setRemark(){

    wx.navigateTo({
      url: '/pages/inputMoney/inputMoney',
    })
  },
 
  ceshi(e){
    console.log('ceshi',e);
  },
  toggle(){
    this.setData({
      showAccount:!this.data.showAccount
    })
  },
  getMask(e){
  
    this.setData({
      showCard:true
    })
      console.log('showCard',this.data.showCard);
 
  },
  onViewScroll(e) {
    console.log('页面发生了滚动', e.detail);
  },
  // 获取子组件传递的参数
  getcateID(e) {
    // console.log('子组件传递的数据',e.detail);
  },
  onReady() {

  },

  /**
   * 生命周期函数--监听页面显示
   */
  onShow() {

  },

  /**
   * 生命周期函数--监听页面隐藏
   */
  onHide() {

  },

  /**
   * 生命周期函数--监听页面卸载
   */
  onUnload() {

  },

  /**
   * 页面相关事件处理函数--监听用户下拉动作
   */
  onPullDownRefresh() {

  },

  /**
   * 页面上拉触底事件的处理函数
   */
  onReachBottom() {

  },

  /**
   * 用户点击右上角分享
   */
  onShareAppMessage() {

  }
})