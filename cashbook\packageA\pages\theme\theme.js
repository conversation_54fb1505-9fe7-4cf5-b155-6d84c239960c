// packageA/pages/theme/theme.js
import { getThemeList } from '../../../api/theme/index';

function lightenDarkenColor(hex, amt) {
  // 处理 #RRGGBB 格式
  let usePound = false;
  if (hex[0] === "#") {
    hex = hex.slice(1);
    usePound = true;
  }
  let num = parseInt(hex, 16);
  let r = (num >> 16) + amt;
  let g = ((num >> 8) & 0x00FF) + amt;
  let b = (num & 0x0000FF) + amt;
  r = Math.max(Math.min(255, r), 0);
  g = Math.max(Math.min(255, g), 0);
  b = Math.max(Math.min(255, b), 0);
  return (usePound ? "#" : "") + ((1 << 24) + (r << 16) + (g << 8) + b).toString(16).slice(1);
}

Page({
  /**
   * 页面的初始数据
   */
  data: {
    selectedColor: '', // 默认选中的颜色
    selectedColorType: 'preset', // preset: 预设颜色, custom: 自定义颜色
    colorList: [], // 从服务器获取的主题颜色列表
    loading: true, // 加载状态
    cardColors: {} // 卡片颜色
  },

  /**
   * 生命周期函数--监听页面加载
   */
  onLoad: function (options) {
    // 获取本地存储的主题颜色
    const themeColor = wx.getStorageSync('themeColor') || '';
    const themeColorType = wx.getStorageSync('themeColorType') || 'preset';

    // 获取本地存储的卡片颜色
    let cardColors = wx.getStorageSync('cardColors');

    this.setData({
      selectedColor: themeColor,
      selectedColorType: themeColorType,
      cardColors: cardColors || {}
    });

    // 获取服务器主题列表
    this.fetchServerThemes();
  },

  /**
   * 获取服务器主题列表
   */
  fetchServerThemes: function () {
    this.setData({ loading: true });

    getThemeList().then(res => {
      if (res.code === 1 && res.data) {
        // 处理服务器返回的主题数据
        const themeList = res.data.map(item => {
          return {
            id: item.id,
            name: item.name,
            value: `theme_${item.id}`,
            color: item.color,
            cardColors: {
              theme1: {
                backgroundColor: lightenDarkenColor(item.color, 40),
                // 设置字体颜色
                textColor: '#000000',
                // 设置summary-item-new的背景颜色
                summaryBgColor: 'rgba(242, 250, 254, 0.888)'
              },
              theme2: {
                backgroundColor: lightenDarkenColor(item.color, -40),
                // 设置字体颜色
                textColor: '#ffffff',
                // 设置summary-item-new的背景颜色
                summaryBgColor: 'rgba(24, 50, 65, 0.8)'
              }
            }
          };
        });

        this.setData({
          colorList: themeList,
          loading: false
        });

        // 如果已有选中的主题，确认它在新列表中，否则选择第一个
        // if (this.data.selectedColor && !themeList.some(item => item.value === this.data.selectedColor)) {
        //   if (themeList.length > 0) {
        //     this.selectColor({
        //       currentTarget: {
        //         dataset: {
        //           color: themeList[0].value
        //         }
        //       }
        //     });
        //   }
        // } else if (!this.data.selectedColor && themeList.length > 0) {
        //   // 如果没有选中的主题，选择第一个
        //   this.selectColor({
        //     currentTarget: {
        //       dataset: {
        //         color: themeList[0].value
        //       }
        //     }
        //   });
        // }
      }
    }).catch(err => {
      console.error('获取主题列表失败', err);
    });
  },

  /**
   * 返回上一页
   */
  goBack: function () {
    wx.navigateBack({
      delta: 1
    });
  },

  /**
   * 选择颜色
   */
  selectColor: function (e) {
    const color = e.currentTarget.dataset.color;
    const colorObj = this.data.colorList.find(item => item.value === color);

    if (colorObj) {
      this.setData({
        selectedColor: color,
        selectedColorType: 'preset',
        cardColors: colorObj.cardColors
      });

      // 保存主题颜色到本地存储
      // wx.setStorageSync('themeColor', color);
      // wx.setStorageSync('themeColorType', 'preset');
      // wx.setStorageSync('themeColorValue', colorObj.color);
      wx.setStorageSync('cardColors', colorObj.cardColors);

      // 设置全局主题颜色
      this.setThemeColor(colorObj.color);
    }

  },

  /**
   * 显示颜色选择器
   */
  showColorPicker: function () {
    // 这里可以实现自定义颜色选择器
    // 由于小程序没有内置的颜色选择器，可以使用第三方组件或自定义实现
    // 这里简化处理，直接设置为一个固定的自定义颜色
    const customColor = '#8DC2B5';
    const customCardColors = {
      theme1: { backgroundColor: lightenDarkenColor(customColor, 40), textColor: lightenDarkenColor(customColor, 100) },
      theme2: { backgroundColor: lightenDarkenColor(customColor, -40), textColor: lightenDarkenColor(customColor, -100) }
    };

    this.setData({
      selectedColorType: 'custom',
      cardColors: customCardColors
    });

    // 保存主题颜色到本地存储
    // wx.setStorageSync('themeColorType', 'custom');
    // wx.setStorageSync('themeColorValue', customColor);
    wx.setStorageSync('cardColors', customCardColors);

    // 设置全局主题颜色
    this.setThemeColor(customColor);
  },

  /**
   * 设置全局主题颜色
   */
  setThemeColor: function (color) {
    console.log(111);

    // 这里可以设置全局的主题颜色
    // 在实际应用中，可能需要通过全局变量或事件通知其他页面更新主题
    wx.setStorage({
      key: 'selectedColor',
      data: color,
    })
    getApp().globalData.selectedColor = color;
    getApp().globalData.cardColors = this.data.cardColors;

    // 提示用户主题已更改
    wx.showToast({
      title: '主题颜色已更新',
      icon: 'success',
      duration: 1500
    });
    console.log('主题颜色已更新');

  }
});