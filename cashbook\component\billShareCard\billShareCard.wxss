/* billShareCard.wxss */

/* 弹窗样式 */
.van-popup {
  overflow: hidden !important;
  border-radius: 35rpx !important;
}

.share-container {
  padding: 0;
  border-radius: 24rpx;
  display: flex;
  flex-direction: column;
  height: 85vh;
  /* 固定高度 */
  position: relative;
  overflow: hidden;
  /* 防止内容溢出 */
}

.share-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 30rpx;
  width: 100%;
  box-sizing: border-box;
}

.fixed-header {
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  z-index: 101;
  /* 提高层级 */
  background-color: #fff;
  border-bottom: 1rpx solid #eee;
  box-shadow: 0 2rpx 5rpx rgba(0, 0, 0, 0.05);
  border-radius: 35rpx 35rpx 0 0;
  /* 与弹窗一致的圆角 */
}

.share-title {
  font-size: 32rpx;
  font-weight: bold;
  text-align: center;
  flex: 1;
}

.close-btn {
  padding: 10rpx;
  background-color: #e1e1e1;
  border-radius: 50%;
  display: flex;
  align-items: center;
  justify-content: center;
  width: 40rpx;
  height: 40rpx;
  position: absolute;
}

.btn-right {
  right: 30rpx;
}

.btn-left {
  left: 30rpx;
}

/* 分享内容样式 */
.share-content {
  position: absolute;
  top: 80rpx;
  /* 顶部标题栏的高度 */
  bottom: 130rpx;
  /* 底部按钮的高度加上内边距 */
  left: 0;
  right: 0;
  z-index: 99;
  /* 确保在头部和底部之下 */
  height: 300px;
}

.bill-card {
  background-color: #ffb6c1;
  border-radius: 20rpx;
  overflow: visible;
  /* 允许内容溢出 */
  box-shadow: 0 2rpx 10rpx rgba(0, 0, 0, 0.1);
  margin-bottom: 20rpx;
  transition: background-color 0.3s ease;
  flex: 1;
}

/* 卡片头部 */
.bill-header {
  padding: 30rpx;
  display: flex;
  justify-content: space-between;
  align-items: center;
}

.bill-title {
  display: flex;
  flex-direction: column;
}

.app-name {
  font-size: 36rpx;
  font-weight: bold;
  color: #06866c;
  margin-bottom: 8rpx;
}

.app-desc {
  font-size: 24rpx;
  color: #06866c;
  opacity: 0.8;
}

.frog-icon {
  width: 80rpx;
  height: 80rpx;
}

.frog-icon image {
  width: 100%;
  height: 100%;
}

/* 账单总结 */
.bill-summary {
  padding: 20rpx 30rpx;
}

.summary-item {
  background-color: rgba(255, 255, 255, 0.5);
  padding: 20rpx;
  border-radius: 16rpx;
  margin-bottom: 20rpx;
  display: flex;
  align-items: center;
  transition: background-color 0.3s ease;
}

.summary-text {
  font-size: 28rpx;
  color: #333;
  margin-right: 10rpx;
  transition: background-color 0.3s ease;
}

.summary-amount {
  font-size: 32rpx;
  font-weight: bold;
  color: #333;
  transition: color 0.3s ease;
}

.summary-currency {
  font-size: 28rpx;
  color: #333;
  margin-left: 4rpx;
  transition: color 0.3s ease;
}

/* 日期分割线 */
.date-divider {
  display: flex;
  align-items: center;
  padding: 20rpx 30rpx;
}

.divider-line {
  height: 1rpx;
  background-color: rgba(0, 0, 0, 0.1);
  flex: 1;
}

.divider-date {
  padding: 0 20rpx;
  font-size: 24rpx;
  color: #666;
}

/* 账单明细 */
.bill-details-wrapper {
  padding: 0 30rpx 30rpx;
  overflow-y: auto;
  /* 内容过多时可滚动 */
  max-height: 600rpx;
  /* 设置一个更合理的最大高度 */
}

.bill-item {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 20rpx 0;
}

.item-left {
  display: flex;
  align-items: center;
}

.item-icon {
  width: 60rpx;
  height: 60rpx;
  background-color: #f5f5f5;
  border-radius: 50%;
  margin-right: 15rpx;
  display: flex;
  justify-content: center;
  align-items: center;
}

.item-icon image {
  width: 70%;
  height: 70%;
}

.item-category {
  font-size: 28rpx;
  color: #333;
}

.item-subcategory {
  margin-left: 10rpx;
  font-size: 24rpx;
  color: #666;
}

.item-right {
  display: flex;
  flex-direction: column;
  align-items: flex-end;
}

.item-amount {
  font-size: 30rpx;
  font-weight: bold;
  color: #333;
}

.item-tag {
  font-size: 22rpx;
  color: #666;
  background-color: rgba(255, 255, 255, 0.5);
  padding: 2rpx 10rpx;
  border-radius: 10rpx;
  margin-top: 4rpx;
}

/* 品牌底部 */
.brand-footer {
  background-color: #ffffff;
  padding: 20rpx;
  display: flex;
  align-items: center;
  box-shadow: 0 2rpx 8rpx rgba(0, 0, 0, 0.05);
}

/* 青蛙样式 */
.frog-brand {
  display: flex;
  align-items: center;
  width: 100%;
}

.brand-logo {
  width: 60rpx;
  height: 60rpx;
  margin-right: 15rpx;
}

.brand-logo image {
  width: 100%;
  height: 100%;
}

.brand-info {
  flex: 1;
}

.brand-name {
  font-size: 28rpx;
  font-weight: bold;
  color: #06866c;
}

.brand-slogan {
  font-size: 22rpx;
  color: #999;
  margin-top: 4rpx;
}

/* 个人头像样式 */
.user-brand {
  display: flex;
  width: 100%;
}

.user-info {
  display: flex;
  align-items: center;
  width: 100%;
}

.user-avatar {
  width: 80rpx;
  height: 80rpx;
  border-radius: 50%;
  overflow: hidden;
  margin-right: 20rpx;
  background-color: #f5f5f5;
}

.avatar-image {
  width: 100%;
  height: 100%;
}

.user-details {
  flex: 1;
}

.user-name-wrapper {
  display: flex;
  align-items: center;
  margin-bottom: 4rpx;
}

.user-name {
  font-size: 32rpx;
  font-weight: bold;
  color: #333;
  margin-right: 10rpx;
}

.vip-badge {
  background-color: #ffb0bc;
  border-radius: 15rpx;
  padding: 2rpx 10rpx;
  font-size: 20rpx;
  color: #FFFFFF;
}

.user-record {
  font-size: 24rpx;
  color: #999;
}

/* 底部操作按钮 */
.action-buttons {
  display: flex;
  padding: 20rpx 30rpx;
  justify-content: center;
  gap: 15rpx;
  width: 100%;
  box-sizing: border-box;
}

.fixed-footer {
  position: absolute;
  bottom: 0;
  left: 0;
  right: 0;
  z-index: 101;
  /* 提高层级 */
  background-color: #fff;
  box-shadow: 0 -2rpx 10rpx rgba(0, 0, 0, 0.05);
  padding-bottom: env(safe-area-inset-bottom);
  border-radius: 0 0 35rpx 35rpx;
  /* 与弹窗一致的圆角 */
}

.action-btn {
  height: 80rpx;
  border-radius: 40rpx;
  font-size: 28rpx;
  display: flex;
  align-items: center;
  justify-content: center;
  padding: 0 30rpx;
  flex: 0 0 auto;
  background-color: #ffb0bc;
  color: #fff;
  border: none;
  box-shadow: 0 2rpx 8rpx rgba(255, 176, 188, 0.3);
}

/* 样式选择弹窗 */
.format-popup {
  padding: 30rpx 0;
  background-color: #fff;
}

.format-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 0 45rpx 20rpx;
  position: relative;
}

.format-title {
  font-size: 32rpx;
  font-weight: bold;
  text-align: center;
  flex: 1;
}

.format-options {
  padding: 0 20rpx;
}

.format-option {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 30rpx 20rpx;
  border-bottom: 1px solid #f5f5f5;
}

.format-option:last-child {
  border-bottom: none;
}

.option-text {
  font-size: 30rpx;
  color: #333;
}