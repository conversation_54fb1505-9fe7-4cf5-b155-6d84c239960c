/* 资产管理页面样式 */
.asset-management-container {
  display: flex;
  flex-direction: column;
  height: 100vh; /* 保持100vh高度，但内部元素使用flex布局 */
  background-color: #f5f5f5;
  box-sizing: border-box;

  /* 顶部状态栏样式 */
  .status-bar {
    background-color: #ffffff;
    width: 100%;
  }

  /* 顶部导航栏样式 */
  .navigation-bar {
    display: flex;
    flex-direction: row;
    align-items: center;
    padding: 0 16px;
    background-color: #ffffff;
    position: relative;
    height: 100rpx;
    width: 100%;

    .back-button {
      width: 60rpx;
      height: 60rpx;
      display: flex;
      align-items: center;
      justify-content: center;
      margin-right: 10rpx;
      z-index: 2;
    }

    /* 标签栏样式 */
    .tab-bar {
      display: flex;
      flex: 1;
      align-items: center;
      // justify-content: center;
      height: 88rpx;
      position: absolute;
      left: 80rpx;
      right: 20rpx;

      .tab {
        padding: 0 30rpx;
        height: 64rpx;
        display: flex;
        align-items: center;
        justify-content: center;
        font-size: 28rpx;
        color: #333333;
        border-radius: 32rpx;
        margin: 0 6rpx;
        transition: all 0.3s ease;

        &.active {
          color: #ffffff;
          background-color: #303744;
          font-weight: 500;
        }

        text {
          line-height: 1;
        }
      }
    }
  }

  /* 资产列表样式 */
  .asset-list {
    flex: 1; /* 占据剩余空间 */
    padding: 24rpx 36rpx; /* 增加左右两侧的内边距，与效果图一致 */
    overflow-y: auto; /* 允许垂直滚动 */
    -webkit-overflow-scrolling: touch; /* 增强iOS滚动体验 */
    box-sizing: border-box;

    /* 加载中状态 */
    .loading-container {
      display: flex;
      flex-direction: column;
      align-items: center;
      justify-content: center;
      padding: 100rpx 0;

      .loading-text {
        font-size: 28rpx;
        color: #999999;
        margin-top: 20rpx;
      }
    }

    /* 无数据状态 */
    .empty-container {
      display: flex;
      flex-direction: column;
      align-items: center;
      justify-content: center;
      padding: 100rpx 0;

      .empty-text {
        font-size: 28rpx;
        color: #999999;
      }
    }

    .asset-card {
      display: flex;
      justify-content: space-between;
      align-items: center;
      height: 160rpx;
      margin-bottom: 20rpx; /* 调整卡片间距 */
      background-color: #ffffff;
      border-radius: 20rpx; /* 调整圆角 */
      box-shadow: 0 2rpx 6rpx rgba(0, 0, 0, 0.03); /* 调整阴影，更轻微 */
      padding: 0 30rpx; /* 调整内边距 */
      position: relative;
      overflow: hidden;

      .asset-card-left {
        display: flex;
        align-items: center;
        z-index: 2;

        .asset-icon {
          width: 88rpx; /* 调整图标大小 */
          height: 88rpx; /* 调整图标大小 */
          border-radius: 50%;
          display: flex;
          align-items: center;
          justify-content: center;
          margin-right: 24rpx; /* 调整间距 */
          background-color: #f2f2f2;

          /* 资产类型图标样式 */
          &.capital {
            background-color: #51c332; /* 资产类型背景色 */
          }

          /* 负债类型图标样式 */
          &.liabilities {
            background-color: #ff9500; /* 负债类型背景色 */
          }

          image {
            width: 65%; /* 调整图标图片大小 */
            height: 65%; /* 调整图标图片大小 */
          }
        }

        .asset-info {
          .asset-name {
            font-size: 30rpx; /* 调整字体大小 */
            color: #333333;
            margin-bottom: 6rpx; /* 调整间距 */
            font-weight: 500;
          }

          .asset-type {
            font-size: 24rpx; /* 调整字体大小 */
            color: #999999;
          }
        }
      }

      .asset-card-right {
        position: relative;
        z-index: 1;

        .bg-icon {
          width: 100rpx; /* 调整右侧背景图标大小 */
          height: 100rpx; /* 调整右侧背景图标大小 */
          opacity: 0.15; /* 调整透明度 */
        }

        .selected-mark {
          position: absolute;
          right: 0;
          bottom: 0;
          width: 44rpx; /* 调整选中标记大小 */
          height: 44rpx; /* 调整选中标记大小 */
          background-color: #4cd964;
          border-radius: 50%;
          display: flex;
          align-items: center;
          justify-content: center;

          image {
            width: 26rpx; /* 调整选中标记图标大小 */
            height: 26rpx; /* 调整选中标记图标大小 */
          }
        }
      }
    }
  }
}
