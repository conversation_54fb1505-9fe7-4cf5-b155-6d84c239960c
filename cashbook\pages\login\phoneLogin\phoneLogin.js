// pages/login/phoneLogin/phoneLogin.js
const { phoneLogin } = require('../../../api/login/index');

Page({
  data: {
    account: '',
    password: '',
    showPassword: false,
    isAgree: true
  },

  onLoad: function(options) {
    // 页面加载时的逻辑
  },

  // 账号输入
  onAccountInput: function(e) {
    this.setData({
      account: e.detail.value
    });
  },

  // 密码输入
  onPasswordInput: function(e) {
    this.setData({
      password: e.detail.value
    });
  },

  // 切换密码显示/隐藏
  togglePasswordVisibility: function() {
    this.setData({
      showPassword: !this.data.showPassword
    });
  },

  // 登录
  login: function() {
    if (!this.data.isAgree) {
      wx.showToast({
        title: '请先同意隐私政策',
        icon: 'none'
      });
      return;
    }

    if (!this.data.account || !this.data.password) {
      wx.showToast({
        title: '请输入账号和密码',
        icon: 'none'
      });
      return;
    }
    
    wx.showLoading({
      title: '登录中...',
    });
    
    // 准备登录参数
    const loginData = {
      username: this.data.account,
      password: this.data.password
    };
    
    // 调用登录接口
    phoneLogin(loginData).then(response => {
      wx.hideLoading();
      
      if (response.code === 1) {
        // 登录成功，保存用户信息
        const userInfo = response.data.userinfo;
        wx.setStorageSync('token', userInfo.token);
        wx.setStorageSync('userInfo', userInfo);
        
        // 设置登录状态
        wx.setStorageSync('isLoggedIn', true);
        
        // 显示登录成功提示
        wx.showToast({
          title: '登录成功',
          icon: 'success',
          duration: 1500
        });
        
        // 返回上一页或首页
        setTimeout(() => {
          const pages = getCurrentPages();
          // if (pages.length > 1) {
          //   wx.navigateBack();
          // } else {
          //   wx.switchTab({
          //     url: '/pages/index2/index2',
          //   });
          // }
          wx.navigateTo({
            url: '/pages/index2/index2',
          });
        }, 1500);
      } else {
        // 登录失败
        wx.showToast({
          title: response.msg || '登录失败',
          icon: 'none'
        });
      }
    }).catch(error => {
      wx.hideLoading();
      wx.showToast({
        title: '登录失败，请稍后重试',
        icon: 'none'
      });
      console.error('登录失败:', error);
    });
  },

  // 前往注册页面
  toRegister: function() {
    wx.navigateTo({
      url: '/pages/login/register/register',
    });
  },

  // 前往找回密码页面
  toForgotPassword: function() {
    wx.navigateTo({
      url: '/pages/login/forgotPassword/forgotPassword',
    });
  },

  // 返回上一页
  goBack: function() {
    wx.navigateBack();
  },

  // 隐私政策勾选变化
  checkboxChange: function(e) {
    this.setData({
      isAgree: e.detail.value.length > 0
    });
  },

  // 显示隐私政策
  showPrivacyPolicy: function() {
    wx.navigateTo({
      url: '/pages/login/privacy/privacy',
    });
  }
})