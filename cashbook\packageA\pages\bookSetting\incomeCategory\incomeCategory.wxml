<!-- 收支分类页面 -->
<view class="category-container">
  <!-- 顶部导航栏 -->
  <view class="nav-bar" style="padding-top: {{statusBarHeight}}px; height: {{navBarHeight}}px;">
    <view class="back-icon" bindtap="goBack">
      <van-icon name="arrow-left" size="20px" color="#fff" />
    </view>
    <view class="tab-container">
      <view class="tab {{activeTab === 'expenses' ? 'active' : ''}}" bindtap="switchTab" data-type="expenses">
        支出分类
      </view>
      <view class="tab {{activeTab === 'income' ? 'active' : ''}}" bindtap="switchTab" data-type="income">
        收入分类
      </view>
    </view>
    <view class="book-selector" bindtap="switchBook">
      <van-icon name="browsing-history" size="18px" color="#fff" />
      <text>{{currentBook || '默认账本'}}</text>
    </view>
  </view>
  <!-- 内容区域 -->
  <view class="content-area" style="padding-top: calc({{statusBarHeight}}px + {{navBarHeight}}px); padding-bottom: 51px;">
    <!-- 分类列表 -->
    <view class="category-list">
      <block wx:if="{{categories.length > 0}}">
        <block wx:for="{{categories}}" wx:key="id">
          <view class="category-card">
            <!-- 分类项头部（可点击展开/折叠） -->
            <view class="category-item" catchtap="toggleCategory" data-id="{{item.id}}" data-type="{{item.type}}">
              <view class="item-left">
                <van-icon name="play" size="16px" color="#ccc" class="arrow-icon" style="transform: rotate({{item.isExpanded ? 90 : 0}}deg); transition: transform 0.3s ease; transform-origin: center;" />
                <view class="category-icon">
                  <!-- 根据图标类型显示不同内容 -->
                  <block wx:if="{{item.imageType === 'text'}}">
                    <view class="text-icon">{{item.iconContent}}</view>
                  </block>
                  <block wx:elif="{{item.imageType === 'emoji'}}">
                    <view class="emoji-icon">{{item.iconContent}}</view>
                  </block>
                  <block wx:else>
                    <image src="{{item.image}}" mode="aspectFit"></image>
                  </block>
                </view>
                <view class="category-name">{{item.name}}</view>
              </view>
              <view class="item-right" catchtap="showMoreOptions" data-id="{{item.id}}">
                <van-icon name="ellipsis" />
              </view>
            </view>
            <!-- 子分类内容（使用动画展开/收起） -->
            <view class="subcategory-container {{item.isExpanded ? 'expanded' : 'collapsed'}}">
              <view class="subcategory-grid">
                <block wx:for="{{item.child}}" wx:key="id" wx:for-item="subItem">
                  <view class="subcategory-item" catchtap="editSubCategory" data-id="{{subItem.id}}" data-parent-id="{{item.id}}">
                    <view class="subcategory-icon">
                      <!-- 根据图标类型显示不同内容 -->
                      <block wx:if="{{subItem.imageType === 'text'}}">
                        <view class="text-icon">{{subItem.iconContent}}</view>
                      </block>
                      <block wx:elif="{{subItem.imageType === 'emoji'}}">
                        <view class="emoji-icon">{{subItem.iconContent}}</view>
                      </block>
                      <block wx:else>
                        <image src="{{subItem.image}}" mode="aspectFit"></image>
                      </block>
                    </view>
                    <view class="subcategory-name">{{subItem.name}}</view>
                  </view>
                </block>
                <!-- 添加子分类按钮 -->
                <view class="subcategory-item add-item">
                  <view class="add-icon" catchtap="addSubCategory" data-parent-id="{{item.id}}">
                    <van-icon name="add-o" size="24px" color="#333" />
                  </view>
                  <view class="subcategory-name">添加子分类</view>
                </view>
              </view>
            </view>
          </view>
        </block>
      </block>
      <block wx:else>
        <view class="empty-tip">
          <image src="/static/images/empty-category.png" mode="aspectFit" class="empty-image"></image>
          <text class="empty-text">暂无分类，点击下方按钮添加</text>
        </view>
      </block>
    </view>
    <!-- 底部添加按钮 -->
    <view class="add-button" bindtap="addCategory">
      <text>添加分类</text>
    </view>
  </view>
  <!-- 更多选项弹出层 -->
  <custom-popup visible="{{showMorePopup}}" closeButtonPosition="left" title="{{isSubCategory ? '子分类弹出层' : '更多'}}" position="bottom" bind:close="closeMorePopup">
    <view class="more-options-list">
      <!-- 主分类选项 -->
      <block wx:if="{{!isSubCategory}}">
        <!-- 添加子分类 -->
        <view class="more-option-item" bindtap="backupSubCategory" data-parent-id="{{currentCategoryId}}">
          <view class="option-left">
            <view class="option-content">
              <view class="option-title">添加子分类</view>
            </view>
          </view>
          <view class="option-arrow">
            <van-icon name="arrow" size="16px" color="#dddddd" />
          </view>
        </view>
        <!-- 改为子分类 -->
        <view class="more-option-item" bindtap="setAsMainCategory">
          <view class="option-left">
            <view class="option-content">
              <view class="option-title">改为子分类</view>
              <view class="option-desc">将「{{currentCategoryName}}」变为其他住分类的子分类</view>
            </view>
          </view>
          <view class="option-arrow">
            <van-icon name="arrow" size="16px" color="#dddddd" />
          </view>
        </view>
        <!-- 编辑分类 -->
        <view class="more-option-item" bindtap="editCategoryName">
          <view class="option-left">
            <view class="option-content">
              <view class="option-title">编辑分类</view>
              <view class="option-desc">修改「{{currentCategoryName}}」分类</view>
            </view>
          </view>
          <view class="option-arrow">
            <van-icon name="arrow" size="16px" color="#dddddd" />
          </view>
        </view>
        <!-- 分类排序 -->
        <view class="more-option-item" bindtap="sortCategory">
          <view class="option-left">
            <view class="option-content">
              <view class="option-title">分类排序</view>
              <view class="option-desc">为主分类排序</view>
            </view>
          </view>
          <view class="option-arrow">
            <van-icon name="arrow" size="16px" color="#dddddd" />
          </view>
        </view>
        <!-- 子分类排序 -->
        <view class="more-option-item" bindtap="sortSubCategory">
          <view class="option-left">
            <view class="option-content">
              <view class="option-title">子分类排序</view>
              <view class="option-desc">为「{{currentCategoryName}}」下的子分类排序</view>
            </view>
          </view>
          <view class="option-arrow">
            <van-icon name="arrow" size="16px" color="#dddddd" />
          </view>
        </view>
        <!-- 账单迁移 -->
        <view class="more-option-item" bindtap="showMigrationPopup">
          <view class="option-left">
            <view class="option-content">
              <view class="option-title">账单迁移</view>
              <view class="option-desc">仅迁移「{{currentCategoryName}}」分类下的账单，不包含子分类账单</view>
            </view>
          </view>
          <view class="option-arrow">
            <van-icon name="arrow" size="16px" color="#dddddd" />
          </view>
        </view>
        <!-- 删除分类并迁移账单 -->
        <view class="more-option-item" bindtap="deleteCategoryFromPopup">
          <view class="option-left">
            <view class="option-content">
              <view class="option-title">删除分类并迁移账单</view>
              <view class="option-desc">迁移「{{currentCategoryName}}」分类下的账单，并删除分类包含的子分类</view>
            </view>
          </view>
          <view class="option-arrow">
            <van-icon name="arrow" size="16px" color="#dddddd" />
          </view>
        </view>
        <!-- 删除分类 -->
        <view class="more-option-item" bindtap="deleteCategoryFromPopup">
          <view class="option-left">
            <view class="option-content">
              <view class="option-title">删除分类</view>
              <view class="option-desc">无需校验是否存在账单，仅删除分类</view>
            </view>
          </view>
          <view class="option-arrow">
            <van-icon name="arrow" size="16px" color="#dddddd" />
          </view>
        </view>
        <!-- 删除分类 -->
        <view class="more-option-item" bindtap="deleteCategoryFromPopup">
          <view class="option-left">
            <view class="option-content">
              <view class="option-title">删除分类</view>
              <view class="option-desc">校验是否存在账单</view>
            </view>
          </view>
          <view class="option-arrow">
            <van-icon name="arrow" size="16px" color="#dddddd" />
          </view>
        </view>
      </block>
      <!-- 子分类选项 -->
      <block wx:else>
        <!-- 设为主分类 -->
        <view class="more-option-item" bindtap="setAsMainCategory">
          <view class="option-left">
            <view class="option-content">
              <view class="option-title">设为主分类</view>
              <view class="option-desc">将「{{currentCategoryName}}」设为主分类</view>
            </view>
          </view>
          <view class="option-arrow">
            <van-icon name="arrow" size="16px" color="#dddddd" />
          </view>
        </view>
        <!-- 编辑子分类 -->
        <view class="more-option-item" bindtap="editSubCategoryFromPopup" data-id="{{currentCategoryId}}" data-parent-id="{{parentCategoryId}}">
          <view class="option-left">
            <view class="option-content">
              <view class="option-title">编辑子分类</view>
              <view class="option-desc">修改「{{currentCategoryName}}」分类</view>
            </view>
          </view>
          <view class="option-arrow">
            <van-icon name="arrow" size="16px" color="#dddddd" />
          </view>
        </view>
        <!-- 子分类排序 -->
        <view class="more-option-item" bindtap="sortSubCategory">
          <view class="option-left">
            <view class="option-content">
              <view class="option-title">子分类排序</view>
              <view class="option-desc">为「{{currentCategoryName}}」下的子分类排序</view>
            </view>
          </view>
          <view class="option-arrow">
            <van-icon name="arrow" size="16px" color="#dddddd" />
          </view>
        </view>
        <!-- 删除子分类并迁移账单 -->
        <view class="more-option-item" bindtap="deleteCategoryFromPopup">
          <view class="option-left">
            <view class="option-content">
              <view class="option-title">删除子分类并迁移账单</view>
              <view class="option-desc">迁移「{{currentCategoryName}}」分类下的账单</view>
            </view>
          </view>
          <view class="option-arrow">
            <van-icon name="arrow" size="16px" color="#dddddd" />
          </view>
        </view>
        <!-- 删除子分类 -->
        <view class="more-option-item" bindtap="deleteCategoryFromPopup">
          <view class="option-left">
            <view class="option-content">
              <view class="option-title">删除子分类</view>
              <view class="option-desc">校验是否存在账单</view>
            </view>
          </view>
          <view class="option-arrow">
            <van-icon name="arrow" size="16px" color="#dddddd" />
          </view>
        </view>
      </block>
    </view>
  </custom-popup>

  <!-- 账单迁移弹窗 -->
  <custom-popup visible="{{showMigrationPopup}}" closeButtonPosition="left" title="账单迁移" position="bottom" bind:close="closeMigrationPopup">
    <view class="migration-container">
      <!-- 待迁移分类名称 -->
      <view class="migration-section">
        <view class="migration-label">待迁移分类名称</view>
        <view class="migration-category-display">
          <view class="migration-category-content">
            <view class="migration-category-name">{{migrationData.categoryName}}</view>
            <view class="migration-category-icon">
              <!-- 根据图标类型显示不同内容 -->
              <block wx:if="{{migrationData.imageType === 'text'}}">
                <view class="text-icon">{{migrationData.iconContent}}</view>
              </block>
              <block wx:elif="{{migrationData.imageType === 'emoji'}}">
                <view class="emoji-icon">{{migrationData.iconContent}}</view>
              </block>
              <block wx:else>
                <image src="{{migrationData.categoryIcon}}" mode="aspectFit"></image>
              </block>
            </view>
          </view>
        </view>
      </view>

      <!-- 迁移到新分类 -->
      <view class="migration-section">
        <view class="migration-label">迁移到新分类</view>
        <view class="migration-target-selector" bindtap="selectMigrationTarget">
          <view class="migration-target-content">
            <view class="migration-target-text">{{migrationData.targetCategoryName || '请选择分类'}}</view>
            <view class="migration-target-icon">
              <van-icon name="bars" size="20px" color="#ccc" />
            </view>
          </view>
        </view>
      </view>

      <!-- 提示文本 -->
      <view class="migration-tips">
        <text>主分类迁移，仅迁移存在主分类下的账单，子分类账单不会改变哦~</text>
      </view>

      <!-- 确认迁移按钮 -->
      <view class="migration-confirm-btn" bindtap="confirmMigration">
        <text>确认迁移</text>
      </view>
    </view>
  </custom-popup>
</view>