<!--pages/asset-change/asset-change.wxml-->
<view class="asset-change-container">
  <!-- 自定义导航栏 -->
  <view class="custom-navbar">
    <view class="navbar-content">
      <view class="nav-left" bindtap="goBack">
        <text class="iconfont icon-close">✕</text>
      </view>
      <view class="nav-right" bindtap="goToSettings">
        <text class="setting-text">设置</text>
      </view>
    </view>
  </view>

  <!-- 头部内容区域 -->
  <view class="header-content">
    <view class="nav-center">
      <view class="nav-icon">
        <text class="chart-icon">📊</text>
      </view>
      <view class="nav-title">
        <text class="title">资产变动</text>
        <text class="subtitle">查看资产的每天变动，了解自身财务状态。</text>
      </view>
    </view>
  </view>

  <!-- 资产组成 -->
  <view class="asset-composition">
    <view class="section-title">
      <view class="title-indicator"></view>
      <text class="title-text">资产组成</text>
    </view>

    <!-- 负债部分 -->
    <view class="debt-section">
      <text class="section-label">负债</text>
      <view class="debt-items">
        <view class="debt-item debt-main">
          <view class="debt-content">
            <text class="debt-name">信用卡等</text>
            <text class="debt-percentage">83.89%</text>
          </view>
          <text class="debt-amount">-5260.0</text>
        </view>
        <view class="debt-item debt-secondary">
          <view class="debt-content">
            <text class="debt-name">应付</text>
            <text class="debt-percentage">16.11%</text>
          </view>
          <text class="debt-amount">-1010.0</text>
        </view>
      </view>
    </view>

    <!-- 总资产部分 -->
    <view class="asset-section">
      <text class="section-label">总资产</text>
      <view class="asset-items">
        <view class="asset-item asset-main">
          <view class="asset-content">
            <text class="asset-name">流动资金</text>
            <text class="asset-percentage">58.33%</text>
          </view>
          <text class="asset-amount">700.0</text>
        </view>
        <view class="asset-item asset-secondary">
          <view class="asset-content">
            <text class="asset-name">应收</text>
            <text class="asset-percentage">41.67%</text>
          </view>
          <text class="asset-amount">500.0</text>
        </view>
      </view>
    </view>
  </view>

  <!-- 资产变动图表 -->
  <view class="asset-chart">
    <view class="chart-header">
      <view class="chart-title">
        <view class="title-indicator"></view>
        <text class="title-text">资产变动</text>
        <text class="chart-type">线图</text>
      </view>
      <view class="chart-controls">
        <text class="control-icon">💰</text>
        <text class="control-icon">⚙️</text>
        <text class="time-period">近1月</text>
      </view>
    </view>

    <view class="chart-date">
      <text class="date-text">2025年5月30日 金额 ¥ -4000.00</text>
    </view>

    <view class="chart-container">
      <canvas class="chart-canvas" canvas-id="assetChart" disable-scroll="true"></canvas>
      <view class="chart-placeholder">
        <view class="chart-line">
          <view class="chart-point" wx:for="{{chartPoints}}" wx:key="index" style="left: {{item.x}}%; top: {{item.y}}%;"></view>
        </view>
      </view>
    </view>

    <view class="chart-button">
      <text class="button-text">开启数据统计</text>
    </view>
  </view>

  <!-- 银行卡信息 -->
  <view class="bank-card">
    <view class="card-info">
      <image class="bank-logo" src="/images/cmb-logo.png" mode="aspectFit"></image>
      <view class="card-details">
        <text class="bank-name">招商银行</text>
        <text class="card-type">信用卡 4321</text>
        <text class="card-icon">💳</text>
      </view>
      <text class="card-badge">👑</text>
    </view>

    <view class="card-amount">
      <text class="amount-date">2025年5月30日</text>
      <text class="amount-value">金额 ¥ -4860.00</text>
    </view>

    <view class="card-chart">
      <view class="mini-chart">
        <view class="chart-line-mini"></view>
        <view class="chart-points-mini">
          <view class="point-mini" wx:for="{{miniChartPoints}}" wx:key="index" style="left: {{item.x}}%; top: {{item.y}}%;"></view>
        </view>
      </view>
    </view>
  </view>
</view>
