<!--packageA/pages/help/allCategories/allCategories.wxml-->
<view class="all-categories-container">
  <!-- 顶部导航 -->
  <!-- <view class="nav-bar">
    <view class="back-btn" bindtap="goBack">
      <image src="/static/icon/back.png" mode="aspectFit"></image>
    </view>
    <view class="page-title">全部分类</view>
  </view> -->

  <!-- 分类列表 -->
  <view class="categories-list" wx:if="{{!loading && categories.length > 0}}">
    <view class="category-item" 
          wx:for="{{categories}}" 
          wx:key="id" 
          bindtap="viewCategory" 
          data-id="{{item.id}}"
          data-name="{{item.name}}">
      <view class="category-icon" wx:if="{{item.icon}}">
        <image src="{{item.icon}}" mode="aspectFit"></image>
      </view>
      <view class="category-icon default-icon" wx:else>
        <image src="/static/icon/category_default.png" mode="aspectFit"></image>
      </view>
      <view class="category-info">
        <view class="category-name">{{item.name}}</view>
        <view class="category-desc" wx:if="{{item.description}}">{{item.description}}</view>
        <view class="category-count" wx:if="{{item.article_count !== undefined}}">
          {{item.article_count}} 篇文章
        </view>
      </view>
      <view class="category-arrow">
        <image src="/static/icon/arrow_right.png" mode="aspectFit"></image>
      </view>
    </view>
  </view>
  
  <!-- 加载中 -->
  <view class="loading-container" wx:if="{{loading}}">
    <view class="loading-icon"></view>
    <text>加载中...</text>
  </view>
  
  <!-- 空状态 -->
  <view class="empty-container" wx:if="{{!loading && categories.length === 0}}">
    <image src="/static/icon/empty.png" mode="aspectFit"></image>
    <text>暂无分类</text>
  </view>
</view>