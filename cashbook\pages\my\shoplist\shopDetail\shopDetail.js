// pages/my/shoplist/shopDetail/shopDetail.js
import { detaillist, listgoods } from '../../../../api/user/index'

Page({
  /**
   * 页面的初始数据
   */
  data: {
    managerDialog: false,
    id: '',
    listDetail: {},
    listgoodss: []
  },
  goodelist() {
    listgoods({
      list_id: this.data.id
    })
      .then((res) => {
        console.log(res, 'res-----------')
        if (res && res.code === 1) {
          this.setData({
            listgoodss: res.data
          })
        } else {
        }
      })
      .catch((err) => {})
  },
  detail() {
    detaillist({
      list_id: this.data.id
    })
      .then((res) => {
        wx.hideLoading()
        console.log(res, 'res-----------')
        if (res && res.code === 1) {
          // 更新本地显示
          this.setData({
            listDetail: res.data
          })
        } else {
        }
      })
      .catch((err) => {})
  },
  toAddShop() {
    wx.navigateTo({
      url: '/pages/my/shoplist/addShop/addShop'
    })
  },
  getText(e) {
    console.log('text', e)
    const text = e.detail
    if (text == '管理') {
      this.setData({
        managerDialog: true
      })
    }
  },
  closeDialog2() {
    this.setData({
      managerDialog: false
    })
  },
  toEdit() {
    wx.navigateTo({
      url: '/pages/my/shoplist/shopEdit/shopEdit'
    })
  },
  onLoad(options) {
    this.setData({
      id: options.id
    })
    this.detail()
    this.goodelist()
  },

  /**
   * 生命周期函数--监听页面初次渲染完成
   */
  onReady() {},

  /**
   * 生命周期函数--监听页面显示
   */
  onShow() {},

  /**
   * 生命周期函数--监听页面隐藏
   */
  onHide() {},

  /**
   * 生命周期函数--监听页面卸载
   */
  onUnload() {},

  /**
   * 页面相关事件处理函数--监听用户下拉动作
   */
  onPullDownRefresh() {},

  /**
   * 页面上拉触底事件的处理函数
   */
  onReachBottom() {},

  /**
   * 用户点击右上角分享
   */
  onShareAppMessage() {}
})
