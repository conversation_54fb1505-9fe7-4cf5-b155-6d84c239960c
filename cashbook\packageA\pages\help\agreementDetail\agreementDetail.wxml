<!--packageA/pages/help/agreementDetail/agreementDetail.wxml-->
<view class="agreement-detail-container">
  <!-- 加载中提示 -->
  <view class="loading-container" wx:if="{{loading}}">
    <view class="loading-spinner"></view>
    <text class="loading-text">加载中...</text>
  </view>
  
  <!-- 错误提示 -->
  <view class="error-container" wx:elif="{{error}}">
    <image class="error-icon" src="/static/icon/error.png" mode="aspectFit"></image>
    <text class="error-text">内容加载失败</text>
    <button class="retry-btn" bindtap="fetchAgreementDetail" data-title="{{agreement.title}}">重试</button>
  </view>
  
  <!-- 协议内容 -->
  <view class="agreement-content" wx:else>
    <!-- 协议标题 -->
    <view class="agreement-header">
      <text class="agreement-title">{{agreement.title}}</text>
      <view class="divider"></view>
    </view>
    
    <!-- 协议内容 - 处理两种情况 -->
    <!-- 1. 如果使用了WxParse库 -->
    <view class="rich-content" wx:if="{{article}}">
      <import src="../../../../wxParse/wxParse.wxml"/>
      <template is="wxParse" data="{{wxParseData:article.nodes}}"/>
    </view>
    
    <!-- 2. 如果没有使用WxParse库或解析失败，直接显示文本 -->
    <view class="text-content" wx:else>
      <!-- 如果内容是HTML，则尝试简单处理 -->
      <rich-text nodes="{{agreement.content}}"></rich-text>
      
      <!-- 如果rich-text不支持或内容为纯文本 -->
      <text wx:if="{{!agreement.content}}">暂无内容</text>
    </view>
  </view>
  
  <!-- 底部公司信息 -->
  <view class="footer">
    <text class="company-name">小猫记账 © {{currentYear}}</text>
  </view>
</view>