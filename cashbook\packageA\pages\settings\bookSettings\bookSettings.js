import {
  getAccountBookList,
  setDefaultBook
} from '../../../../api/book/index';

// 引入接口
const { setConfig } = require('../../../../api/user/setconfig');
const util = require('../../../../utils/index.js');

// 更新记账功能设置
function updateSettings(data) {
  setConfig(data).then(res => {
    if (res.code === 1) {
      wx.showToast({
        title: '设置成功',
        icon: 'success'
      });
    } else {
      wx.showToast({
        title: res.msg || '设置失败',
        icon: 'none'
      });
    }
  }).catch(err => {
    wx.showToast({
      title: '网络错误',
      icon: 'none'
    });
  });
}

Page({
  data: {
    bookList: [],
    showBookSelector: false,
    loading: false,
    selectedBookId: null,
    showBookingPagePopup: false,
    showAssetReminder: true,
    rememberCategoryAsset: true,
    allowFutureRecords: false,
    checkBalanceShortage: false,
    keyboardVibration: true,
    incomeAnimation: true,
    showDatePickerPopup: false,
    selectedDay: 1,
    currentMonthName: '',
    nextMonthName: '',
    daysInLastRow: [],
    billCycleExample: '', // 新增：账单周期示例文本
    showAssetSelector: false,
    assetAccounts: [{
      id: 1,
      name: '现金',
      icon: '/static/icon/cash.png',
      balance: '1000.00',
      selected: false
    },
    {
      id: 2,
      name: '支付宝',
      icon: '/static/icon/alipay.png',
      balance: '2500.00',
      selected: false
    },
    {
      id: 3,
      name: '微信',
      icon: '/static/icon/wechat.png',
      balance: '1800.00',
      selected: false
    },
    {
      id: 4,
      name: '银行卡',
      icon: '/static/icon/bank.png',
      balance: '5000.00',
      selected: true
    }
    ],
    selectedAssetAccount: '默认账户',
    showCategoryStylePopup: false,
    categoryStyle: '列表翻页',
    showRowsSelector: false,
    selectedRows: 4, // 默认选择4行
    showDateTypePopup: false,
    dateType: '年月日',
    showFeaturePopup: false,
    features: {
      assetAccount: true,
      multiBook: true,
      reimbursement: true,
      tags: false,
      discount: true,
      notIncluded: true,
      billTemplate: false,
      billImage: true,
      viewMode: 'list', // 默认列表模式
      isMultiSelect: false,  // 是否为多选模式
    }
  },

  onLoad: function () {
    // 页面加载时获取账本列表
    this.fetchBookList();
    // 加载记账功能设置
    wx.getStorage({
      key: 'bookingFeatures',
      success: (res) => {
        this.setData({
          features: res.data
        });
      }
    });
  },
  closeDatePicker() {
    this.setData({
      showDatePickerPopup: false
    })
  },
  /**
 * 切换账本选择模式（单选/多选）
 */
toggleSelectMode: function() {
  const isMultiSelect = !this.data.isMultiSelect;
  
  // 如果从多选切换到单选，需要保留当前选中的账本
  if (!isMultiSelect && this.data.bookList) {
    // 找到当前选中的账本
    const selectedBooks = this.data.bookList.filter(book => book.selected);
    
    // 如果有多个选中的账本，只保留第一个
    if (selectedBooks.length > 1) {
      const updatedBookList = this.data.bookList.map(book => {
        return {
          ...book,
          selected: book.id === selectedBooks[0].id
        };
      });
      
      this.setData({
        bookList: updatedBookList
      });
    }
  }
  
  this.setData({
    isMultiSelect: isMultiSelect
  });
},

/**
 * 确认账本选择（多选模式下）
 */
confirmBookSelection: function() {
  if (!this.data.isMultiSelect) {
    return;
  }
  
  // 获取所有选中的账本
  const selectedBooks = this.data.bookList.filter(book => book.selected);
  
  if (selectedBooks.length === 0) {
    wx.showToast({
      title: '请至少选择一个账本',
      icon: 'none'
    });
    return;
  }
  
  // 处理选中的账本
  const selectedBookIds = selectedBooks.map(book => book.id);
  const selectedBookNames = selectedBooks.map(book => book.name);
  
  // 这里可以根据实际需求处理选中的账本数据
  // 例如：保存到本地存储、更新全局状态等
  
  // 关闭账本选择器
  this.closeBookSelector();
  
  // 触发选择完成事件，传递选中的账本信息
  this.triggerEvent('bookSelectionComplete', {
    mode: 'multi',
    books: selectedBooks,
    bookIds: selectedBookIds,
    bookNames: selectedBookNames
  });
  
  // 显示提示信息
  wx.showToast({
    title: `已选择${selectedBooks.length}个账本`,
    icon: 'success'
  });
},
  // 添加切换视图模式的方法
switchViewMode: function(e) {
  const mode = e.currentTarget.dataset.mode;
  this.setData({
    viewMode: mode
  });
},

// 重新加载账本列表
reloadBookList: function() {
  this.fetchBookList();
  wx.showToast({
    title: '数据已刷新',
    icon: 'success'
  });
},
  confirmDateSelection(){
    updateSettings({
      start_days: this.data.selectedDay
    });
    this.setData({
      showDatePickerPopup: false
    })
  },
  // 打开记账功能弹窗
  goToBookingFeatures: function () {
    this.setData({
      showFeaturePopup: true
    });
  },

  // 关闭记账功能弹窗
  closeFeaturePopup: function () {
    this.setData({
      showFeaturePopup: false
    });
  },

  // 处理弹窗状态变化
  onFeaturePopupChange: function (e) {
    if (!e.detail.visible) {
      this.setData({
        showFeaturePopup: false
      });
    }
  },

  // 切换功能开关
  toggleFeature: function (e) {
    const feature = e.currentTarget.dataset.feature;
    const value = e.detail.value;

    // 更新对应功能的状态
    const features = this.data.features;
    features[feature] = value;

    this.setData({
      features: features
    });

    // 保存设置
    this.saveFeatureSettings();
  },

  // 保存功能设置
  saveFeatureSettings: function () {
    wx.setStorage({
      key: 'bookingFeatures',
      data: this.data.features
    });
  },
  // 打开日期类型选择弹窗
  selectDateType: function () {
    this.setData({
      showDateTypePopup: true
    });
  },

  // 关闭日期类型选择弹窗
  closeDateTypePopup: function () {
    this.setData({
      showDateTypePopup: false
    });
  },

  // 处理弹窗状态变化
  onDateTypePopupChange: function (e) {
    if (!e.detail.visible) {
      this.setData({
        showDateTypePopup: false
      });
    }
  },

  // 选择日期类型
  selectDate: function (e) {
    const type = e.currentTarget.dataset.type;
    let typeName = '年月日';

    if (type === 'datetime') {
      typeName = '年月日时分';
    }

    this.setData({
      dateType: typeName,
      showDateTypePopup: false
    });

    // 保存设置
    this.saveBookingPageSettings();
  },
  // 打开分类显示行数选择弹窗
  selectCategoryRows: function () {
    this.setData({
      showRowsSelector: true
    });
  },

  // 关闭分类显示行数选择弹窗
  closeRowsSelector: function () {
    this.setData({
      showRowsSelector: false
    });
  },

  // 处理弹窗状态变化
  onRowsSelectorChange: function (e) {
    if (!e.detail.visible) {
      this.setData({
        showRowsSelector: false
      });
    }
  },

  // 选择行数
  selectRows: function (e) {
    const rows = parseInt(e.currentTarget.dataset.rows);
    this.setData({
      selectedRows: rows
    });
  },
  

  // 确认选择行数
  confirmRowsSelection: function () {
    // 保存设置
    const settings = {
      // ... existing code ...
      categoryRows: this.data.selectedRows,
      dateType: this.data.dateType
    };

    wx.setStorage({
      key: 'bookingPageSettings',
      data: settings
    });

    this.closeRowsSelector();
  },
  // 修改selectMonthStartDay方法
  selectMonthStartDay() {
    // 获取当前月份信息
    const now = new Date();
    const currentMonth = now.getMonth();
    const currentYear = now.getFullYear();

    // 计算当前月份的天数
    const daysInMonth = new Date(currentYear, currentMonth + 1, 0).getDate();

    // 计算最后一行需要显示的天数（29-31日）
    const daysInLastRow = [];
    for (let i = 29; i <= daysInMonth; i++) {
      daysInLastRow.push(i);
    }

    // 获取当前月份和下一个月份的名称
    const months = ['1月', '2月', '3月', '4月', '5月', '6月', '7月', '8月', '9月', '10月', '11月', '12月'];
    const currentMonthName = months[currentMonth];

    // 设置当前选中的日期为已保存的设置
    const selectedDay = parseInt(this.data.monthStartDay) || 1;

    // 计算下一个月
    const nextMonth = (currentMonth + 1) % 12;
    const nextYear = nextMonth === 0 ? currentYear + 1 : currentYear;

    // 获取下一个月的天数
    const daysInNextMonth = new Date(nextYear, nextMonth + 1, 0).getDate();

    // 处理特殊情况：如果选择的日期超过下一个月的天数
    let nextMonthDay = selectedDay;
    let nextMonthName = months[nextMonth];

    // 如果选择的日期超过下一个月的天数，显示下下个月的1日
    if (selectedDay > daysInNextMonth) {
      nextMonthDay = 1;
      // 获取下下个月的名称
      nextMonthName = months[(nextMonth + 1) % 12];
    }

    // 生成账单周期示例文本
    const billCycleExample = `${currentMonthName}${selectedDay}日 - ${nextMonthName}${nextMonthDay}日`;

    this.setData({
      showDatePickerPopup: true,
      daysInLastRow: daysInLastRow,
      currentMonthName: currentMonthName,
      nextMonthName: nextMonthName,
      selectedDay: selectedDay,
      billCycleExample: billCycleExample
    });
  },
  // 修改selectDay方法
  selectDay(e) {
    const day = e.currentTarget.dataset.day;

    // 获取当前月份信息
    const now = new Date();
    const currentMonth = now.getMonth();
    const currentYear = now.getFullYear();

    // 计算下一个月
    const nextMonth = (currentMonth + 1) % 12;
    const nextYear = nextMonth === 0 ? currentYear + 1 : currentYear;

    // 获取下一个月的天数
    const daysInNextMonth = new Date(nextYear, nextMonth + 1, 0).getDate();

    // 处理特殊情况：如果选择的日期超过下一个月的天数
    let nextMonthDay = day;
    let nextMonthName = this.data.nextMonthName;
    let thirdMonthName = '';

    // 如果选择的日期超过下一个月的天数，显示下下个月的1日
    if (day > daysInNextMonth) {
      nextMonthDay = 1;
      // 获取下下个月的名称
      const months = ['1月', '2月', '3月', '4月', '5月', '6月', '7月', '8月', '9月', '10月', '11月', '12月'];
      thirdMonthName = months[(nextMonth + 1) % 12];
      nextMonthName = thirdMonthName;
    }

    // 更新账单周期示例文本
    const billCycleExample = `${this.data.currentMonthName}${day}日 - ${nextMonthName}${nextMonthDay}日`;

    this.setData({
      selectedDay: day,
      billCycleExample: billCycleExample
    });
  },

  // 获取账本列表
  fetchBookList: function () {
    this.setData({
      loading: true
    });

    getAccountBookList().then(res => {
      if (res.code === 1) {
        res.data.forEach(item => {
          item.image = util.getImageUrl(item.image);
        });

        // 处理账本列表数据
        const bookList = res.data || [];

        // 找到默认账本
        const defaultBook = bookList.find(book => book.is_default === 1);

        this.setData({
          bookList: bookList,
          selectedBookId: defaultBook ? defaultBook.id : null
        });
      } else {
        wx.showToast({
          title: res.msg || '获取账本列表失败',
          icon: 'none'
        });
      }
    }).catch(err => {
      wx.showToast({
        title: '网络异常，请稍后重试',
        icon: 'none'
      });
      console.error('获取账本列表失败:', err);
    }).finally(() => {
      this.setData({
        loading: false
      });
    });
  },

  // 选择默认查询账本
  selectDefaultBook: function () {
    console.log(111);

    // 设置默认选中的账本
    const bookList = this.data.bookList.map(book => {
      return {
        ...book,
        selected: book.name === this.data.defaultBook
      };
    });

    this.setData({
      showBookSelector: true
    });
  },

  // 关闭账本选择器
  closeBookSelector: function () {
    this.setData({
      showBookSelector: false
    });
  },

  // 选择账本
  selectBook: function (e) {
    const bookId = e.currentTarget.dataset.id;
    console.log(bookId);

    // 如果已经是默认账本，则不需要设置
    const selectedBook = this.data.bookList.find(book => book.id === bookId);
    if (selectedBook && selectedBook.is_default === 1) {
      this.closeBookSelector();
      return;
    }

    this.setData({
      loading: true
    });
    updateSettings({select_book_id:bookId})

  },

  // 添加新账本
  addNewBook: function () {
    wx.navigateTo({
      url: '/packageA/pages/myBooks/myBooks'
    })
  },
  // 打开记账页面设置弹窗
  goToBookingPageSettings: function () {
    this.setData({
      showBookingPagePopup: true
    });
  },

  // 关闭记账页面设置弹窗
  closeBookingPagePopup: function () {
    this.setData({
      showBookingPagePopup: false
    });
  },

  // 弹窗状态变化
  onBookingPagePopupChange: function (e) {
    if (!e.detail.visible) {
      this.setData({
        showBookingPagePopup: false
      });
    }
  },

  // 开关切换方法
  toggleShowAssetReminder: function (e) {
    this.setData({
      showAssetReminder: e.detail.value
    });
    this.saveBookingPageSettings();
  },

  toggleRememberCategoryAsset: function (e) {
    this.setData({
      rememberCategoryAsset: e.detail.value
    });
    this.saveBookingPageSettings();
  },

  toggleAllowFutureRecords: function (e) {
    this.setData({
      allowFutureRecords: e.detail.value
    });
    this.saveBookingPageSettings();
  },

  toggleCheckBalanceShortage: function (e) {
    this.setData({
      checkBalanceShortage: e.detail.value
    });
    this.saveBookingPageSettings();
  },

  toggleKeyboardVibration: function (e) {
    this.setData({
      keyboardVibration: e.detail.value
    });
    this.saveBookingPageSettings();
  },

  toggleIncomeAnimation: function (e) {
    this.setData({
      incomeAnimation: e.detail.value
    });
    this.saveBookingPageSettings();
  },
  // 打开资产账户选择器
  selectDefaultAsset: function () {
    this.setData({
      showAssetSelector: true
    });
  },

  // 关闭资产账户选择器
  closeAssetSelector: function () {
    this.setData({
      showAssetSelector: false
    });
  },

  // 处理弹窗状态变化
  onAssetPopupChange: function (e) {
    if (!e.detail.visible) {
      this.setData({
        showAssetSelector: false
      });
    }
  },

  // 选择"不选择具体账户"选项
  selectNoSpecificAccount: function () {
    // 重置所有账户的选中状态
    const accounts = this.data.assetAccounts.map(account => {
      return {
        ...account,
        selected: false
      };
    });

    this.setData({
      assetAccounts: accounts,
      selectedAssetAccount: '不选择具体账户'
    });
  },

  // 选择特定资产账户
  selectAssetAccount: function (e) {
    const index = e.currentTarget.dataset.index;
    const accounts = this.data.assetAccounts.map((account, idx) => {
      return {
        ...account,
        selected: idx === index
      };
    });

    this.setData({
      assetAccounts: accounts,
      selectedAssetAccount: accounts[index].name
    });
  },

  // 确认选择
  confirmAssetSelection: function () {
    // 这里可以添加保存选择的逻辑
    this.closeAssetSelector();
  },

  // 保存记账页面设置
  // 保存记账页面设置
  saveBookingPageSettings: function () {
    const settings = {
      showAssetReminder: this.data.showAssetReminder,
      rememberCategoryAsset: this.data.rememberCategoryAsset,
      allowFutureRecords: this.data.allowFutureRecords,
      checkBalanceShortage: this.data.checkBalanceShortage,
      keyboardVibration: this.data.keyboardVibration,
      incomeAnimation: this.data.incomeAnimation,
      categoryStyle: this.data.categoryStyle,
      categoryRows: this.data.selectedRows,
      dateType: this.data.dateType
    };

    wx.setStorage({
      key: 'bookingPageSettings',
      data: settings
    });
  },
  // 打开分类样式选择弹窗
  selectCategoryStyleType: function () {
    this.setData({
      showCategoryStylePopup: true
    });
  },

  // 关闭分类样式选择弹窗
  closeCategoryStylePopup: function () {
    this.setData({
      showCategoryStylePopup: false
    });
  },

  // 处理弹窗状态变化
  onCategoryStylePopupChange: function (e) {
    if (!e.detail.visible) {
      this.setData({
        showCategoryStylePopup: false
      });
    }
  },

  // 选择分类样式
  selectCategoryStyle: function (e) {
    const style = e.currentTarget.dataset.style;
    let styleName = '列表翻页';

    if (style === 'card') {
      styleName = '卡片';
    }

    this.setData({
      categoryStyle: styleName,
      showCategoryStylePopup: false
    });

    // 保存设置
    this.saveBookingPageSettings();
  },
});
