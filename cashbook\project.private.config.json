{"description": "项目私有配置文件。此文件中的内容将覆盖 project.config.json 中的相同字段。项目的改动优先同步到此文件中。详见文档：https://developers.weixin.qq.com/miniprogram/dev/devtools/projectconfig.html", "projectname": "cashbook", "setting": {"compileHotReLoad": true, "urlCheck": false, "coverView": true, "lazyloadPlaceholderEnable": false, "skylineRenderEnable": false, "preloadBackgroundData": false, "autoAudits": false, "useApiHook": true, "useApiHostProcess": true, "showShadowRootInWxmlPanel": true, "useStaticServer": false, "useLanDebug": false, "showES6CompileOption": false, "checkInvalidKey": true, "ignoreDevUnusedFiles": true, "bigPackageSizeSupport": true}, "condition": {"miniprogram": {"list": [{"name": "pages/budget/budget", "pathName": "pages/budget/budget", "query": "", "scene": null, "launchMode": "default"}, {"name": "packageA/pages/introduction/introduction", "pathName": "packageA/pages/introduction/introduction", "query": "", "launchMode": "default", "scene": null}, {"name": "packageA/pages/myBooks/myBooks", "pathName": "packageA/pages/myBooks/myBooks", "query": "", "launchMode": "default", "scene": null}, {"name": "packageA/pages/settings/bookSettings/bookSettings", "pathName": "packageA/pages/settings/bookSettings/bookSettings", "query": "", "launchMode": "default", "scene": null}, {"name": "pages/inputMoney/inputMoney", "pathName": "pages/inputMoney/inputMoney", "query": "", "launchMode": "default", "scene": null}]}}, "libVersion": "3.8.4"}