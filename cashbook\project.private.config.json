{"setting": {"urlCheck": false, "bigPackageSizeSupport": true, "compileHotReLoad": true}, "condition": {"miniprogram": {"list": [{"name": "packageA/pages/bookSetting/incomeCategory/incomeCategory", "pathName": "packageA/pages/bookSetting/incomeCategory/incomeCategory", "query": "id=56", "scene": null, "launchMode": "default"}, {"name": "packageA/pages/bookDetail/bookDetail", "pathName": "packageA/pages/bookDetail/bookDetail", "query": "id=56", "launchMode": "default", "scene": null}, {"name": "packageA/pages/bookSetting/incomeCategory/sortCategory/sortCategory", "pathName": "packageA/pages/bookSetting/incomeCategory/sortCategory/sortCategory", "query": "type=expenses&accountbook_id=56&pid=0", "launchMode": "default", "scene": null}]}}}