// asset-change.scss
// 主题色变量 - 可根据个性化需求调整
$primary-color: var(--theme-primary, #1890ff);
$success-color: var(--theme-success, #52c41a);
$error-color: var(--theme-error, #ff4d4f);
$warning-color: var(--theme-warning, #faad14);
$text-primary: var(--theme-text-primary, #262626);
$text-secondary: var(--theme-text-secondary, #8c8c8c);
$background-color: var(--theme-bg, #f5f5f5);
$card-background: var(--theme-card-bg, #ffffff);

.asset-change-container {
  min-height: 100vh;
  background-color: $background-color;

  // 自定义导航栏
  .custom-navbar {
    background: $card-background;
    padding-top: env(safe-area-inset-top, 44rpx);
    padding-left: 32rpx;
    padding-right: 32rpx;
    padding-bottom: 20rpx;

    .navbar-content {
      display: flex;
      align-items: center;
      justify-content: space-between;

      .nav-left {
        width: 64rpx;
        height: 64rpx;
        display: flex;
        align-items: center;
        justify-content: center;
        background: #f5f5f5;
        border-radius: 50%;

        .icon-close {
          font-size: 32rpx;
          color: $text-secondary;
        }
      }

      .nav-right {
        .setting-text {
          font-size: 28rpx;
          color: #d4a574;
          background: #faf2e6;
          padding: 12rpx 24rpx;
          border-radius: 20rpx;
        }
      }
    }
  }

  // 头部内容区域
  .header-content {
    background: $card-background;
    padding: 0 32rpx 32rpx;

    .nav-center {
      display: flex;
      align-items: flex-start;

      .nav-icon {
        width: 80rpx;
        height: 80rpx;
        background: #f0f0f0;
        border-radius: 20rpx;
        display: flex;
        align-items: center;
        justify-content: center;
        margin-right: 24rpx;

        .chart-icon {
          font-size: 40rpx;
        }
      }

      .nav-title {
        flex: 1;

        .title {
          display: block;
          font-size: 36rpx;
          font-weight: 600;
          color: $text-primary;
          margin-bottom: 8rpx;
        }

        .subtitle {
          display: block;
          font-size: 28rpx;
          color: $text-secondary;
          line-height: 1.4;
        }
      }
    }
  }

  // 资产组成模块
  .asset-composition {
    margin: 32rpx;
    background: $card-background;
    border-radius: 24rpx;
    padding: 32rpx;

    .section-title {
      display: flex;
      align-items: center;
      margin-bottom: 32rpx;

      .title-indicator {
        width: 8rpx;
        height: 32rpx;
        background: $primary-color;
        border-radius: 4rpx;
        margin-right: 16rpx;
      }

      .title-text {
        font-size: 32rpx;
        font-weight: 600;
        color: $text-primary;
      }
    }

    .debt-section,
    .asset-section {
      margin-bottom: 40rpx;

      &:last-child {
        margin-bottom: 0;
      }

      .section-label {
        display: block;
        font-size: 28rpx;
        color: $text-secondary;
        margin-bottom: 24rpx;
      }
    }

    .debt-items,
    .asset-items {
      display: flex;
      gap: 16rpx;

      .debt-item,
      .asset-item {
        flex: 1;
        border-radius: 16rpx;
        padding: 32rpx 24rpx;
        position: relative;
        overflow: hidden;

        .debt-content,
        .asset-content {
          margin-bottom: 16rpx;

          .debt-name,
          .asset-name {
            display: block;
            font-size: 24rpx;
            color: rgba(255, 255, 255, 0.9);
            margin-bottom: 8rpx;
          }

          .debt-percentage,
          .asset-percentage {
            font-size: 24rpx;
            color: rgba(255, 255, 255, 0.7);
          }
        }

        .debt-amount,
        .asset-amount {
          font-size: 32rpx;
          font-weight: 600;
          color: white;
        }
      }

      .debt-item {
        background: $error-color;

        &.debt-main {
          flex: 2.5;
        }

        &.debt-secondary {
          flex: 1;
        }
      }

      .asset-item {
        background: $success-color;

        &.asset-main {
          flex: 1.4;
        }

        &.asset-secondary {
          flex: 1;
        }
      }
    }
  }

  // 资产变动图表
  .asset-chart {
    margin: 32rpx;
    background: $card-background;
    border-radius: 24rpx;
    padding: 32rpx;

    .chart-header {
      display: flex;
      justify-content: space-between;
      align-items: center;
      margin-bottom: 24rpx;

      .chart-title {
        display: flex;
        align-items: center;

        .title-indicator {
          width: 8rpx;
          height: 32rpx;
          background: $primary-color;
          border-radius: 4rpx;
          margin-right: 16rpx;
        }

        .title-text {
          font-size: 32rpx;
          font-weight: 600;
          color: $text-primary;
          margin-right: 16rpx;
        }

        .chart-type {
          background: #e6f4ff;
          color: $primary-color;
          font-size: 20rpx;
          padding: 4rpx 12rpx;
          border-radius: 8rpx;
        }
      }

      .chart-controls {
        display: flex;
        align-items: center;
        gap: 16rpx;

        .control-icon {
          font-size: 28rpx;
        }

        .time-period {
          font-size: 24rpx;
          color: $text-secondary;
        }
      }
    }

    .chart-date {
      margin-bottom: 32rpx;

      .date-text {
        font-size: 28rpx;
        color: $text-secondary;
      }
    }

    .chart-container {
      height: 300rpx;
      position: relative;
      margin-bottom: 40rpx;

      .chart-canvas {
        width: 100%;
        height: 100%;
      }

      .chart-placeholder {
        position: absolute;
        top: 0;
        left: 0;
        width: 100%;
        height: 100%;

        .chart-line {
          position: relative;
          width: 100%;
          height: 100%;

          &::before {
            content: '';
            position: absolute;
            top: 50%;
            left: 0;
            width: 100%;
            height: 2rpx;
            background: linear-gradient(90deg, $primary-color 0%, transparent 50%, $primary-color 100%);
            transform: translateY(-50%);
          }

          .chart-point {
            position: absolute;
            width: 12rpx;
            height: 12rpx;
            background: $primary-color;
            border-radius: 50%;
            transform: translate(-50%, -50%);

            &::before {
              content: '';
              position: absolute;
              top: 50%;
              left: 50%;
              width: 24rpx;
              height: 24rpx;
              background: rgba(24, 144, 255, 0.2);
              border-radius: 50%;
              transform: translate(-50%, -50%);
            }
          }
        }
      }
    }

    .chart-button {
      text-align: center;

      .button-text {
        display: inline-block;
        background: $primary-color;
        color: white;
        font-size: 28rpx;
        padding: 24rpx 64rpx;
        border-radius: 40rpx;
        font-weight: 500;
      }
    }
  }

  // 银行卡信息
  .bank-card {
    margin: 32rpx;
    background: $card-background;
    border-radius: 24rpx;
    padding: 32rpx;

    .card-info {
      display: flex;
      align-items: center;
      margin-bottom: 24rpx;

      .bank-logo {
        width: 64rpx;
        height: 64rpx;
        border-radius: 12rpx;
        margin-right: 24rpx;
      }

      .card-details {
        flex: 1;

        .bank-name {
          display: block;
          font-size: 32rpx;
          font-weight: 600;
          color: $text-primary;
          margin-bottom: 8rpx;
        }

        .card-type {
          font-size: 28rpx;
          color: $text-secondary;
          margin-right: 16rpx;
        }

        .card-icon {
          font-size: 24rpx;
        }
      }

      .card-badge {
        font-size: 32rpx;
      }
    }

    .card-amount {
      margin-bottom: 32rpx;

      .amount-date {
        display: block;
        font-size: 24rpx;
        color: $text-secondary;
        margin-bottom: 8rpx;
      }

      .amount-value {
        font-size: 36rpx;
        font-weight: 600;
        color: $error-color;
      }
    }

    .card-chart {
      .mini-chart {
        height: 120rpx;
        position: relative;
        background: #f8f9fa;
        border-radius: 12rpx;
        overflow: hidden;

        .chart-line-mini {
          position: absolute;
          top: 50%;
          left: 0;
          width: 100%;
          height: 2rpx;
          background: linear-gradient(90deg, $primary-color 0%, $error-color 50%, $primary-color 100%);
          transform: translateY(-50%);
        }

        .chart-points-mini {
          position: relative;
          width: 100%;
          height: 100%;

          .point-mini {
            position: absolute;
            width: 8rpx;
            height: 8rpx;
            background: $primary-color;
            border-radius: 50%;
            transform: translate(-50%, -50%);
          }
        }
      }
    }
  }
}

// 主题适配
:root {
  &[data-theme='blue'] {
    --theme-primary: #1890ff;
    --theme-success: #52c41a;
    --theme-error: #ff4d4f;
  }

  &[data-theme='green'] {
    --theme-primary: #52c41a;
    --theme-success: #73d13d;
    --theme-error: #ff7875;
  }

  &[data-theme='purple'] {
    --theme-primary: #722ed1;
    --theme-success: #95de64;
    --theme-error: #ff85c0;
  }

  &[data-theme='orange'] {
    --theme-primary: #fa8c16;
    --theme-success: #52c41a;
    --theme-error: #ff4d4f;
  }
}

// 响应式适配
@media (max-width: 375px) {
  .asset-change-container {
    .custom-navbar {
      padding: 40rpx 24rpx 24rpx;
    }

    .asset-composition,
    .asset-chart,
    .bank-card {
      margin: 24rpx 16rpx;
      padding: 24rpx;
    }
  }
}
