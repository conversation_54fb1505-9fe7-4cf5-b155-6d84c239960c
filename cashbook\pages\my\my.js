const util = require('../../utils/index.js')

// 假设这是您的 my.js 文件
Page({
  data: {
    isLoggedIn: false,
    userInfo: {},
    billDay: 1,
    list: [
      { id: 1, name: '收支分类' },
      { id: 2, name: '客服' },
      { id: 3, name: '预算设置' },
      { id: 4, name: '存钱' },
      { id: 5, name: '购物清单' },
      { id: 6, name: '标签' }
    ],
    list2: [
      { id: 1, name: '账单管理' },
      { id: 2, name: '定时记账' },
      { id: 3, name: '账单报告' },
      { id: 4, name: '资产' }
    ],
    list3: [
      { id: 1, name: '记录偏好' },
      { id: 2, name: '个性化' }
    ]
  },

  onLoad: function () {
    // 检查用户登录状态
    this.checkLoginStatus()
  },

  onShow: function () {
    // 每次页面显示时更新用户信息
    if (this.data.isLoggedIn) {
      this.getUserInfo()
    }
  },

  // 检查登录状态
  checkLoginStatus: function () {
    const token = wx.getStorageSync('token')
    if (token) {
      this.setData({
        isLoggedIn: true
      })
      // this.getUserInfo();
    } else {
      this.setData({
        isLoggedIn: false,
        userInfo: {}
      })
    }
  },

  // 获取用户信息
  getUserInfo: function () {
    // 这里应该是从服务器获取用户信息的API调用
    // 示例代码，实际应替换为您的API调用
    wx.showLoading({
      title: '加载中'
    })

    // 模拟API调用，实际开发中应替换为真实API
    setTimeout(() => {
      // 模拟从服务器获取的用户数据
      const userInfo = wx.getStorageSync('userInfo') || {
        nickName: '用户',
        avatarUrl: '/static/icon/default_avatar.png',
        accountDays: 1,
        isVip: false,
        lastLoginTime: this.formatDate(new Date()),
        signedToday: false,
        totalRecords: 0,
        continuousDays: 0,
        signInDays: 0
      }

      console.log(userInfo, 'userInfo')

      // 处理头像URL
      userInfo.avatar = util.getImageUrl(userInfo.avatar)

      // 更新用户信息
      this.setData({
        userInfo: userInfo,
        billDay: wx.getStorageSync('billDay') || 1
      })

      console.log(this.data.userInfo)

      wx.hideLoading()
    }, 500)
  },

  togobuylist: function () {
    wx.navigateTo({
      url: '/pages/my/shoplist/shoplist'
    })
  },
  // 跳转到记账偏好页面
  toBookSettings: function () {
    wx.navigateTo({
      url: '/packageA/pages/settings/accountPreference/accountPreference'
    })
  },

  // 格式化日期为 "yyyy-MM-dd HH:mm" 格式
  formatDate: function (date) {
    const year = date.getFullYear()
    const month = (date.getMonth() + 1).toString().padStart(2, '0')
    const day = date.getDate().toString().padStart(2, '0')
    const hours = date.getHours().toString().padStart(2, '0')
    const minutes = date.getMinutes().toString().padStart(2, '0')

    return `${year}-${month}-${day} ${hours}:${minutes}`
  },

  // 签到功能
  signIn: function () {
    if (!this.data.isLoggedIn) {
      wx.showToast({
        title: '请先登录',
        icon: 'none'
      })
      return
    }

    // 检查今日是否已签到
    if (this.data.userInfo.signedToday) {
      wx.showToast({
        title: '今日已签到',
        icon: 'success'
      })
      return
    }

    // 模拟签到API调用
    wx.showLoading({
      title: '签到中'
    })

    setTimeout(() => {
      // 更新用户信息
      const userInfo = this.data.userInfo
      userInfo.signedToday = true
      userInfo.signInDays = (userInfo.signInDays || 0) + 1

      this.setData({
        userInfo: userInfo
      })

      // 保存到本地存储，实际开发中应该发送到服务器
      wx.setStorageSync('userInfo', userInfo)

      wx.hideLoading()
      wx.showToast({
        title: '签到成功',
        icon: 'success'
      })
    }, 500)
  },

  // 跳转到登录页面
  toLogin: function () {
    wx.navigateTo({
      url: '/pages/login/login'
    })
  },

  // 跳转到用户信息页面
  toUserInfo: function () {
    if (!this.data.isLoggedIn) {
      wx.showToast({
        title: '请先登录',
        icon: 'none'
      })
      return
    }

    wx.navigateTo({
      url: '/pages/userInfo/userInfo'
    })
  },

  // 跳转到VIP中心
  toVip: function () {
    wx.navigateTo({
      url: '/pages/vip/vip'
    })
  },

  // 跳转到达人中心
  toVipCenter: function () {
    if (!this.data.isLoggedIn) {
      wx.showToast({
        title: '请先登录',
        icon: 'none'
      })
      return
    }

    wx.navigateTo({
      url: '/pages/vipCenter/vipCenter'
    })
  },

  // 跳转到设置页面
  toSet: function () {
    wx.navigateTo({
      url: '/packageA/pages/settings/settings'
    })
  },

  // 扫码功能
  scanCode: function () {
    wx.scanCode({
      success: (res) => {
        console.log(res)
        // 处理扫码结果
      },
      fail: (err) => {
        console.error(err)
        wx.showToast({
          title: '扫码失败',
          icon: 'none'
        })
      }
    })
  },

  // 跳转到活动页面
  toActivity: function () {
    wx.navigateTo({
      url: '/pages/activity/activity'
    })
  },

  toHelp: function () {
    wx.navigateTo({
      url: '/packageA/pages/help/help'
    })
  },
  // 跳转到个性化页面
  toPersonalization: function () {
    wx.navigateTo({
      url: '/packageA/pages/personalize/personalize'
    })
  }
})
