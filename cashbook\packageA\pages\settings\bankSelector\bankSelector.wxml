<!-- 顶部状态栏 -->
<view class="status-bar" style="height: {{statusBarHeight}}px;"></view>

<!-- 固定区域：顶部导航栏和搜索框 -->
<view class="fixed-header" style="padding-top: {{statusBarHeight}}px;">
  <!-- 顶部导航栏 -->
  <view class="navigation-bar">
    <view class="nav-left" bindtap="navigateBack">
      <t-icon name="chevron-left" size="48rpx" color="#333333" />
    </view>
    <view class="nav-title">{{navTitle || '借记卡'}}</view>
    <view class="nav-right"></view>
  </view>

  <!-- 搜索框和自定义按钮 -->
  <view class="search-container">
    <view class="search-box">
      <t-icon name="search" size="40rpx" color="#BBBBBB" />
      <input class="search-input" placeholder="搜索银行" placeholder-class="placeholder" bindinput="onSearchInput" value="{{searchKeyword}}" />
    </view>
    <view class="custom-button" bindtap="navigateToCustom">自定义</view>
  </view>
</view>

<!-- 内容区域（考虑固定头部的高度） -->
<view class="bank-selector-container" style="padding-top: {{headerHeight}}px;">
  <!-- 银行列表区域 -->
  <scroll-view scroll-y class="bank-list-container" enable-back-to-top scroll-into-view="{{scrollIntoView}}" scroll-with-animation>
    <!-- 加载中状态 -->
    <view class="loading-container" wx:if="{{loading}}">
      <t-loading theme="circular" size="40rpx" />
      <text class="loading-text">加载中...</text>
    </view>

    <!-- 无数据状态 -->
    <view class="empty-container" wx:elif="{{!bankList.length}}">
      <text class="empty-text">暂无银行数据</text>
    </view>

    <!-- 银行列表 -->
    <block wx:else>
      <!-- 按字母分组的银行 -->
      <view wx:for="{{bankList}}" wx:key="id" wx:for-item="bank">
        <!-- 字母索引标题（使用van-sticky实现吸顶效果） -->
        <!-- <van-sticky wx:if="{{bank.isInitial}}"> -->
        <view id="{{bank.initial}}" wx:if="{{bank.isInitial}}" class="index-title">{{bank.initial}}</view>
        <!-- </van-sticky> -->

        <!-- 银行项 -->
        <view class="bank-item" wx:else bindtap="selectBank" data-bank="{{bank}}">
          <view class="bank-icon">
            <image src="{{bank.icon}}" mode="aspectFit"></image>
          </view>
          <view class="bank-name">{{bank.name}}</view>
        </view>
      </view>
    </block>
  </scroll-view>

  <!-- 右侧字母索引 -->
  <view class="letter-index">
    <view class="letter-item {{currentLetter === letter ? 'active' : ''}}" wx:for="{{letters}}" wx:key="*this" wx:for-item="letter" bindtap="onLetterTap" data-letter="{{letter}}">
      {{letter}}
    </view>
  </view>
</view>

<!-- 自定义图片名称弹窗 -->
<custom-popup visible="{{showCustomPopup}}" title="自定义图片名称" position="bottom" round="{{true}}" closeButtonPosition="left" bind:close="onCustomPopupClose">
  <view class="custom-image-form">
    <view class="form-item">
      <input class="image-url-input" placeholder="图片链接" value="{{customImageUrl}}" bindinput="onImageUrlInput" />
    </view>
    <view class="form-item">
      <input class="image-name-input" placeholder="名称" value="{{customImageName}}" bindinput="onImageNameInput" />
    </view>
    <view class="save-button" bindtap="onSaveCustomImage">保存</view>
  </view>
</custom-popup>
