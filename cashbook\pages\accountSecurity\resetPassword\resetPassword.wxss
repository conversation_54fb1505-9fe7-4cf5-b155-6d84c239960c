/* 整体容器 */
.container {
  display: flex;
  flex-direction: column;
  min-height: 100vh;
  background-color: #f5f5f5;
  position: relative;
}

/* 顶部导航 */
.nav-header {
  display: flex;
  align-items: center;
  justify-content: space-between;
  padding: 10px 15px;
  background-color: #ffffff;
  position: sticky;
  top: 0;
  z-index: 100;
  box-shadow: 0 1px 3px rgba(0, 0, 0, 0.05);
}

.nav-back {
  width: 24px;
  height: 24px;
  display: flex;
  align-items: center;
  justify-content: center;
}

.nav-back image {
  width: 20px;
  height: 20px;
}

.nav-title {
  font-size: 16px;
  font-weight: 500;
}

.nav-placeholder {
  width: 24px;
}

/* 内容区域 */
.content {
  flex: 1;
  padding: 20px;
}

/* 密码输入区域 */
.password-section {
  margin-top: 30px;
}

/* 输入组 */
.input-group {
  position: relative;
  margin-bottom: 20px;
  background-color: #fff;
  border-radius: 8px;
  padding: 5px 15px;
  box-shadow: 0 1px 3px rgba(0, 0, 0, 0.05);
}

.password-input {
  height: 45px;
  width: 100%;
  font-size: 15px;
  padding-right: 40px; /* 为眼睛图标留出空间 */
}

.password-toggle {
  position: absolute;
  right: 15px;
  top: 50%;
  transform: translateY(-50%);
  width: 24px;
  height: 24px;
  display: flex;
  align-items: center;
  justify-content: center;
}

.password-toggle image {
  width: 20px;
  height: 20px;
}

/* 修改按钮 */
.modify-btn {
  width: 90%;
  height: 45px;
  background-color: #a5ddb9;
  border-radius: 22.5px;
  display: flex;
  justify-content: center;
  align-items: center;
  margin: 40px auto;
  color: #fff;
  font-size: 16px;
  box-shadow: 0 2px 5px rgba(165, 221, 185, 0.3);
}

/* 底部导航栏 */
.bottom-tabbar {
  display: flex;
  justify-content: space-around;
  align-items: center;
  height: 50px;
  background-color: #fff;
  border-top: 1px solid #f0f0f0;
}

.tab-item {
  display: flex;
  justify-content: center;
  align-items: center;
  flex: 1;
}

.tab-item image {
  width: 24px;
  height: 24px;
}