<view class="list">
  <!-- 本月账单/近期账单 -->
  <!-- <view class="oneColonm">
    <view>{{time}}</view>
    <view bind:tap="toggele" class="time" wx:if="{{current==0}}">按时间</view>
    <view bind:tap="toggele2" class="time" wx:else="">按金额</view>
  </view> -->
  <!-- 列表 -->
  <view class="box" wx:for="{{displayList}}" wx:key="index">
    <!-- 仅在按时间排序时显示日期标题栏 -->
    <view class="date-header" wx:if="{{sortType === 'time' && (index === 0 || displayList[index].time !== displayList[index-1].time)}}">
      <view class="date-left">
        <text class="date-day">{{item.time}}</text>
        <text class="date-today" wx:if="{{item.istoday}}">{{item.istoday}}</text>
        <image class="date-refresh" src="/static/icon/share.png" mode="" />
      </view>
      <view class="date-income">收支:{{item.payAccount}}</view>
    </view>
    
    <!-- 转账项 -->
    <view class="transfer-item" wx:if="{{item.isTransfer}}">
      <view class="transfer-icon">
        <image src="/static/icon/jr.png" mode="" />
      </view>
      <view class="transfer-content">
        <view class="transfer-title">内部转账</view>
        <view class="transfer-desc-text">
          <!-- 按金额排序时在描述中显示时间 -->
          <block wx:if="{{sortType === 'amount'}}">{{item.time}} </block>{{item.desc}}
        </view>
      </view>
      <view class="transfer-right">
        <view class="transfer-amount">¥ {{item.amountDisplay || item.amount}}</view>
        <view class="transfer-remark">{{item.remark || ''}}</view>
      </view>
    </view>
    
    <!-- 普通消费项 -->
    <view class="item" wx:if="{{item.payList}}" wx:for="{{item.payList}}" wx:for-item="payItem" wx:key="payIndex">
      <view class="item2">
        <view class="avator">
          <image src="/static/icon/avator.png" mode="" />
        </view>
        <view>{{payItem.type}}</view>
        <view class="acount">￥{{payItem.account}}</view>
      </view>
    </view>
  </view>
</view>