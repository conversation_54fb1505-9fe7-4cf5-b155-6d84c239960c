<view class="list">
  <!-- 本月账单/近期账单 -->
  <!-- <view class="oneColonm">
    <view>{{time}}</view>
    <view bind:tap="toggele" class="time" wx:if="{{current==0}}">按时间</view>
    <view bind:tap="toggele2" class="time" wx:else="">按金额</view>
  </view> -->
  <!-- 列表 -->
  <view class="box" wx:for="{{displayList}}" wx:key="index">
    <!-- 日期标题栏 - 只为日期标题类型的项显示 -->
    <view class="date-header" wx:if="{{item.isDateHeader}}">
      <view class="date-left">
        <text class="date-day">{{item.time}}</text>
        <text class="date-today" wx:if="{{item.istoday}}">{{item.istoday}}</text>
        <image class="date-refresh" bind:tap="share" src="/static/icon/share.png" mode="" />
      </view>
      <view class="date-income">
        <!-- 是否显示图标 -->
        <view class="icon-container">
          <van-icon vx:if="{{isShowIcon}}" name="warning-o" />
        </view>
        支:{{item.payAccount}}
      </view>
    </view>
    <!-- 账单项 - 只为非日期标题类型的项显示 -->
    <block wx:if="{{!item.isDateHeader}}">
      <!-- 使用van-swipe-cell包装transfer-item添加滑动功能，左右两侧都有按钮 -->
      <van-swipe-cell right-width="168" left-width="60" class="swipe-cell-item">
        <!-- 左侧复制按钮 -->
        <view slot="left" class="copy-button-container">
          <view class="copy-button" bindtap="handleActionClick" data-action="复制" data-id="{{item.id || index}}">
            复制
          </view>
        </view>
        <!-- 转账项 -->
        <view class="transfer-item" bind:tap="onBillItemTap" data-type="transfer" data-index="{{index}}">
          <view class="transfer-icon">
            <image src="/static/icon/jr.png" mode="" />
          </view>
          <view class="transfer-content">
            <view class="transfer-title">{{item.title || '内部转账'}}</view>
            <view class="transfer-desc-text">
              <block wx:if="{{sortType === 'amount'}}">{{item.time}}</block>
              {{item.desc}}
            </view>
          </view>
          <view class="transfer-right">
            <!-- 字体的颜色是不一样的 -->
            <view class="transfer-amount">¥ {{item.amountDisplay || item.amount}}</view>
            <view class="transfer-arrow" wx:if="{{item.fromAccount && item.toAccount}}">利息10</view>
            <view class="transfer-remark">
              {{item.fromAccount || ''}} - {{item.toAccount || ''}}
            </view>
          </view>
        </view>
        <!-- 右侧滑动按钮 -->
        <view slot="right" class="right-buttons">
          <view class="migration-button" bindtap="handleActionClick" data-action="迁移" data-id="{{item.id || index}}">
            迁移
          </view>
          <view class="edit-button" bindtap="handleActionClick" data-action="编辑" data-id="{{item.id || index}}">
            编辑
          </view>
          <view class="delete-button" bindtap="handleActionClick" data-action="删除" data-id="{{item.id || index}}">
            删除
          </view>
        </view>
      </van-swipe-cell>
      <!-- 普通消费项 -->
      <block wx:if="{{item.payList && item.payList.length > 0}}" wx:for="{{item.payList}}" wx:for-item="payItem" wx:for-index="payIndex" wx:key="payIndex" bind:tap="onBillItemTap">
        <van-swipe-cell right-width="180" left-width="80" class="swipe-cell-item">
          <!-- 左侧复制按钮 -->
          <view slot="left" class="copy-button-container">
            <view class="copy-button" bindtap="handleActionClick" data-action="复制" data-id="{{item.id + '_' + payIndex || 'pay_' + index + '_' + payIndex}}">
              复制
            </view>
          </view>
          <view class="item2">
            <view class="avator">
              <image src="/static/icon/avator.png" mode="" />
            </view>
            <view>{{payItem.type}}</view>
            <view class="acount">￥{{payItem.account}}</view>
          </view>
          <!-- 右侧滑动按钮 -->
          <view slot="right" class="right-buttons">
            <view class="edit-button" bindtap="handleActionClick" data-action="编辑" data-id="{{item.id + '_' + payIndex || 'pay_' + index + '_' + payIndex}}">
              编辑
            </view>
            <view class="edit-button" bindtap="handleActionClick" data-action="编辑" data-id="{{item.id + '_' + payIndex || 'pay_' + index + '_' + payIndex}}">
              编辑
            </view>
            <view class="delete-button" bindtap="handleActionClick" data-action="删除" data-id="{{item.id + '_' + payIndex || 'pay_' + index + '_' + payIndex}}">
              删除
            </view>
          </view>
        </van-swipe-cell>
      </block>
    </block>
  </view>
  
  <!-- 用于生成图片的隐藏canvas -->
  <canvas type="2d" id="shareCanvas" style="width: 100%; height: 100%; position: fixed; top: 0; left: -999999px;"></canvas>
</view>

<!-- 将弹窗组件移到外部，确保其位置不受内容高度影响 -->
<view class="global-popup-container">
  <!-- 账单详情弹窗 -->
  <accountDetailsPopup visible="{{showPopup}}" />
  
  <!-- 分享账单弹窗 -->
  <custom-popup visible="{{showAddBookPopup}}" closeButtonPosition="left" title="分享给朋友" position="bottom" bind:close="closeAddBookPopup">
    <scroll-view scroll-y="true" class="bill-card-scroll" style="height: 50vh; width: 100%; padding-bottom: 20rpx; overflow-y: auto; -webkit-overflow-scrolling: touch; display: block;">
      <view class="bill-card-new" style="background-color: {{cardStyle.backgroundColor}};">
      <!-- 卡片头部 -->
      <view class="bill-header-new">
        <view class="bill-title-new">
          <view class="app-name-new">小青账</view>
          <view class="app-desc-new">和小蛙一起记账省钱</view>
        </view>
        <view class="frog-icon-new">
          <image src="/static/images/frog.png" mode="aspectFit" />
        </view>
      </view>
      <!-- 总计消费统计 -->
      <view class="bill-summary-new">
        <view class="summary-item-new" style="background-color: {{cardStyle.summaryBgColor}}">
          <view class="summary-text-new" style="color: {{cardStyle.textColor}}">
            共消费
            <text style="color: {{selectedColor}}">{{billData.expenseCount}}</text>
            笔，共计
          </view>
          <view class="summary-amount-new">
            <text style="color: {{selectedColor}}">{{billData.expenseTotal}}</text>
            元
          </view>
        </view>
        <view class="summary-item-new" style="background-color: {{cardStyle.summaryBgColor}}">
          <view class="summary-text-new" style="color: {{cardStyle.textColor}}">
            共收款
            <text style="color: {{selectedColor}}">{{billData.incomeCount}}</text>
            笔，共计
          </view>
          <view class="summary-amount-new">
            <text style="color: {{selectedColor}}">{{billData.expenseTotal}}</text>
            元
          </view>
        </view>
      </view>
      <!-- 日期分割线 -->
      <view class="date-divider-new">
        <view class="divider-line-new"></view>
        <view class="divider-date-new" style="color: {{cardStyle.textColor}}">
          {{billData.date}} 消费明细
        </view>
        <view class="divider-line-new"></view>
      </view>
        <!-- 消费明细 -->
      <view class="bill-details-wrapper-new">
        <block wx:if="{{billData.items && billData.items.length > 0}}">
          <block wx:for="{{billData.items}}" wx:key="index">
              <view class="transfer-item" bind:tap="onBillItemTap" data-type="transfer" data-index="{{index}}">
                <view class="transfer-icon">
                  <image src="/static/icon/jr.png" mode="" />
                </view>
                <view class="transfer-content">
                  <view class="transfer-title">{{item.title || '内部转账'}}</view>
                  <view class="transfer-desc-text">
                    <block wx:if="{{sortType === 'amount'}}">{{item.time}}</block>
                    {{item.desc}}
                  </view>
                </view>
                <view class="transfer-right">
                  <!-- 字体的颜色是不一样的 -->
                  <view class="transfer-amount">¥ {{item.amountDisplay || item.amount}}</view>
                  <view class="transfer-arrow" wx:if="{{item.fromAccount && item.toAccount}}">
                    利息10
                  </view>
                  <view class="transfer-remark">
                    {{item.fromAccount || ''}} - {{item.toAccount || ''}}
                  </view>
                </view>
              </view>
            </block>
          </block>
          <block wx:else>
            <view class="bill-item-new">
              <view class="item-left-new">
                <view class="item-icon-new">
                  <image src="/static/icon/food.png" mode="aspectFit" />
                </view>
                <view class="item-category-new" style="color: {{cardStyle.textColor}}">
                  餐饮
                  <text class="item-subcategory-new" style="color: {{cardStyle.textColor}}">
                    午餐
                  </text>
                </view>
              </view>
              <view class="item-right-new">
                <view class="item-amount-new" style="color: #20C0E0">¥ 20.00</view>
                <view class="item-interest" style="color: #ff4d4f; font-size: 24rpx;">利息20.00</view>
                <view class="item-transfer-info" style="color: #999; font-size: 22rpx; max-width: 200rpx; white-space: nowrap; overflow: hidden; text-overflow: ellipsis;">
                  午餐
                </view>
              </view>
            </view>
          </block>
          </view>
      </view>
      <view class="brand-footer" wx:if="{{cardFormat !== 'none'}}" style="background-color: {{cardStyle.backgroundColor === storedColors.theme2.backgroundColor ? '#183241' : '#ffffff'}}">
        <!-- 青蛙样式 -->
        <view class="frog-brand" wx:if="{{cardFormat === 'frog'}}">
          <view class="brand-logo">
            <image src="/static/images/frog-mini.png" mode="aspectFit" />
          </view>
          <view class="brand-info">
            <view class="brand-name" style="color: {{selectedColor}}">小青账</view>
            <view class="brand-slogan" style="color: {{cardStyle.backgroundColor === storedColors.theme2.backgroundColor ? 'rgba(255,255,255,0.8)' : '#999'}}">
              享受记账乐趣，为财务自由做好准备～
            </view>
          </view>
        </view>
        <!-- 个人头像样式 -->
        <view class="user-brand" wx:elif="{{cardFormat === 'avatar'}}">
          <view class="user-info">
            <view class="user-avatar">
              <image src="/static/images/user-avatar.png" mode="aspectFit" class="avatar-image" />
            </view>
            <view class="user-details">
              <view class="user-name-wrapper">
                <view class="user-name" style="color: {{selectedColor}}">西风.</view>
                <view class="vip-badge" style="background-color: {{selectedColor}}; color: #fff;">
                  VIP
                </view>
              </view>
              <view class="user-record" style="color: {{cardStyle.backgroundColor === storedColors.theme2.backgroundColor ? 'rgba(255,255,255,0.8)' : '#999'}}">
                今天是你记账的第27天啦 🎯
              </view>
            </view>
          </view>
        </view>
      </view>
    </scroll-view>
    <!-- 底部留白区域，确保内容不被按钮遮挡 -->
    <view style="height: 96rpx;"></view>
    <!-- 底部操作按钮 - 放在页面底部 -->
    <view class="floating-buttons" wx:if="{{showAddBookPopup}}">
      <button class="action-btn-new" style="background-color: {{btnStyle.backgroundColor}};" bind:tap="onChangeColor">
        更换颜色
      </button>
      <button class="action-btn-new" style="background-color: {{btnStyle.backgroundColor}};" bind:tap="onChangeFormat">
        样式
      </button>
      <button class="action-btn-new" style="background-color: {{btnStyle.backgroundColor}};" bind:tap="onSaveImage">
        保存图片
      </button>
    </view>
  </custom-popup>
</view>

<!-- 样式选择弹窗 -->
<custom-popup visible="{{ formatPopupVisible }}" closeButtonPosition="right" title="选择样式" position="bottom" bind:close="onCloseFormatPopup">
    <view class="format-options">
      <view class="format-option" bindtap="selectFormat" data-format="frog">
        <view class="option-text">小青账</view>
      <van-icon name="arrow" color="#ccc" />
      </view>
      <view class="format-option" bindtap="selectFormat" data-format="avatar">
        <view class="option-text">个人头像</view>
      <van-icon name="arrow" color="#ccc" />
      </view>
      <view class="format-option" bindtap="selectFormat" data-format="none">
        <view class="option-text">无</view>
      <van-icon name="arrow" color="#ccc" />
    </view>
  </view>
</custom-popup>