Component({
  /**
   * 组件的属性列表
   */
  properties: {
    visible: {
      type: Boolean,
      value: false
    },
    // 初始年份
    initYear: {
      type: Number,
      value: new Date().getFullYear()
    },
    // 初始月份
    initMonth: {
      type: Number,
      value: new Date().getMonth() + 1 // JavaScript 月份是 0-11
    },
    // 最小年份
    minYear: {
      type: Number,
      value: 2000
    },
    // 最大年份
    maxYear: {
      type: Number,
      value: 2100
    }
  },

  /**
   * 组件的初始数据
   */
  data: {
    currentYear: new Date().getFullYear(),
    currentMonth: new Date().getMonth() + 1,
    selectedYear: null,
    selectedMonth: null,
    // 滑动相关数据
    startY: 0,
    touchY: 0,
    touchTime: 0,
    isTouching: false,
    // 当前系统时间
    systemYear: new Date().getFullYear(),
    systemMonth: new Date().getMonth() + 1
  },

  /**
   * 组件生命周期
   */
  lifetimes: {
    attached() {
      // 设置初始值
      const month = this.properties.initMonth;
      
      // 获取当前系统时间
      const now = new Date();
      const systemYear = now.getFullYear();
      const systemMonth = now.getMonth() + 1;
      
      this.setData({
        currentYear: this.properties.initYear,
        currentMonth: month,
        selectedYear: this.properties.initYear,
        selectedMonth: month,
        systemYear,
        systemMonth
      });
    }
  },

  /**
   * 组件的方法列表
   */
  methods: {
    // 判断月份是否可选择
    isMonthSelectable(year, month) {
      const { systemYear, systemMonth } = this.data;
      
      // 如果是过去的年份，所有月份都可选
      if (year < systemYear) return true;
      
      // 如果是当前年份，当前月份及之前的月份可选，下个月可选，再之后不可选
      if (year === systemYear) return month <= systemMonth + 1;
      
      // 如果是未来的年份，所有月份都不可选
      return false;
    },
    
    // 选择月份
    selectMonth(e) {
      const month = parseInt(e.currentTarget.dataset.month);
      
      // 检查月份是否可选
      if (this.isMonthSelectable(this.data.currentYear, month)) {
        this.setData({
          currentMonth: month,
          selectedMonth: month
        });
      } else {
        wx.showToast({
          title: '不能选择未来的月份',
          icon: 'none'
        });
      }
    },
    
    // 上一年
    prevYear() {
      const { currentYear, minYear } = this.data;
      if (currentYear > minYear) {
        this.setData({
          currentYear: currentYear - 1
        });
      } else {
        wx.showToast({
          title: `不能早于${minYear}年`,
          icon: 'none'
        });
      }
    },
    
    // 下一年
    nextYear() {
      const { currentYear, maxYear } = this.data;
      if (currentYear < maxYear) {
        this.setData({
          currentYear: currentYear + 1
        });
      } else {
        wx.showToast({
          title: `不能晚于${maxYear}年`,
          icon: 'none'
        });
      }
    },
    
    // 取消选择
    onCancel() {
      this.triggerEvent('cancel');
    },
    
    // 确认选择
    onConfirm() {
      const { currentYear, currentMonth } = this.data;
      this.triggerEvent('confirm', {
        year: currentYear,
        month: currentMonth,
        // 格式化为 YYYY-MM 格式供其他组件使用
        date: `${currentYear}-${String(currentMonth).padStart(2, '0')}`
      });
    },
    
    // 触摸开始
    touchStart(e) {
      this.setData({
        startY: e.touches[0].clientY,
        touchY: e.touches[0].clientY,
        touchTime: e.timeStamp,
        isTouching: true
      });
    },
    
    // 触摸移动
    touchMove(e) {
      if (!this.data.isTouching) return;
      
      const currentY = e.touches[0].clientY;
      const moveY = currentY - this.data.touchY;
      
      this.setData({
        touchY: currentY
      });
      
      // 上下滑动判断逻辑
      if (Math.abs(moveY) > 10) {
        if (moveY > 0) {
          // 下滑，前一年
          this.prevYear();
        } else {
          // 上滑，后一年
          this.nextYear();
        }
        
        // 防止连续触发，设置一个小延迟
        this.setData({
          isTouching: false
        });
        
        setTimeout(() => {
          this.setData({
            isTouching: true
          });
        }, 200);
      }
    },
    
    // 触摸结束
    touchEnd(e) {
      this.setData({
        isTouching: false
      });
    }
  }
}) 