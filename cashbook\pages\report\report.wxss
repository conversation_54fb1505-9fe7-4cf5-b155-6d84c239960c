.container {
  background-color: #e8f0d9;
  min-height: 100vh;
  padding-bottom: 100px;
}

/* 顶部导航栏 */
.nav-bar {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 15px 20px;
  background-color: #e8f0d9;
}

.tab-container {
  display: flex;
  background-color: #2d3142;
  border-radius: 20px;
  padding: 3px;
  width: 140px;
}

.tab {
  padding: 5px 15px;
  text-align: center;
  color: #fff;
  border-radius: 20px;
  font-size: 14px;
}

.more-icon image {
  width: 24px;
  height: 24px;
}

/* 日期选择器 */
.date-selector {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 10px 20px;
  margin-bottom: 10px;
}

.date {
  display: flex;
  align-items: center;
  font-size: 16px;
  font-weight: 500;
}

.date image {
  width: 16px;
  height: 16px;
  margin-left: 5px;
}

.filter {
  font-size: 14px;
  color: #333;
}

/* 收支统计卡片 */
.stats-card {
  background-color: #fff;
  border-radius: 15px;
  margin: 0 15px;
  padding: 15px;
  box-shadow: 0 2px 5px rgba(0, 0, 0, 0.05);
}

.stats-tabs {
  display: flex;
  justify-content: space-between;
  margin-bottom: 20px;
}

.stats-tab {
  display: flex;
  flex-direction: column;
  align-items: center;
  width: 33%;
}

.stats-tab radio {
  transform: scale(0.8);
  margin-bottom: 5px;
}

.stats-tab text {
  font-size: 14px;
  color: #666;
}

.amount {
  font-size: 16px;
  font-weight: 500;
  margin-top: 5px;
}

.expense {
  color: #ff5252;
}

.income {
  color: #4caf50;
}

.other {
  color: #333;
}

/* 环形图表 */
.chart-container {
  position: relative;
  height: 280px;
  display: flex;
  justify-content: center;
  align-items: center;
  margin: 20px 0;
}

.pie-chart {
  position: relative;
  width: 100%;
  height: 280px;
}

/* 图表中心内容 */
.chart-center {
  position: absolute;
  top: 50%;
  left: 50%;
  transform: translate(-50%, -50%);
  text-align: center;
  z-index: 10;
  pointer-events: none;
  display: flex;
  flex-direction: column;
  align-items: center;
}

.center-title {
  font-size: 16px;
  color: #333;
  font-weight: 500;
  margin-bottom: 2px;
}

.center-icon {
  width: 18px;
  height: 18px;
  margin: 2px 0;
  pointer-events: auto;
}

.center-icon image {
  width: 100%;
  height: 100%;
}

.center-subtitle {
  font-size: 12px;
  color: #999;
}

/* 环形图标签 */
.chart-labels {
  position: absolute;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
  pointer-events: none;
}

.chart-label {
  position: absolute;
  display: flex;
  align-items: center;
  white-space: nowrap;
  font-size: 12px;
  color: #333;
}

.label-left {
  left: 15%;
  top: 45%;
  transform: translateY(-50%);
}

.label-top {
  top: 15%;
  left: 50%;
  transform: translateX(-50%);
  flex-direction: column;
}

.label-right {
  right: 15%;
  top: 45%;
  transform: translateY(-50%);
}

.label-text {
  font-size: 12px;
  color: #333;
}

.label-line {
  margin: 0 4px;
  color: #666;
}

.label-percent {
  font-size: 12px;
  color: #333;
  font-weight: 500;
}

/* 面积图表样式 */
.area-chart-container {
  display: flex;
  flex-direction: column;
  margin: 15px;
  background-color: #f9f9f9;
  border-radius: 10px;
  padding: 15px;
}

.area-chart-item {
  display: flex;
  flex-direction: column;
  margin-bottom: 10px;
  background-color: #fff;
  border-radius: 8px;
  padding: 12px;
  box-shadow: 0 1px 3px rgba(0, 0, 0, 0.05);
}

.area-chart-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 8px;
}

.area-chart-title {
  font-size: 14px;
  color: #333;
  font-weight: 500;
}

.area-chart-percentage {
  font-size: 14px;
  color: #666;
}

.area-chart-progress {
  height: 6px;
  background-color: #e0e0e0;
  border-radius: 3px;
  overflow: hidden;
  margin-bottom: 8px;
}

.area-chart-progress-bar {
  height: 100%;
  background-color: #a2b486;
  border-radius: 3px;
}

/* 类别列表样式优化 */
.category-list {
  margin: 15px;
}

.category-item {
  display: flex;
  align-items: center;
  background-color: #f9f9f9;
  border-radius: 10px;
  padding: 15px;
  margin-bottom: 10px;
}

.category-icon {
  width: 40px;
  height: 40px;
  background-color: #fff;
  border-radius: 8px;
  display: flex;
  justify-content: center;
  align-items: center;
  margin-right: 10px;
}

.category-icon image {
  width: 24px;
  height: 24px;
}

.category-info {
  flex: 1;
}

.category-name {
  font-size: 14px;
  color: #333;
  margin-bottom: 5px;
  display: flex;
  align-items: center;
}

.category-percentage {
  font-size: 12px;
  color: #666;
  margin-left: 5px;
}

.progress-bar {
  height: 6px;
  background-color: #e0e0e0;
  border-radius: 3px;
  overflow: hidden;
  width: 100%;
}

.progress {
  height: 100%;
  background-color: #a2b486;
  border-radius: 3px;
}

.category-amount {
  text-align: right;
  margin-left: 10px;
}

.category-amount .amount {
  display: block;
  color: #ff5252;
  font-size: 16px;
  font-weight: 500;
}

.income-amount .amount {
  color: #4caf50;
}

.category-amount .count {
  display: block;
  color: #999;
  font-size: 12px;
  margin-top: 3px;
}

/* 收入类别列表样式 */
.income-category-item {
  background-color: #f5f9f0;
}

.income-category-icon {
  background-color: #eaf5e0;
}

.income-progress {
  background-color: #4caf50;
}

/* 面积图表切换样式 */
.toggle-option {
  padding: 5px 10px;
  font-size: 12px;
  text-align: center;
}

.toggle-option.active {
  background-color: #a2b486;
  color: white;
}

/* 图表类型切换 */
.chart-type-switch {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-top: 15px;
  padding: 0 10px;
}

.switch-left {
  display: flex;
  align-items: center;
}

.switch-category {
  display: flex;
  align-items: center;
  padding: 5px 10px;
  border-radius: 15px;
  background-color: #f5f5f5;
}

.switch-icon {
  width: 16px;
  height: 16px;
  margin-right: 5px;
}

.switch-right {
  display: flex;
  align-items: center;
}

.chart-type-toggle {
  display: flex;
  background-color: #f5f5f5;
  border-radius: 15px;
  overflow: hidden;
}

.toggle-option {
  padding: 5px 12px;
  font-size: 12px;
  color: #666;
}

.toggle-option.active {
  background-color: #FF9800;
  color: #fff;
}

/* 支出类别列表 */
.category-list {
  margin: 15px;
}

.category-item {
  display: flex;
  align-items: center;
  background-color: #f9f9f9;
  border-radius: 10px;
  padding: 15px;
  margin-bottom: 10px;
}

.category-icon {
  width: 40px;
  height: 40px;
  background-color: #fff;
  border-radius: 8px;
  display: flex;
  justify-content: center;
  align-items: center;
  margin-right: 10px;
}

.category-icon image {
  width: 24px;
  height: 24px;
}

.category-info {
  flex: 1;
}

.category-name {
  font-size: 14px;
  color: #333;
  margin-bottom: 5px;
}

.progress-bar {
  height: 6px;
  background-color: #e0e0e0;
  border-radius: 3px;
  overflow: hidden;
  width: 100%;
}

.progress {
  height: 100%;
  background-color: #8bc34a;
  border-radius: 3px;
}

.category-amount {
  text-align: right;
  margin-left: 10px;
}

.category-amount .amount {
  display: block;
  color: #ff5252;
  font-size: 16px;
  font-weight: 500;
}

.category-amount .count {
  display: block;
  color: #999;
  font-size: 12px;
  margin-top: 3px;
}

/* 底部导航栏样式会由组件提供 */
.chart {
  width: 700rpx;
  height: 600rpx;
}

/* 年月选择弹窗样式 */
.date-picker-popup {
  position: fixed;
  bottom: 0;
  left: 0;
  width: 100%;
  height: 100%;
  z-index: 1000;
  display: flex;
  flex-direction: column;
  justify-content: flex-end;
}

.popup-mask {
  position: absolute;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
  background-color: rgba(0, 0, 0, 0.5);
}

.popup-content {
  position: relative;
  background-color: #e8f0d9;
  border-top-left-radius: 20px;
  border-top-right-radius: 20px;
  padding: 20px;
  z-index: 1001;
}

.popup-header {
  text-align: center;
  margin-bottom: 15px;
}

.popup-header text {
  font-size: 18px;
  font-weight: 500;
  display: block;
  margin-bottom: 15px;
}

.tab-container {
  display: flex;
  justify-content: space-between;
  background-color: #f5f5f5;
  border-radius: 10px;
  padding: 5px;
  margin: 0 auto;
  width: 90%;
}

.tab {
  flex: 1;
  text-align: center;
  padding: 8px 0;
  font-size: 14px;
  color: #666;
  border-radius: 8px;
}

.tab.active {
  background-color: #2d354a;
  color: #fff;
}

.year-selector {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 15px 10px;
  background-color: #fff;
  border-radius: 10px;
  margin: 15px 0;
}

.year-selector text {
  font-size: 16px;
  font-weight: 500;
}

.year-arrows {
  display: flex;
  flex-direction: column;
  gap: 5px;
}

.arrow {
  width: 24px;
  height: 24px;
  display: flex;
  justify-content: center;
  align-items: center;
}

.arrow image {
  width: 16px;
  height: 16px;
}

.month-grid {
  background-color: #fff;
  border-radius: 10px;
  padding: 15px;
  margin-bottom: 15px;
}

.month-row {
  display: flex;
  justify-content: space-between;
  margin-bottom: 15px;
}

.month-row:last-child {
  margin-bottom: 0;
}

.month-item {
  width: 60px;
  height: 40px;
  display: flex;
  justify-content: center;
  align-items: center;
  border-radius: 20px;
  font-size: 14px;
  color: #333;
}

.month-item.active {
  background-color: #a2b486;
  color: #fff;
}

.popup-footer {
  display: flex;
  justify-content: space-between;
  padding: 0 20px;
}

.btn {
  width: 100px;
  height: 40px;
  border-radius: 20px;
  display: flex;
  justify-content: center;
  align-items: center;
  font-size: 16px;
}

.cancel {
  background-color: #f5f5f5;
  color: #666;
}

.confirm {
  background-color: #a2b486;
  color: #fff;
}

/* 自定义单选按钮样式 */
.radio-circle {
  width: 18px;
  height: 18px;
  border-radius: 50%;
  border: 1px solid #ccc;
  margin-bottom: 5px;
  display: flex;
  justify-content: center;
  align-items: center;
}

.radio-circle.active {
  border-color: #00aa00;
  background-color: #fff;
  position: relative;
}

.radio-circle.active::after {
  content: "";
  width: 12px;
  height: 12px;
  border-radius: 50%;
  background-color: #00aa00;
  position: absolute;
}

/* 树状图(面积图)样式 */
.treemap-chart {
  position: relative;
  width: 100%;
  height: 260px;
  background-color: #fff;
  border-radius: 8px;
  overflow: hidden;
}
 
