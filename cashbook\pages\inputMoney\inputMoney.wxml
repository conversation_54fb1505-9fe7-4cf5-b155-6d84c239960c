<view class="box">
  <!-- 自定义导航栏 -->
  <view class="mynav" style="background-color: {{selectedColor}};padding-top: {{windowHeight}}px;">
    <scrollTap bind:getId="getId" isIcon="{{false}}" isback="{{true}}" isSet="{{false}}" list="{{list1}}" activeId="{{currentMouds}}"></scrollTap>
  </view>
  <!-- 导航栏占位元素，防止内容上移 -->
  <view class="nav-placeholder" style="height: {{windowHeight + 49}}px;"></view>
  <!-- <view bind:tap="ceshi">点击切换</view> -->
  <!-- 切换  轮播图点击按钮切换  加减currentItem即可 -->
  <swiper current="{{currentItem}}" style="height: {{swiperHeight}}px;padding-top:{{swiperTop}}px" bindchange="swperChange" bindtouchstart="swiperTouchStart" bindtouchmove="swiperTouchMove" bindtouchend="swiperTouchEnd">
    <block>
      <!-- tab1 支出 -->
      <swiper-item>
        <view class="list swiper-item-content">
          <view class="item" wx:for="{{list2}}" wx:key="index" bind:tap="changItem" data-id="{{item.id}}" data-index="{{index}}" data-type="expense">
            <view class="top">
              <view class="toppic" style="{{ item.id == currentID ? 'background-color:' + selectedColor : '' }}">
                <image src="{{item.icon}}" mode="" />
              </view>
              <!-- <view class="more  {{item.id==currentID ? 'active2': '' }}" > <span>...</span> </view> -->
            </view>
            <view class="text food-icon">{{item.name}}</view>
          </view>
          <view class="item">
            <view class="top">
              <view class="toppic ">
                <image src="/static/icon/set.png" mode="" />
              </view>
            </view>
            <view class="text">设置</view>
          </view>
        </view>
      </swiper-item>
      <!-- tab1 收入 -->
      <swiper-item>
        <view class="list swiper-item-content">
          <view class="item" wx:for="{{incomeList}}" bind:tap="changItem" data-id="{{item.id}}" data-index="{{index}}" data-type="income" wx:key="index">
            <view class="top">
              <view class="toppic" style="{{ item.id == incomeCurrentID ? 'background-color:' + selectedColor : '' }}">
                <image src="{{item.icon}}" mode="" />
              </view>
              <!-- <view class="more  {{item.id==incomeCurrentID ? 'active2': '' }}" > <span>...</span> </view> -->
            </view>
            <view class="text">{{item.name}}</view>
          </view>
          <view class="item">
            <view class="top">
              <view class="toppic ">
                <image src="/static/icon/set.png" mode="" />
              </view>
            </view>
            <view class="text">设置</view>
          </view>
        </view>
      </swiper-item>
      <!-- tab2 -->
      <swiper-item>
        <view class="zz swiper-item-content">
          <view class="help">帮助？</view>
          <view class="oneClonm" data-type="from" bindtap="openAccountSelector">
            <view class="zczh">
              <view class="account-icon-left">
                <image src="/static/icon/card.png" mode="aspectFit" />
              </view>
              <view class="account-info">
                <view class="account-name" wx:if="{{currentAccount.id !== 3}}">
                  {{currentAccount.name || '现金钱包'}}
                </view>
                <view class="account-name" wx:else>扣款账户</view>
                <view class="account-subname" wx:if="{{currentAccount.id != 3}}">
                  {{currentAccount.subName || '回家'}}
                </view>
              </view>
              <view class="account-icon-right">
                <image src="{{currentAccount.icon || '/static/icon/card.png'}}" mode="aspectFit" />
              </view>
            </view>
            <view class="account-label">扣款账户</view>
          </view>
          <view class="tranfs" bindtap="swapAccounts">
            <image src="/static/icon/xh.png" mode="" />
            转至
          </view>
          <view class="oneClonm" data-type="to" bindtap="openAccountSelector">
            <view class="zczh">
              <view class="account-icon-left">
                <image src="/static/icon/card.png" mode="aspectFit" />
              </view>
              <view class="account-info">
                <view class="account-name" wx:if="{{currentAccount2.id !== 3}}">
                  {{currentAccount2.name || '公积金'}}
                </view>
                <view class="account-name" wx:else>转入账户</view>
                <!-- 只有当不是"不选择具体账户"时才显示 account-subname -->
                <view class="account-subname" wx:if="{{currentAccount2.id != 3}}">
                  {{currentAccount2.subName || '回家'}}
                </view>
              </view>
              <view class="account-icon-right">
                <image src="{{currentAccount2.id === 3 ? '/static/icon/card.png' : (currentAccount2.icon || '/static/icon/card.png')}}" mode="aspectFit" />
              </view>
            </view>
            <view class="account-label">入款账户</view>
          </view>
          <!-- 手续费模块 -->
          <view class="sxfMouds">
            <view class="twoleaveColnm">
              <view class="sxf">
                <image src="/static/icon/card.png" mode="" />
                <view class="fee-input-container">
                  <input type="text" placeholder="{{isSX === 1 ? '手续费' : '优惠'}}" value="{{feeData.extraFee}}" bindinput="onFeeInput" />
                  <view class="clear-btn" wx:if="{{feeData.extraFee && feeData.extraFee.length > 0}}" catchtap="clearFeeInput">
                    <!-- <text class="clear-icon">×</text> -->
                    <van-icon name="cross" />
                  </view>
                </view>
              </view>
              <view bind:tap="SX" class="sxfitem {{isSX==1 ?'sxfitemActive' :''}}">手续费</view>
              <view bind:tap="YH" class="sxfitem {{isSX==2 ?'sxfitemActive' :''}}">优惠</view>
            </view>
            <view class="account-label" bind:tap="{{isSX === 1 ? 'openFeeCalculator' : 'showDiscountTip'}}">
              计算器
            </view>
          </view>
          <!-- 文本介绍 -->
          <view class="textIntro">
            <view>转账、信用卡还款、取现可以用这个功能哦。</view>
            <view>转出账户=转出金额+手续费</view>
            <view>转出账户=转出金额-优惠</view>
          </view>
        </view>
      </swiper-item>
      <!-- tab3 -->
      <swiper-item>
        <view class="borrowAndReturn swiper-item-content">
          <view class="brtab">
            <view bind:tap="changBr" data-id="{{item.id}}" class="britem {{item.id==brtabid ?'brActive' :''}}" wx:for="{{brtab}}" wx:key="index">
              {{item.name}}
            </view>
          </view>
          <!-- 使用swiper替换tabcontent -->
          <swiper current="{{brSwiperCurrent}}" bindchange="brSwiperChange" class="br-swiper" style="width: 100%; height: {{brSwiperHeight}}px; overflow: visible;">
            <!-- 借入内容 -->
            <swiper-item>
              <view class="tabcontent">
                <view class="tbcitem" wx:for="{{tabContentList}}" wx:key="index" bindtap="changeTbcItem" data-id="{{item.id}}" data-index="{{index}}" data-type="borrow">
                  <view class="tbcpic" style="{{ item.id == tbciActive ? 'background-color:' + selectedColor : '' }}">
                    <image src="{{item.icon}}" mode="" />
                  </view>
                  <view>{{item.name}}</view>
                </view>
              </view>
            </swiper-item>
            <!-- 借出内容 -->
            <swiper-item>
              <view class="tabcontent">
                <view class="tbcitem" wx:for="{{tabContentList2}}" wx:key="index" bindtap="changeTbcItem" data-id="{{item.id}}" data-index="{{index}}" data-type="lend">
                  <view class="tbcpic" style="{{ item.id == tbciActive2 ? 'background-color:' + selectedColor : '' }}">
                    <image src="{{item.icon}}" mode="" />
                  </view>
                  <view>{{item.name}}</view>
                </view>
              </view>
            </swiper-item>
          </swiper>
          <!-- 内容区域 -->
          <view class="content">
            <!-- 借入/借出账户部分 - 始终显示 -->
            <view class="one">
              <view class="onepic">
                <image src="/static/icon/card.png" mode="" />
              </view>
              {{brtabid == 1 ? '借入账户' : '借出账户'}}
            </view>
            <view class="description">虚拟账户:如找小明借钱，小明就是此账户</view>
            <!-- 资产账户部分 - 仅在非债务削减/坏账损失时显示 -->
            <block wx:if="{{showFullContent}}">
              <view class="oneClonm" bindtap="openAccountSelector">
                <view class="zczh">
                  <view class="account-icon-left">
                    <image src="/static/icon/card.png" mode="aspectFit" />
                  </view>
                  <view class="account-info1">
                    <view class="account-name" wx:if="{{currentAccount.id !== 3}}">
                      {{currentAccount.name || '现金钱包'}}
                    </view>
                    <view class="account-name" wx:else>扣款账户</view>
                    <view class="account-subname" wx:if="{{currentAccount.id != 3}}">
                      {{currentAccount.subName || '回家'}}
                    </view>
                  </view>
                  <view class="account-icon-right">
                    <image src="{{currentAccount.icon || '/static/icon/card.png'}}" mode="aspectFit" />
                  </view>
                </view>
              </view>
              <view class="description">资产账户:将金额累计到这个账户里</view>
              <!-- 利息部分 -->
              <view class="sxfMouds">
                <view class="twoleaveColnm">
                  <view class="sxf">
                    <image src="/static/icon/card.png" mode="" />
                    <view class="fee-input-container">
                      <input type="text" placeholder="{{isSX === 1 ? '手续费' : '优惠'}}" value="{{feeData.extraFee}}" bindinput="onFeeInput" />
                      <view class="clear-btn" wx:if="{{feeData.extraFee && feeData.extraFee.length > 0}}" catchtap="clearFeeInput">
                        <!-- <text class="clear-icon">×</text> -->
                        <van-icon name="cross" />
                      </view>
                    </view>
                  </view>
                  <view bind:tap="SX" class="sxfitem {{isSX==1 ?'sxfitemActive' :''}}">手续费</view>
                  <view bind:tap="YH" class="sxfitem {{isSX==2 ?'sxfitemActive' :''}}">优惠</view>
                </view>
                <view class="account-label" bind:tap="{{isSX === 1 ? 'openFeeCalculator' : 'showDiscountTip'}}">
                  计算器
                </view>
              </view>
              <!-- 文本说明部分 -->
              <view class="textIntro">
                <view>利息根据个人需求可在借出或者收债时候添加;一般在一方添加即可</view>
                <view>{{brtabid == 1 ? '借入' : '借出'}}:</view>
                <view>{{brtabid == 1 ? '借入' : '借出'}}账户 = {{brtabid == 1 ? '借入' : '借出'}}金额+利息</view>
                <view>资产账户 ={{brtabid == 1 ? '借入' : '借出'}}金额</view>
                <view>{{brtabid == 1 ? '还债' : '收债'}}:</view>
                <view>{{brtabid == 1 ? '借入' : '借出'}}账户 = {{brtabid == 1 ? '借入' : '借出'}}金额</view>
                <view>资产账户 ={{brtabid == 1 ? '借入' : '借出'}}金额 +利息</view>
              </view>
            </block>
            <!-- 债务削减/坏账损失特有内容 -->
            <!-- <block wx:else>
              <view class="description" style="margin-top: 10px;">
                {{brtabid == 1 ? '债务削减' : '坏账损失'}}是指{{brtabid == 1 ? '借入方' : '借出方'}}无法偿还债务，需要进行账务处理的情况。
              </view>
              <view class="description">
                此操作将直接减少{{brtabid == 1 ? '借入账户' : '借出账户'}}的债务金额，不涉及资产账户的变动。
              </view>
            </block> -->
          </view>
        </view>
      </swiper-item>
      <!-- tab4 -->
      <swiper-item>
        <view class="reimbursement swiper-item-content">
          <view class="RBcard">
            <!-- 第一列 -->
            <view class="RBheader">
              <view>报销收入</view>
              <view>帮助</view>
            </view>
            <!-- 第二列 -->
            <view class="rbTwo">
              <input class="rbInput" type="number" placeholder="报销金额" />
            </view>
            <!-- 第三列 -->
            <view class="RBtime">
              <view>时间</view>
              <view class="rbtimeInfo">
                <view>2025年4月23日</view>
                <view class="rbicon">
                  <image src="/static/icon/arrDown.png" mode="" />
                </view>
              </view>
            </view>
            <!-- 4 -->
            <view class="rbtimeInfo">
              <view>
                <view style="color: #000;">不计入收支</view>
                <view>收支或支出不算这笔账单的金额</view>
              </view>
              <view>
                <switch checked="" bindchange="" />
              </view>
            </view>
            <!-- 5 -->
            <view class="rbTwo">
              <input class="rbInput" type="text" placeholder="备注" />
            </view>
          </view>
          <view class="RBcard">
            <!-- 第一列 -->
            <view class="RBheader">
              <view>收款账户</view>
            </view>
            <!-- 第二列 -->
            <view class="rbTwo">
              <image src="/static/icon/card.png" mode="" />
              <input class="rbInput" type="number" placeholder="入款账户" placeholder-style="padding-left:30px;" />
            </view>
          </view>
          <!-- 3，4 -->
          <view class="RBcard">
            <!-- 第一列 -->
            <view class="RBheader">
              <view>报销收入</view>
              <view class="select">
                <image src="/static/icon/card.png" mode="" />
                选取
              </view>
            </view>
            <!-- 第二列 -->
            <view class="" style="text-align: center;color: #707070;">请选择报销账户</view>
          </view>
          <view class="RBcard">
            <!-- 第一列 -->
            <view class="RBheader">
              <view>报销账单</view>
              <view>选取</view>
            </view>
            <!-- 第二列 -->
            <view class="" style="text-align: center;color: #707070;">请选择需要报销的账单</view>
          </view>
          <!-- 账本 -->
          <view class="RBcard">
            <!-- 第一列 -->
            <view class="RBheader" style="padding: 0px 0 0px 10px;">
              <view>账本</view>
              <view class="zbIcon" style="background-color: transparent;padding: 5px 0 5px 10px;">
                <image src="/static/icon/hg.png" mode="" />
              </view>
            </view>
            <view class="zbinfo">
              <view class="avator">
                <image src="/static/icon/avator.png" mode="" />
              </view>
              <view class="zbinfo" style="grid-template-columns: 1fr auto;">
                <view>
                  <view>账本</view>
                  <view style="color: #707070;font-size: 14px;">默认账本</view>
                </view>
                <view class="arrRIght">
                  <image src="/static/icon/arrDown.png" mode="" />
                </view>
              </view>
            </view>
          </view>
        </view>
      </swiper-item>
      <!-- tab5 -->
      <swiper-item>
        <view class="reimbursement swiper-item-content">
          <view class="RBcard">
            <!-- 第一列 -->
            <view class="RBheader">
              <view>提示</view>
              <view>帮助</view>
            </view>
            <!-- 第二列 -->
            <view class="rbTwo">
              <view class="description">
                <view>可将购买的物品进行退款或多次退款，也可作为AA账单支付使用~</view>
                <view>支持:</view>
                <view>普通用户:支持单笔账单退款多次;</view>
                <view>特别用户:支持单笔账单退款多次，支持单次退款多笔账单;</view>
              </view>
            </view>
          </view>
          <view class="RBcard">
            <!-- 第一列 -->
            <view class="RBheader">
              <view>原账单</view>
              <view class="select">
                <image src="/static/icon/card.png" mode="" />
                选取
              </view>
            </view>
            <!-- 第二列 -->
            <view class="rbTwo">
              <!-- <image src="/static/icon/card.png" mode="" />
              <input class="rbInput" type="number" placeholder="入款账户" placeholder-style="padding-left:30px;" /> -->
              xxxxx
            </view>
          </view>
          <!-- 3，4 -->
          <view class="RBcard">
            <!-- 第一列 -->
            <view class="RBheader">
              <view>报销收入</view>
              <view class="select">
                <image src="/static/icon/card.png" mode="" />
                选取
              </view>
            </view>
            <!-- 第二列 -->
            <view class="" style="text-align: center;color: #707070;">请选择报销账户</view>
          </view>
          <view class="RBcard">
            <!-- 第一列 -->
            <view class="RBheader">
              <view>报销账单</view>
              <view>选取</view>
            </view>
            <!-- 第二列 -->
            <view class="" style="text-align: center;color: #707070;">请选择需要报销的账单</view>
          </view>
          <!-- 账本 -->
          <view class="RBcard">
            <!-- 第一列 -->
            <view class="RBheader" style="padding: 0px 0 0px 10px;">
              <view>账本</view>
              <view class="zbIcon" style="background-color: transparent;padding: 5px 0 5px 10px;">
                <image src="/static/icon/hg.png" mode="" />
              </view>
            </view>
            <view class="zbinfo">
              <view class="avator">
                <image src="/static/icon/avator.png" mode="" />
              </view>
              <view class="zbinfo" style="grid-template-columns: 1fr auto;">
                <view>
                  <view>账本</view>
                  <view style="color: #707070;font-size: 14px;">默认账本</view>
                </view>
                <view class="arrRIght">
                  <image src="/static/icon/arrDown.png" mode="" />
                </view>
              </view>
            </view>
          </view>
        </view>
      </swiper-item>
    </block>
  </swiper>
  <!-- 这里不需要额外的视图，swiper中已经包含了所有内容 -->
  <!-- 底部空间 - 仅在显示计算器时添加 -->
  <!-- <view style="height: {{showCalculator ? '300px' : '0'}}"></view> -->
  <!-- 计算器组件 - 根据showCalculator状态显示或隐藏 -->
  <calculator wx:if="{{showCalculator}}" currentTab="{{currentMouds}}"></calculator>
  <!-- 分类选择器组件 -->
  <categorySelector show="{{showCategorySelector}}" mode="{{selectorMode}}" categories="{{categories}}" themeColor="{{selectedColor}}" showSettings="{{false}}" bind:modeChange="handleSelectorModeChange" bind:select="handleCategorySelect" bind:add="handleAddCategory" bind:close="closeCategorySelector" bind:settings="handleSettings"></categorySelector>
  <!-- 账户选择器组件 -->
  <accountSelector show="{{showAccountSelector}}" mode="{{accountSelectorMode}}" accounts="{{accounts}}" themeColor="{{selectedColor}}" showSettings="{{true}}" selectedAccountId="{{currentAccountType === 'from' ? currentAccount.id : (currentAccountType === 'to' ? currentAccount2.id : 0)}}" bind:modeChange="handleAccountSelectorModeChange" bind:select="handleAccountSelect" bind:add="handleAddAccount" bind:close="closeAccountSelector" bind:settings="handleAccountSettings" bind:refresh="handleRefreshAccounts"></accountSelector>
  <!-- 手续费计算器组件 -->
  <feeCalculator show="{{showFeeCalculator}}" themeColor="{{selectedColor}}" initialFee="{{feeData.fee}}" initialDiscount="{{feeData.discount}}" initialExtraFee="{{feeData.extraFee}}" bind:close="closeFeeCalculator" bind:save="handleFeeSave"></feeCalculator>
  <!-- 使用 t-message 组件 -->
  <t-message id="t-message" align="center" style="background-color: #********;border-radius: 80px; text-align: center;  font-size: 14px; padding: 10px 20px;" />
</view>