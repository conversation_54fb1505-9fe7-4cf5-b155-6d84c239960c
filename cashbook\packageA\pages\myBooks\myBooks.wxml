<!-- packageA/pages/myBooks/myBooks.wxml -->
<view class="container">
  <!-- 错误提示条 - 改为黄底黑字居中显示 -->
  <view class="error-tip" wx:if="{{showErrorTip}}">
    类型不可为空
  </view>

  <!-- 顶部导航 -->
  <!-- <view class="nav-header" style="height: {{menuButtonInfo.height}}px;margin-top: {{menuButtonInfo.top}}px;">
    <view class="nav-back" bindtap="goBack">
      <van-icon name="arrow-left" size="40rpx" />
    </view>
    <view class="nav-title">我的账本</view>
    <view class="nav-more" bindtap="showMoreOptions"></view>
  </view> -->
  <!-- 账本列表 - 改为网格布局 -->
  <view class="book-grid">
    <!-- 非会员只显示一个示例账本，会员显示全部账本 -->
    <view class="book-item" wx:for="{{isVip ? bookList : [demoBook]}}" wx:key="id" bindtap="{{isVip ? 'selectBook' : 'showVipTip'}}" data-id="{{item.id}}">
      <view class="book-card" style="background-color: {{item.bgColor}}">
        <!-- <view class="book-label" wx:if="{{item.isDefault}}">默认账本</view> -->
        <view class="book-content">
          <text class="book-name">{{item.name}}</text>
        </view>
        <image class="book-bg" src="{{item.image}}" mode="aspectFill" wx:if="{{item.image}}"></image>
      </view>
    </view>
  </view>
  <!-- 添加账本按钮 - 仅会员可见 -->
  <view class="add-book-btn" style="background-color: {{selectedColor}};" bindtap="addNewBook" wx:if="{{isVip}}">
    添加账本
  </view>
  <!-- "更多"按钮 - 移到底部 -->
  <view class="more-btn" bindtap="showMoreOptions">更多</view>
  <!-- 会员提示条 - 非会员显示 -->
  <view class="vip-tip-bar" wx:if="{{!isVip}}">
    <view class="vip-tip-text">
      <view>当前为示例</view>
      <view>请升级到会员再使用此功能~</view>
    </view>
    <view class="upgrade-btn" style="background-color: {{selectedColor}};" bindtap="upgradeToVip">
      升级会员
    </view>
  </view>
  <!-- 添加账本弹层 - 使用自定义组件 -->
  <custom-popup visible="{{showAddBookPopup}}" closeButtonPosition="left" title="添加账本" position="bottom" bind:close="closeAddBookPopup">
    <view class="popup-container">
      <scroll-view scroll-y="true" class="add-book-popup">
        <view class="book-cover">
          <image src="{{bookCoverImage || 'http://www.youcai.com/uploads/20250515/289b4a0c8b7de41b385f5dc285f174f2.jpg'}}" mode="aspectFill" class="cover-image" />
          <view class="book-cover-overlay" bindtap="refreshBookCover">
            <view style="display: flex;align-items: center;justify-content: center;">
              <view class="refresh-icon">
                <image src="/static/icon/refresh.png" mode="aspectFit" />
              </view>
              <view class="cover-text">账本封面</view>
            </view>
          </view>
          <!-- <view class="cover-refresh" bindtap="refreshBookCover">
            <image src="/static/icon/refresh.png" mode="aspectFit" />
          </view> -->
        </view>
        <!-- 分类初始化 -->
        <view class="category-init" bindtap="initCategory">
          <view class="init-icon">
            <image src="http://www.youcai.com/uploads/20250521/dd7091ec2bd62f9cd48ea478986958ad.png" mode="aspectFit" />
          </view>
          <view class="init-content">
            <view class="init-text"
              >{{selectedCategoryName || '分类初始化'}}
              <!-- <text class="required-mark" wx:if="{{!selectedCategoryId}}">*</text> -->
            </view>
            <view class="init-desc">{{selectedCategoryDesc || '内含不同账单分类组合'}}</view>
          </view>
          <view class="init-arrow"><van-icon name="arrow"/></view>
        </view>
        <view class="book-form">
          <view class="form-item">
            <view class="input-container">
              <input
                type="text"
                class="book-input {{inputFocusStates.name ? 'focus' : ''}}"
                bindinput="onBookNameInput"
                value="{{bookName}}"
                bindfocus="onInputFocus"
                bindblur="onInputBlur"
                data-field="name"
                placeholder=""
              />
              <view
                class="input-label {{bookName ? 'has-value' : ''}} {{inputFocusStates.name ? 'focus' : ''}}"
                style="{{(inputFocusStates.name || bookName) ? 'color:' + selectedColor + ';' : ''}}"
                >名称</view
              >
              <view class="input-placeholder {{inputFocusStates.name && !bookName ? 'show' : ''}}">请输入名称</view>
            </view>
          </view>
          <view class="form-item">
            <view class="input-container">
              <input
                type="text"
                class="book-input {{inputFocusStates.remark ? 'focus' : ''}}"
                bindinput="onBookRemarkInput"
                value="{{bookRemark}}"
                bindfocus="onInputFocus"
                bindblur="onInputBlur"
                data-field="remark"
                placeholder=""
              />
              <view
                class="input-label {{bookRemark ? 'has-value' : ''}} {{inputFocusStates.remark ? 'focus' : ''}}"
                style="{{(inputFocusStates.remark || bookRemark) ? 'color:' + selectedColor + ';' : ''}}"
                >备注</view
              >
              <view class="input-placeholder {{inputFocusStates.remark && !bookRemark ? 'show' : ''}}">请输入备注</view>
            </view>
          </view>
        </view>
        <!-- 占位区域，确保内容可以滚动到底部按钮上方 -->
        <!-- <view class="bottom-placeholder"></view> -->
      </scroll-view>
      <view class="save-btn-container">
        <view class="save-btn" bindtap="validateAndSaveNewBook">保存</view>
      </view>
    </view>
  </custom-popup>
  <!-- "更多"弹出层 - 使用自定义组件 -->
  <custom-popup visible="{{showMorePopup}}" closeButtonPosition="left" title="更多" position="bottom" bind:close="onMorePopupClose">
    <view class="more-menu-list">
      <!-- 账本排序 -->
      <view class="more-menu-item" bindtap="handleBookSort">
        <view class="menu-item-icon">
          <van-icon name="bars" />
        </view>
        <view class="menu-item-content">
          <view class="menu-item-title">账本排序</view>
          <view class="menu-item-desc">给账本排序</view>
        </view>
        <view class="menu-item-arrow">
          <van-icon name="arrow" />
        </view>
      </view>
      <!-- 封存账本列表 -->
      <view class="more-menu-item" bindtap="handleArchivedBooks">
        <view class="menu-item-icon">
          <van-icon name="clock-o" />
        </view>
        <view class="menu-item-content">
          <view class="menu-item-title">封存账本列表</view>
          <view class="menu-item-desc">不常用的账本</view>
        </view>
        <view class="menu-item-arrow">
          <van-icon name="arrow" />
        </view>
      </view>
      <!-- 默认账本隐藏 -->
      <view class="more-menu-item">
        <view class="menu-item-icon">
          <van-icon name="delete-o" />
        </view>
        <view class="menu-item-content">
          <view class="menu-item-title">默认账本隐藏</view>
          <view class="menu-item-desc">隐藏后，在所有页面【默认账本】处于隐藏状态</view>
        </view>
        <view class="menu-item-switch">
          <van-switch checked="{{ hideDefaultBook }}" bind:change="onHideDefaultBookChange" size="24px" />
        </view>
      </view>
      <!-- 常见问题 -->
      <view class="more-menu-item" bindtap="handleFAQ">
        <view class="menu-item-icon">
          <van-icon name="question-o" />
        </view>
        <view class="menu-item-content">
          <view class="menu-item-title">常见问题</view>
          <view class="menu-item-desc">查看账本解答</view>
        </view>
        <view class="menu-item-arrow">
          <van-icon name="arrow" />
        </view>
      </view>
    </view>
  </custom-popup>
  <!-- 分类初始化弹层 -->
  <custom-popup visible="{{showCategoryInitPopup}}" closeButtonPosition="left" title="请选择分类初始化" position="bottom" bind:close="closeCategoryInitPopup">
    <view class="more-menu-list">
      <!-- 分类选项列表 -->
      <view class="more-menu-item" wx:for="{{categoryList}}" wx:key="id" bindtap="selectCategory" data-id="{{item.id}}">
        <view class="menu-item-content">
          <view class="menu-item-title">{{item.name}}</view>
          <view class="menu-item-desc">{{item.desc}}</view>
        </view>
        <view class="menu-item-arrow">
          <van-icon name="arrow" />
        </view>
      </view>
    </view>
  </custom-popup>
  <!-- 封面选择弹层 -->
  <custom-popup
    visible="{{showCoverSelectPopup}}"
    closeButtonPosition="left"
    title="选择封面"
    position="bottom"
    bind:close="closeCoverSelectPopup"
    showCustomUpload="{{true}}"
    customUploadText="自定义"
    bind:customUpload="uploadCustomCover"
  >
    <view class="cover-select-container">
      <view class="cover-grid">
        <view class="cover-item" wx:for="{{coverList}}" wx:key="index" bindtap="selectCover" data-url="{{item.url}}">
          <image src="{{item.url}}" mode="aspectFill" class="cover-thumb" />
        </view>
      </view>
    </view>
  </custom-popup>
  <book-selector
    show="{{showBookSelector}}"
    bookList="{{bookList}}"
    showSelectionIndicator="{{false}}"
    showSelectModeToggle="{{false}}"
    showAllBooksOption="{{false}}"
    showAddButton="{{false}}"
    showViewModeToggle="{{true}}"
    showReloadOption="{{true}}"
    title="选择账本"
    bind:select="onBookSelect"
    bind:close="closeBookSelector"
  />
</view>
