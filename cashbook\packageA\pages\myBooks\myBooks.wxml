<!-- packageA/pages/myBooks/myBooks.wxml -->
<view class="container">
  <!-- 顶部导航 -->
  <view class="nav-header">
    <view class="nav-back" bindtap="goBack">
      <image src="/static/icon/back.png" mode="aspectFit" />
    </view>
    <view class="nav-title">我的账本</view>
    <view class="nav-more">更多</view>
  </view>

  <!-- 账本列表 -->
  <view class="book-list">
    <view class="book-item" 
          wx:for="{{bookList}}" 
          wx:key="id" 
          bindtap="selectBook" 
          data-id="{{item.id}}">
      <view class="book-card">
        <view class="book-content">
          <view class="book-abbr">{{item.name.substr(0,2)}}</view>
          <view class="book-name">{{item.name}}</view>
        </view>
        <image class="book-image" src="{{item.image}}" mode="aspectFill" wx:if="{{item.image}}"></image>
      </view>
    </view>
  </view>

  <!-- 添加账本按钮 - 仅会员可见 -->
  <view class="add-book-btn" bindtap="addNewBook" wx:if="{{isVip}}">
    添加账本
  </view>

  <!-- 会员提示条 - 非会员显示 -->
  <view class="vip-tip-bar" wx:if="{{!isVip}}">
    <view class="vip-tip-text">当前为示例，请开通会员再使用此功能~</view>
    <view class="upgrade-btn" bindtap="upgradeToVip">升级会员</view>
  </view>
  
  <!-- 添加账本弹层 -->
  <t-popup visible="{{showAddBookPopup}}" bind:visible-change="onAddBookPopupChange" placement="bottom">
    <view class="add-book-popup">
      <view class="popup-close" bindtap="closeAddBookPopup">
        <image src="/static/icon/close.png" mode="aspectFit" />
      </view>
      
      <view class="popup-title">添加账本</view>
      
      <view class="book-cover">
        <image src="{{bookCoverImage || '/static/images/shanghai.jpg'}}" mode="aspectFill" class="cover-image" />
        <view class="cover-refresh" bindtap="refreshBookCover">
          <image src="/static/icon/refresh.png" mode="aspectFit" />
        </view>
      </view>
      
      <view class="category-init">
        <view class="init-icon">
          <image src="/static/icon/category.png" mode="aspectFit" />
        </view>
        <view class="init-text">分类初始化</view>
        <view class="init-arrow">
          <image src="/static/icon/arrow-right.png" mode="aspectFit" />
        </view>
      </view>
      
      <view class="book-form">
        <view class="form-item">
          <input type="text" placeholder="名称" class="book-input" bindinput="onBookNameInput" value="{{bookName}}" />
        </view>
        <view class="form-item">
          <input type="text" placeholder="备注" class="book-input" bindinput="onBookRemarkInput" value="{{bookRemark}}" />
        </view>
      </view>
      
      <view class="save-btn" bindtap="saveNewBook">保存</view>
    </view>
  </t-popup>
</view>