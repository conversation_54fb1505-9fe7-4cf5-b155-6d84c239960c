  /* 固定头部区域 */
  .fixed-header {
    position: fixed;
    top: 0;
    left: 0;
    width: 100%;
    z-index: 99;
    background-color: #ffffff;
    box-shadow: 0 2rpx 10rpx rgba(0, 0, 0, 0.05);
    
    /* 顶部导航栏样式 */
    .navigation-bar {
      display: flex;
      flex-direction: row;
      align-items: center;
      justify-content: space-between;
      padding: 0 30rpx;
      background-color: #ffffff;
      height: 88rpx;
      width: 100%;
      box-sizing: border-box;
      
      .nav-left {
        width: 60rpx;
        height: 60rpx;
        display: flex;
        align-items: center;
        justify-content: flex-start;
      }
      
      .nav-title {
        font-size: 36rpx;
        font-weight: 500;
        color: #000000;
        flex: 1;
        text-align: center;
      }
      
      .nav-right {
        width: 60rpx;
        height: 60rpx;
        visibility: hidden; // 保持占位但不可见
      }
    }
    
    /* 搜索框样式 */
    .search-container {
      padding: 20rpx 30rpx;
      background-color: #ffffff;
      display: flex;
      align-items: center;
      justify-content: space-between;
      
      .search-box {
        display: flex;
        align-items: center;
        background-color: #f5f5f5;
        border-radius: 36rpx;
        padding: 16rpx 24rpx;
        flex: 1;
        margin-right: 20rpx;
        
        .search-input {
          flex: 1;
          height: 40rpx;
          font-size: 28rpx;
          color: #333333;
          margin-left: 12rpx;
        }
        
        .placeholder {
          color: #bbbbbb;
        }
      }
      
      .custom-button {
        font-size: 30rpx;
        color: #333333;
        padding: 10rpx 0;
        white-space: nowrap;
      }
    }
  }


/* 银行选择器页面样式 */
.bank-selector-container {
  display: flex;
  flex-direction: column;
  height: 100vh;
  background-color: #f5f5f5;
  box-sizing: border-box;
  /* padding-top动态设置，由JS计算 */
  
  /* 顶部状态栏样式 */
  .status-bar {
    background-color: #ffffff;
    width: 100%;
    position: fixed;
    top: 0;
    left: 0;
    z-index: 100;
  }
  

  
  /* 银行列表区域样式 */
  .bank-list-container {
    flex: 1;
    background-color: #f5f5f5;
    height: 100%;
    
    /* 加载中状态 */
    .loading-container {
      display: flex;
      flex-direction: column;
      align-items: center;
      justify-content: center;
      padding: 100rpx 0;
      
      .loading-text {
        font-size: 28rpx;
        color: #999999;
        margin-top: 20rpx;
      }
    }
    
    /* 无数据状态 */
    .empty-container {
      display: flex;
      flex-direction: column;
      align-items: center;
      justify-content: center;
      padding: 100rpx 0;
      
      .empty-text {
        font-size: 28rpx;
        color: #999999;
      }
    }
    
    /* 字母索引标题 */
    .index-title {
      padding: 10rpx 30rpx;
      font-size: 28rpx;
      color: #999999;
      background-color: #f5f5f5;
      width: 100%;
      box-sizing: border-box;
      z-index: 2;
    }
    
    /* 银行项样式 */
    .bank-item {
      display: flex;
      align-items: center;
      padding: 24rpx 30rpx;
      background-color: #ffffff;
      border-bottom: 1rpx solid #f5f5f5;
      margin-bottom: 2rpx;
      
      .bank-icon {
        width: 80rpx;
        height: 80rpx;
        border-radius: 40rpx;
        overflow: hidden;
        margin-right: 20rpx;
        display: flex;
        align-items: center;
        justify-content: center;
        background-color: #ffffff;
        
        image {
          width: 100%;
          height: 100%;
        }
      }
      
      .bank-name {
        font-size: 32rpx;
        color: #333333;
      }
    }
  }
  
  /* 右侧字母索引样式 */
  .letter-index {
    position: fixed;
    right: 20rpx;
    top: 50%;
    transform: translateY(-50%);
    display: flex;
    flex-direction: column;
    align-items: center;
    z-index: 10;
    
    .letter-item {
      width: 40rpx;
      height: 40rpx;
      font-size: 24rpx;
      color: #666666;
      display: flex;
      align-items: center;
      justify-content: center;
      margin: 2rpx 0;
      
      &.active {
        color: #4CAF50;
        font-weight: bold;
      }
    }
  }
}

/* 自定义图片名称弹窗样式 */
.custom-image-form {
  padding: 20rpx 30rpx 40rpx;
  
  .form-item {
    margin-bottom: 30rpx;
    
    input {
      width: 100%;
      height: 90rpx;
      background-color: #f5f5f5;
      border-radius: 45rpx;
      padding: 0 30rpx;
      font-size: 28rpx;
      box-sizing: border-box;
    }
  }
  
  .save-button {
    width: 100%;
    height: 90rpx;
    background-color: #bcdfc1;
    color: #000000;
    font-size: 32rpx;
    border-radius: 45rpx;
    display: flex;
    align-items: center;
    justify-content: center;
    margin-top: 30rpx;
    box-shadow: 0 2rpx 8rpx rgba(0, 0, 0, 0.05);
  }
} 