<!--packageA/pages/bookSort/bookSort.wxml-->
<view class="container">
  <!-- 标题 -->
  <view class="title">长按拖动排序哦</view>

  <!-- 可拖拽列表 -->
  <scroll-view class="book-scroll-view" scroll-y="{{!dragging}}">
    <view class="book-list">
      <view
        class="book-item {{currentIndex === index ? 'moving' : ''}}"
        wx:for="{{bookList}}"
        wx:key="id"
        bind:longpress="onLongPress"
        catch:touchmove="{{dragging && currentIndex === index ? 'touchMove' : ''}}"
        catch:touchend="{{dragging ? 'touchEnd' : ''}}"
        data-index="{{index}}"
        data-id="{{item.id}}"
        style="{{currentIndex === index ? 'z-index:100;transform: translateY(' + moveY + 'px);' : 'z-index:1;'}}"
      >
        <view class="book-icon">
          <image wx:if="{{item.image}}" src="{{item.image}}" mode="aspectFill"></image>
          <view wx:else style="background-color: {{item.bgColor || '#A5D6B7'}}"></view>
        </view>
        <view class="book-name">{{item.name}}</view>
      </view>
    </view>
  </scroll-view>

  <!-- 保存按钮 -->
  <view class="save-btn" bindtap="saveSort" style="background-color: {{selectedColor}};">保存</view>
</view>
