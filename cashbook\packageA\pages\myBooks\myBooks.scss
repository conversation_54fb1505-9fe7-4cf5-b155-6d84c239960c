/* packageA/pages/myBooks/myBooks.scss */
.container {
  // background-color: #f8f8f8;
  min-height: 100vh;
  position: relative;
  padding-bottom: 180rpx;
  /* Add padding to the bottom of container */
}

.nav-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 0 10px;

  .nav-back {
    width: 60rpx;
    height: 60rpx;
    display: flex;
    align-items: center;

    image {
      width: 40rpx;
      height: 40rpx;
    }
  }

  .nav-title {
    font-size: 36rpx;
    font-weight: 500;
  }

  .nav-more {
    font-size: 28rpx;
    color: #666;
    display: flex;
    align-items: flex-end;
    height: 100%;
    padding-bottom: 10rpx;
    width: 28px;
  }
}

/* 改为网格布局 */
.book-grid {
  margin-top: 50rpx;
  display: flex;
  flex-wrap: wrap;
  justify-content: space-between;
  padding: 0 10rpx;
  height: auto;
  /* Changed from 100% to auto to allow proper margin spacing */
  width: 100%;
  box-sizing: border-box;

  .book-item {
    width: 48%;
    margin-bottom: 24rpx;

    .book-card {
      position: relative;
      height: 220rpx;
      border-radius: 50rpx;
      overflow: hidden;
      display: flex;
      flex-direction: column;
      justify-content: center;
      padding: 20rpx;
      box-shadow: 0 4rpx 12rpx rgba(0, 0, 0, 0.1);
      background-color: #A5D6B7;
      /* 默认浅绿色背景 */

      .book-content {
        position: absolute;
        z-index: 2;
        padding: 4rpx 20rpx;
        top: 20rpx;
        left: 20rpx;
        background-color: #faf5f9;
        border-radius: 50rpx;
        display: flex;
        align-items: center;
        justify-content: center;
        min-height: 36rpx;
      }

      .book-label {
        display: inline-block;
        font-size: 24rpx;
        color: #fff;
        background-color: rgba(255, 255, 255, 0.3);
        padding: 4rpx 16rpx;
        border-radius: 20rpx;
        margin-bottom: 20rpx;
      }

      .book-name {
        font-size: 12px;
        color: #000000;
        font-weight: 500;
        letter-spacing: 2rpx;
        // text-align: center;
        line-height: 1;
      }

      .book-bg {
        position: absolute;
        top: 0;
        left: 0;
        width: 100%;
        height: 100%;
        z-index: 1;
      }
    }
  }
}

.add-book-btn {
  position: fixed;
  bottom: 50rpx;
  left: 0;
  right: 0;
  margin: 0 30rpx;
  height: 90rpx;
  background-color: #A5D6B7;
  /* 与账本卡片匹配的按钮颜色 */
  color: #fff;
  font-size: 32rpx;
  display: flex;
  justify-content: center;
  align-items: center;
  border-radius: 45rpx;
  box-shadow: 0 4rpx 12rpx rgba(0, 0, 0, 0.1);
  z-index: 2;
}

.vip-tip-bar {
  position: fixed;
  bottom: 20rpx;
  left: 20rpx;
  right: 20rpx;
  margin: 0;
  padding: 26rpx 30rpx;
  background-color: #333;
  display: flex;
  justify-content: space-between;
  align-items: center;
  border-radius: 40rpx;
  box-shadow: 0 4rpx 10rpx rgba(0, 0, 0, 0.15);
  z-index: 100;

  .vip-tip-text {
    font-size: 28rpx;
    color: #fff;
    flex: 1;
  }

  .upgrade-btn {
    padding: 12rpx 30rpx;
    background-color: #8BC48A;
    color: #fff;
    font-size: 28rpx;
    border-radius: 30rpx;
    margin-left: 20rpx;
  }
}

/* 底部"更多"按钮样式 */
.more-btn {
  position: fixed;
  bottom: 200rpx;
  right: 30rpx;
  background-color: #ffffff;
  color: #666666;
  font-size: 28rpx;
  padding: 16rpx 30rpx;
  border-radius: 30rpx;
  box-shadow: 0 4rpx 12rpx rgba(0, 0, 0, 0.15);
  z-index: 2;
  /* 确保在大多数元素之上，但在vip-tip-bar之下 */
}

/* "更多"菜单列表样式 */
.more-menu-list {
  max-height: calc(80vh - 100rpx);
  overflow-y: auto;

  .more-menu-item {
    display: flex;
    align-items: center;
    padding: 30rpx 40rpx;
    border-bottom: 1px solid #eee;

    &:last-child {
      border-bottom: none;
      margin-bottom: 20px;
      /* Add some space at the bottom for iOS devices */
    }

    .menu-item-icon {
      width: 50rpx;
      height: 50rpx;
      display: flex;
      align-items: center;
      justify-content: center;
      color: #999;
      margin-right: 20rpx;
      font-size: 40rpx;
    }

    .menu-item-content {
      flex: 1;

      .menu-item-title {
        font-size: 30rpx;
        color: #333;
        margin-bottom: 6rpx;
      }

      .menu-item-desc {
        font-size: 24rpx;
        color: #999;
      }
    }

    .menu-item-arrow {
      width: 40rpx;
      height: 40rpx;
      display: flex;
      align-items: center;
      justify-content: center;
      color: #ccc;
    }

    .menu-item-switch {
      margin-left: 20rpx;
    }
  }
}

/* 添加账本弹出层容器 */
.popup-container {
  display: flex;
  flex-direction: column;
  height: 60vh; /* 限制总高度 */
  position: relative;
  border-radius: inherit;
}

/* 添加账本弹层内容样式 */
.add-book-popup {
  padding: 0;
  flex: 1;
  height: calc(100% - 120rpx); /* 减去底部按钮的高度 */
  border-radius: 40rpx; /* 顶部圆角 */
  overflow-y: auto;
  // background-color: #000000;
  box-shadow: 0px 4px 12px rgba(0, 0, 0, 0.1);
  margin-bottom: 30px;

  .book-cover {
    position: relative;
    width: 100%;
    height: 300rpx;
    margin-bottom: 30rpx;
    margin-top: 20rpx;
    border-radius: 40rpx 40rpx 0 0;
    overflow: hidden;
    background-color: #f8f8f8;
    display: flex;
    justify-content: center;
    align-items: center;
    box-shadow: 0 4rpx 12rpx rgba(0, 0, 0, 0.05);

    .cover-image {
      width: 100%;
      height: 100%;
      object-fit: cover;
    }

    .book-cover-overlay {
      position: absolute;
      top: 0;
      left: 0;
      width: 100%;
      height: 100%;
      display: flex;
      flex-direction: column;
      justify-content: center;
      align-items: center;
      background-color: rgba(0, 0, 0, 0.3);
      
      .refresh-icon {
        width: 64rpx;
        height: 64rpx;
        
        image {
          width: 100%;
          height: 100%;
          opacity: 0.9;
        }
      }
      
      .cover-text {
        color: #fff;
        font-size: 36rpx;
        font-weight: 500;
        text-shadow: 0 2rpx 4rpx rgba(0, 0, 0, 0.3);
      }
    }

    .cover-refresh {
      position: absolute;
      right: 20rpx;
      bottom: 20rpx;
      width: 60rpx;
      height: 60rpx;
      background-color: rgba(255, 255, 255, 0.8);
      border-radius: 50%;
      display: flex;
      align-items: center;
      justify-content: center;
      z-index: 10;

      image {
        width: 36rpx;
        height: 36rpx;
      }
    }
  }

  .category-init {
    display: flex;
    align-items: center;
    padding:0 30rpx;

    .init-icon {
      width: 70rpx;
      height: 70rpx;
      margin-right: 20rpx;
      background-color: #f0f0f0;
      border-radius: 50%;
      display: flex;
      align-items: center;
      justify-content: center;

      image {
        width: 60rpx;
        height: 60rpx;
        border-radius: 50%;
      }
    }

    .init-content {
      flex: 1;
      display: flex;
      flex-direction: column;
    }

    .init-text {
      font-size: 34rpx;
      color: #333;
      font-weight: 500;
      line-height: 1.4;
    }

    .init-desc {
      font-size: 26rpx; 
      color: #999;
      margin-top: 6rpx;
    }

    .init-arrow {
      width: 40rpx;
      height: 40rpx;
      display: flex;
      align-items: center;
      justify-content: center;

      image {
        width: 24rpx;
        height: 24rpx;
        opacity: 0.6;
      }
    }
  }

  .book-form {
    margin: 0 30rpx;
    padding-top: 20rpx;

    .form-item {
      padding: 0;
      margin-bottom: 20rpx;
      border-bottom: none;
      position: relative;
      
      .input-container {
        position: relative;
        width: 100%;
      }
      
      .input-label {
        position: absolute;
        left: 40rpx;
        top: 28rpx; /* 输入框高度的中心位置 */
        font-size: 30rpx;
        color: #999;
        transition: all 0.3s ease;
        pointer-events: none; /* 让标签不阻挡输入框的点击 */
        transform-origin: left top;
        z-index: 1;
      }
      
      .input-placeholder {
        position: absolute;
        left: 40rpx;
        top: 28rpx;
        font-size: 30rpx;
        color: #999;
        pointer-events: none;
        opacity: 0;
        transition: opacity 0.2s ease;
      }
      
      .input-placeholder.show {
        opacity: 1;
      }
      
      .input-label.focus, 
      .input-label.has-value {
        top: -16rpx;
        font-size: 28rpx;
        font-weight: 500;
      }

      .book-input {
        width: 100%;
        height: 90rpx;
        font-size: 32rpx;
        color: #333;
        background-color: #f5f5f5;
        border-radius: 45rpx;
        padding: 0 40rpx;
        box-sizing: border-box;
        font-weight: 300;
      }

      &:last-child {
        margin-bottom: 30rpx;
      }
    }
  }
}

/* 底部占位区域，确保内容可以滚动到底部按钮上方 */
.bottom-placeholder {
  height: 40rpx;
}

/* 保存按钮容器 */
.save-btn-container {
  position: sticky;
  bottom: 0;
  left: 0;
  right: 0;
  padding: 20rpx 30rpx 30rpx;
  background-color: #fff;
  border-top: 1rpx solid #f5f5f5;
  z-index: 10;
}

/* 保存按钮样式 */
.save-btn {
  height: 90rpx;
  background-color: #ff6a6a;
  color: #fff;
  font-size: 32rpx;
  display: flex;
  justify-content: center;
  align-items: center;
  border-radius: 45rpx;
}

/* 封面选择弹层样式 */
.cover-select-container {
  padding: 20rpx;
  overflow-y: auto;
}

.cover-grid {
  display: flex;
  flex-wrap: wrap;
  justify-content: space-between;
  padding: 10rpx;
}

.cover-item {
  width: calc(50% - 20rpx);
  height: 180rpx;
  margin-bottom: 20rpx;
  border-radius: 20rpx;
  overflow: hidden;
  position: relative;
  box-shadow: 0 2rpx 8rpx rgba(0, 0, 0, 0.1);
}

.cover-thumb {
  width: 100%;
  height: 100%;
  object-fit: cover;
}

.custom-cover {
  background-color: #f5f5f5;
  display: flex;
  align-items: center;
  justify-content: center;
  border: 2rpx dashed #ddd;
}

.custom-cover-content {
  text-align: center;
  color: #999;
  font-size: 30rpx;
}

/* 错误提示条样式 - 黄底黑字 */
.error-tip {
  position: fixed;
  top: 50%;
  left: 50%;
  transform: translate(-50%, -50%);
  background-color: #FFEB3B;
  color: #000;
  padding: 20rpx 40rpx;
  font-size: 28rpx;
  border-radius: 30rpx;
  text-align: center;
  z-index: 10000;
  box-shadow: 0 4rpx 12rpx rgba(0, 0, 0, 0.15);
  min-width: 300rpx;
  max-width: 80%;
  font-weight: 500;
}

.close-error {
  display: none; /* 隐藏关闭按钮 */
}

/* 必填标记 */
.required-mark {
  color: #ff4d4f;
  margin-left: 6rpx;
  font-size: 24rpx;
}

/* 分类必选样式 */
.category-required {
  border: 1px solid #ff4d4f;
  border-radius: 8rpx;
  animation: shake 0.5s ease-in-out;
}

@keyframes shake {
  0%, 100% {
    transform: translateX(0);
  }
  10%, 30%, 50%, 70%, 90% {
    transform: translateX(-5rpx);
  }
  20%, 40%, 60%, 80% {
    transform: translateX(5rpx);
  }
}

/* 添加其他样式... */ 