/* packageA/pages/myBooks/myBooks.scss */
.container {
  padding: 20rpx;
  background-color: #f8f8f8;
  min-height: 100vh;
}

.nav-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 20rpx 0;
  margin-bottom: 30rpx;

  .nav-back {
    width: 60rpx;
    height: 60rpx;
    display: flex;
    align-items: center;

    image {
      width: 40rpx;
      height: 40rpx;
    }
  }

  .nav-title {
    font-size: 36rpx;
    font-weight: 500;
  }

  .nav-more {
    font-size: 28rpx;
    color: #666;
  }
}

.book-list {
  display: flex;
  flex-wrap: wrap;
  justify-content: space-between;

  .book-item {
    width: 48%;
    margin-bottom: 30rpx;

    .book-card {
      position: relative;
      height: 180rpx;
      border-radius: 16rpx;
      overflow: hidden;
      display: flex;
      flex-direction: column;
      justify-content: center;
      padding: 20rpx;
      box-shadow: 0 4rpx 12rpx rgba(0, 0, 0, 0.05);

      // 默认背景色（绿色）
      background-color: #a5ddb9;

      // 奇数项背景色（粉色）
      &:nth-child(even) {
        background-color: #ffb6c1;
      }

      .book-content {
        position: relative;
        z-index: 2;
      }

      .book-abbr {
        font-size: 32rpx;
        color: #fff;
        font-weight: 500;
        margin-bottom: 10rpx;
      }

      .book-name {
        font-size: 28rpx;
        color: #fff;
        margin-bottom: 10rpx;
      }

      .book-image {
        position: absolute;
        top: 0;
        left: 0;
        width: 100%;
        height: 100%;
        z-index: 1;
        opacity: 0.8;
      }
    }
  }
}

.add-book-btn {
  position: fixed;
  bottom: 0;
  left: 0;
  right: 0;
  margin: 20rpx 30rpx;

  // width: 100%;
  height: 90rpx;
  background-color: #a5ddb9;
  color: #fff;
  font-size: 32rpx;
  display: flex;
  justify-content: center;
  align-items: center;
  border-radius: 45rpx;
  margin-top: 20rpx;
  box-shadow: 0 4rpx 12rpx rgba(0, 0, 0, 0.1);
}

.vip-tip-bar {
  margin-top: 30rpx;
  padding: 20rpx;
  background-color: #fff8e1;
  border-radius: 12rpx;
  display: flex;
  justify-content: space-between;
  align-items: center;

  .vip-tip-text {
    font-size: 28rpx;
    color: #ff9800;
  }

  .upgrade-btn {
    padding: 10rpx 30rpx;
    background-color: #ff9800;
    color: #fff;
    font-size: 26rpx;
    border-radius: 30rpx;
  }
}

/* 添加账本弹层样式 */
.add-book-popup {
  width: 650rpx;
  background-color: #fff;
  border-radius: 24rpx;
  overflow: hidden;
  position: relative;
  padding-bottom: 30rpx;
  
  .popup-close {
    position: absolute;
    left: 30rpx;
    top: 30rpx;
    width: 40rpx;
    height: 40rpx;
    z-index: 10;
    
    image {
      width: 100%;
      height: 100%;
    }
  }
  
  .popup-title {
    text-align: center;
    font-size: 34rpx;
    font-weight: 500;
    padding: 30rpx 0;
  }
  
  .book-cover {
    position: relative;
    width: 100%;
    height: 300rpx;
    
    .cover-image {
      width: 100%;
      height: 100%;
      object-fit: cover;
    }
    
    .cover-refresh {
      position: absolute;
      right: 20rpx;
      bottom: 20rpx;
      width: 60rpx;
      height: 60rpx;
      background-color: rgba(255, 255, 255, 0.8);
      border-radius: 50%;
      display: flex;
      align-items: center;
      justify-content: center;
      
      image {
        width: 36rpx;
        height: 36rpx;
      }
    }
  }
  
  .category-init {
    display: flex;
    align-items: center;
    padding: 30rpx;
    border-bottom: 1rpx solid #f5f5f5;
    
    .init-icon {
      width: 40rpx;
      height: 40rpx;
      margin-right: 20rpx;
      
      image {
        width: 100%;
        height: 100%;
      }
    }
    
    .init-text {
      flex: 1;
      font-size: 30rpx;
      color: #333;
    }
    
    .init-arrow {
      width: 30rpx;
      height: 30rpx;
      
      image {
        width: 100%;
        height: 100%;
      }
    }
  }
  
  .book-form {
    padding: 0 30rpx;
    
    .form-item {
      padding: 20rpx 0;
      border-bottom: 1rpx solid #f5f5f5;
      
      .book-input {
        width: 100%;
        height: 80rpx;
        font-size: 30rpx;
      }
    }
  }
  
  .save-btn {
    margin: 40rpx 30rpx 0;
    height: 90rpx;
    background-color: #a5ddb9;
    color: #fff;
    font-size: 32rpx;
    display: flex;
    justify-content: center;
    align-items: center;
    border-radius: 45rpx;
  }
}