/* packageA/pages/bookSetting/incomeCategory/sortCategory/sortCategory.scss */

.sort-container {
  min-height: 100vh;
  background-color: #f8f8f8;
  padding-bottom: 120rpx;
}

/* 顶部导航栏 */
.nav-bar {
  display: flex;
  align-items: center;
  position: fixed;
  top: 0;
  left: 0;
  right: 0;
  background-color: #fff;
  padding: 0 30rpx;
  z-index: 100;
  box-shadow: 0 2rpx 10rpx rgba(0, 0, 0, 0.05);

  .back-icon {
    padding: 10rpx;
  }

  .page-title {
    flex: 1;
    text-align: center;
    font-size: 34rpx;
    font-weight: 500;
    color: #333;
  }
}

/* 提示文本 */
.tip-text {
  margin-top: calc(44px + var(--status-bar-height));
  padding: 30rpx;
  font-size: 28rpx;
  color: #999;
  text-align: center;
}

/* 分类列表 */
.category-list {
  padding: 0 20rpx;

  .category-item {
    width: 100%;
    position: relative;
    z-index: 10;
    will-change: transform;
    margin-bottom: 10rpx;

    .item-content {
      transition: transform 0.2s ease, box-shadow 0.2s ease, background-color 0.2s ease;
    }

    &.dragging {
      z-index: 100;

      .item-content {
        background-color: #fff;
        transform: scale(1.02);
        box-shadow: 0 8rpx 20rpx rgba(0, 0, 0, 0.15);
        border: 1rpx solid #59b5dd;
      }
    }
  }
}

.item-content {
  display: flex;
  align-items: center;
  padding: 20rpx;
  background-color: #fff;
  border-radius: 12rpx;
  box-shadow: 0 2rpx 8rpx rgba(0, 0, 0, 0.05);

  &.target-position {
    background-color: #f0f9fe;
    border: 1rpx dashed #59b5dd;
    transform: scale(0.98);
    opacity: 0.85;
  }

  .drag-handle {
    padding: 20rpx;
    display: flex;
    align-items: center;
    justify-content: center;
    cursor: grab;
  }

  .category-icon {
    width: 80rpx;
    height: 80rpx;
    border-radius: 35%;
    background-color: #f5f5f5;
    display: flex;
    justify-content: center;
    align-items: center;
    margin-right: 20rpx;
    overflow: hidden;

    image {
      width: 48rpx;
      height: 48rpx;
    }
  }

  .category-name {
    font-size: 30rpx;
    color: #333;
    flex: 1;
    white-space: nowrap;
    overflow: hidden;
    text-overflow: ellipsis;
  }
}

/* 文本图标 */
.text-icon {
  font-size: 32rpx;
  color: #333;
  width: 100%;
  height: 100%;
  display: flex;
  justify-content: center;
  align-items: center;
  font-weight: 500;
  background-color: #e8f4f8;
}

/* emoji图标 */
.emoji-icon {
  font-size: 36rpx;
  width: 100%;
  height: 100%;
  display: flex;
  justify-content: center;
  align-items: center;
  background-color: #fff;
}

/* 保存按钮 */
.save-button {
  position: fixed;
  bottom: 40rpx;
  left: 40rpx;
  right: 40rpx;
  height: 90rpx;
  border-radius: 45rpx;
  background-color: #59b5dd;
  color: #fff;
  display: flex;
  justify-content: center;
  z-index: 100;
  align-items: center;
  font-size: 32rpx;
  font-weight: 500;
  box-shadow: 0 4rpx 12rpx rgba(89, 181, 221, 0.3);
}

/* 添加视觉提示 */
@keyframes pulse {
  0% {
    transform: scale(1);
  }
  50% {
    transform: scale(1.02);
  }
  100% {
    transform: scale(1);
  }
}

.dragging .item-content {
  animation: pulse 2s infinite;
}

/* 位置交换动画 */
@keyframes swap {
  0% {
    transform: translateY(0) scale(1);
  }
  50% {
    transform: translateY(5px) scale(0.98);
  }
  100% {
    transform: translateY(0) scale(1);
  }
}

.item-content.target-position {
  animation: swap 0.5s ease;
}