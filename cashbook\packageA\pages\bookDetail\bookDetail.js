import { getAccountBookDetail, setDefaultBook } from '../../../api/book/index';
const util = require('../../../utils/index.js');
const app = getApp();

Page({
  /**
   * 页面的初始数据
   */
  data: {
    list: [
      {
        isTransfer: true,
        time: '5月8日',
        istoday: '昨天',
        desc: '【资产初始化】初期余额*****************',
        amount: '100.00',
        remark: '人人认可阿斯达啊大大撒阿萨大大111',
        payAccount: '100.00',
        fromAccount: '100.00',
        toAccount: '100.00',
        date: '2023-05-08'
      },
      {
        isTransfer: true,
        time: '5月10日',
        istoday: '今天',
        desc: '【资产初始化】初期余额*****************',
        amount: '************.00',
        remark: '人人认可123',
        payAccount: '************.00',
        fromAccount: '100.00',
        toAccount: '100.00',
        date: '2023-05-10'
      },
      {
        isTransfer: true,
        time: '5月10日',
        istoday: '今天',
        desc: '【资产初始化】初期余额100.0',
        amount: '100.00',
        remark: '豪华地区哦',
        payAccount: '100.00',
        fromAccount: '一二萨比',
        toAccount: '阿克苏',
        date: '2023-05-10'
      },
      {
        isTransfer: true,
        time: '5月10日',
        istoday: '今天',
        desc: '【资产初始化】初期余额100.0',
        amount: '100.00',
        remark: '豪华地区哦123',
        payAccount: '100.00',
        date: '2023-05-10'
      },
      {
        isTransfer: true,
        time: '5月10日',
        istoday: '今天',
        desc: '【资产初始化】初期余额100.0',
        amount: '100.00',
        remark: '豪华地区哦',
        payAccount: '100.00',
        date: '2023-05-10'
      },
      {
        isTransfer: true,
        time: '5月10日',
        istoday: '今天',
        desc: '【资产初始化】初期余额100.0',
        amount: '100.00',
        remark: '豪华地区哦',
        payAccount: '100.00',
        date: '2023-05-10'
      },
      {
        isTransfer: true,
        time: '5月10日',
        istoday: '今天',
        desc: '【资产初始化】初期余额100.0',
        amount: '100.00',
        remark: '豪华地区哦',
        payAccount: '100.00',
        date: '2023-05-10'
      },
      {
        isTransfer: true,
        time: '5月10日',
        istoday: '今天',
        desc: '【资产初始化】初期余额100.0',
        amount: '100.00',
        remark: '豪华地区哦',
        payAccount: '100.00',
        date: '2023-05-10'
      },
      {
        isTransfer: true,
        time: '5月10日',
        istoday: '今天',
        desc: '【资产初始化】初期余额100.0',
        amount: '100.00',
        remark: '豪华地区哦',
        payAccount: '100.00',
        date: '2023-05-10'
      },
      {
        isTransfer: true,
        time: '5月10日',
        istoday: '今天',
        desc: '【资产初始化】初期余额100.0',
        amount: '100.00',
        remark: '豪华地区哦',
        payAccount: '100.00',
        date: '2023-05-10'
      },
      {
        isTransfer: true,
        time: '5月10日',
        istoday: '今天',
        desc: '【资产初始化】初期余额100.0',
        amount: '100.00',
        remark: '豪华地区哦',
        payAccount: '100.00',
        date: '2023-05-10'
      },
    ],
    bookId: null,
    selectedDate: '',
    currentMonth: '',
    currentYear: '',
    activeTab: 'overview', // overview, month, year, total
    monthlyStats: {
      expense: '0.00',
      income: '0.00',
      balance: '0.00',
      budget: '0.00'
    },
    hasRecords: false,
    bills: [],
    bookDetail: {},
    selectedColor: '',
    isDefaultBook: false,
    bookStatistics: {
      total_count: 0,
      total_income: '0.00',
      total_expense: '0.00'
    },
    categories: [],
    showDeleteConfirm: false,
    weekday: '',
    dayOfMonth: '',
    // 备注
    notes: '',
    // 账本名称
    name: '',
    // 切换排序的标志
    sortType: 'time', // 初始排序类型
    pickerYear: '',
    pickerMonth: '',
    // 月份选择数据
    showPicker: false,
    pickerYear: new Date().getFullYear(),
    pickerMonth: new Date().getMonth() + 1,
    selectedYear: new Date().getFullYear(),
    selectedMonth: new Date().getMonth() + 1,
    selectedMonthDisplay: '5月1日-5月31日', // 显示在UI上的月份日期范围
    selectedYearDisplay: '1月1日-12月31日', // 显示在UI上的年份
    windowHeight: '',
    Navigation: '',
    scrollY: 0, // 当前页面滚动的Y坐标
    dataType: 'expense', // 数据类型：expense, income, other
    chartType: 'pie',    // 图表类型：pie, bar
    container: null,
  },

  /**
   * 生命周期函数--监听页面加载
   */
  onLoad: function (options) {
    console.log(app.globalData, 'app');
    this.setData({
      windowHeight: app.globalData.windowHeight,
      Navigation: app.globalData.Navigation
    });
    console.log(options, 'options');

    // 获取本地存储的主题颜色
    try {
      const themeColor = wx.getStorageSync('selectedColor');
      if (themeColor) {
        this.setData({
          selectedColor: themeColor
        });
        console.log('从本地存储获取的主题颜色:', themeColor);
      } else {
        // 如果没有存储过颜色，则使用默认颜色
        this.setData({
          selectedColor: '#FF7BAC'
        });
        console.log('使用默认主题颜色: #FF7BAC');
      }
    } catch (e) {
      console.error('获取主题颜色失败:', e);
      // 出错时使用默认颜色
      this.setData({
        selectedColor: '#FF7BAC'
      });
    }

    if (options.id) {
      // 设置当前日期
      const now = new Date();
      const year = now.getFullYear();
      const month = now.getMonth() + 1;
      const dayOfMonth = now.getDate();

      // 获取星期几
      const weekdays = ['星期日', '星期一', '星期二', '星期三', '星期四', '星期五', '星期六'];
      const weekday = weekdays[now.getDay()];

      this.setData({
        bookId: options.id,
        list: this.data.list,
        currentYear: year,
        currentMonth: month,
        selectedDate: `${year}年${month}月`,
        weekday: weekday,
        dayOfMonth: dayOfMonth
      });
      console.log('传递给contentList的数据:', this.data.list);

      this.fetchMonthlyData(year, month);
    } else {
      wx.showToast({
        title: '账本ID不存在',
        icon: 'none'
      });
      setTimeout(() => {
        wx.navigateBack();
      }, 1500);
    }
    this.getCurrentYearMonth();

    // 初始化应用主题色到UI元素
    this.updateUIWithThemeColor();
  },
  
  onBillItemTap(e) {
    console.log(e, 'e');
  },
  onBillItemTap(e) {
    console.log(e, 'e');
  },
  toggleSort() {
    // 切换排序类型
    const newSortType = this.data.sortType === 'amount' ? 'time' : 'amount';
    console.log('切换排序类型为:', newSortType);
    this.setData({
      sortType: newSortType
    });
  },

  /**
   * 生命周期函数--监听页面显示
   */
  onShow: function () {
    // 页面显示时可能需要刷新数据
    if (this.data.bookId && this.data.currentYear && this.data.currentMonth) {
      this.fetchMonthlyData(this.data.currentYear, this.data.currentMonth);
    }
  },

  // 显示年月选择器
  showMonthPicker: function () {
    console.log('显示年月选择器');

    this.setData({
      showPicker: true
    });
  },
  // 隐藏年月选择器
  hideMonthPicker() {
    this.setData({
      showPicker: false
    });
  },

  /**
   * 月份选择器确认选择
   */
  onMonthSelected(e) {
    const { year, month } = e.detail;

    // 更新选择的年月
    this.setData({
      pickerYear: year,
      pickerMonth: month,
      selectedYear: year,
      selectedMonth: month,
      showPicker: false,
      currentYear: year,
      currentMonth: month,
      selectedDate: `${year}年${month}月`
    });

    // 使用新选择的年月获取数据
    this.fetchMonthlyData(year, month);
  },

  // 获取当前年月
  getCurrentYearMonth: function () {
    const now = new Date();
    const year = now.getFullYear();
    const month = now.getMonth() + 1;
    this.setData({
      pickerYear: year,
      pickerMonth: month
    });
  },

  /**
   * 获取月度数据
   */
  fetchMonthlyData: function (year, month) {
    wx.showLoading({
      title: '加载中...',
    });

    // 使用getAccountBookDetail API获取账本详情
    getAccountBookDetail({ accountbook_id: this.data.bookId }).then(res => {
      wx.hideLoading();

      if (res && res.code === 1) {
        const bookData = res.data || {};

        if (bookData) {
          // 处理图片URL
          bookData.image = bookData.image ? util.getImageUrl(bookData.image) : '';

          // 判断是否为默认账本
          const isDefaultBook = bookData.is_default === '1';

          // 获取账本中的月度统计信息，如果API返回了相关数据
          const monthlyStats = {
            expense: bookData.monthly_expense || '0.00',
            income: bookData.monthly_income || '0.00',
            balance: bookData.monthly_balance || '0.00',
            budget: bookData.monthly_budget || '0.00'
          };

          // 获取账单列表，如果API返回了相关数据
          const bills = bookData.bills || [];

          this.setData({
            bookDetail: bookData,
            isDefaultBook: isDefaultBook,
            monthlyStats: monthlyStats,
            hasRecords: bills.length > 0,
            bills: bills,
            name: bookData.name,
            notes: bookData.notes
          });
        } else {
          wx.showToast({
            title: '未找到该账本',
            icon: 'none'
          });

          setTimeout(() => {
            wx.navigateBack();
          }, 1500);
        }
      } else {
        wx.showToast({
          title: res.msg || '获取账本详情失败',
          icon: 'none'
        });
      }
    }).catch(err => {
      wx.hideLoading();
      wx.showToast({
        title: '网络请求失败',
        icon: 'none'
      });
      console.error('获取账本详情失败:', err);
    });
  },

  /**
   * 切换Tab - Vant组件的事件处理
   */
  onTabChange: function (event) {
    const tabName = event.detail.name;
    this.setData({
      activeTab: tabName
    });

    // 根据tab加载不同数据
    if (tabName === 'overview') {
      this.fetchMonthlyData(this.data.currentYear, this.data.currentMonth);
    } else if (tabName === 'month') {
      this.fetchMonthReport();
    } else if (tabName === 'year') {
      this.fetchYearReport();
    } else if (tabName === 'total') {
      this.fetchTotalReport();
    }

    // 更新其他需要使用主题色的UI元素
    this.updateUIWithThemeColor();
  },

  /**
   * 使用主题色更新UI元素
   */
  updateUIWithThemeColor: function () {
    // 获取当前主题色
    const themeColor = this.data.selectedColor || '#FF7BAC';

    // 对于van-ellipsis，我们现在使用van-tabs的title-active-color属性
    // 这是在WXML中直接绑定的，不需要在这里额外处理

    // 这里可以添加其他需要动态更新颜色的UI元素

    console.log('使用主题色更新UI元素:', themeColor);
  },

  /**
   * 切换Tab
   */
  switchTab: function (e) {
    const tabId = e.currentTarget.dataset.id;
    this.setData({
      activeTab: tabId
    });

    // 根据tab加载不同数据
    if (tabId === 'overview') {
      this.fetchMonthlyData(this.data.currentYear, this.data.currentMonth);
    } else if (tabId === 'month') {
      this.fetchMonthReport();
    } else if (tabId === 'year') {
      this.fetchYearReport();
    } else if (tabId === 'total') {
      this.fetchTotalReport();
    }
  },

  /**
   * 显示日期选择器
   */
  showDatePicker: function () {
    // 显示日期选择器
    wx.showActionSheet({
      itemList: ['2023年', '2024年', '2025年'],
      success: (res) => {
        if (res.tapIndex === 2) { // 2025年
          this.setData({
            currentYear: 2025,
            selectedDate: `2025年${this.data.currentMonth}月`
          });
          this.fetchMonthlyData(2025, this.data.currentMonth);
        }
      }
    });
  },

  /**
   * 获取月报表
   */
  fetchMonthReport: function () {
    // 获取月度报表数据的API调用
  },

  /**
   * 获取年报表
   */
  fetchYearReport: function () {
    // 获取年度报表数据的API调用
  },

  /**
   * 获取总报表
   */
  fetchTotalReport: function () {
    // 获取总报表数据的API调用
  },

  /**
   * 添加记账
   */
  onAddRecord: function () {
    wx.navigateTo({
      url: `/packageA/pages/addRecord/addRecord?book_id=${this.data.bookId}`
    });
  },

  /**
   * 关闭页面
   */
  onClose: function () {
    wx.navigateBack();
  },

  /**
   * 编辑账本
   */
  onEdit: function () {
    wx.navigateTo({
      url: `/packageA/pages/bookSetting/bookSetting?id=${this.data.bookId}`
    });
  },

  /**
   * 按时间筛选
   */
  filterByTime: function () {
    wx.showActionSheet({
      itemList: ['按日查看', '按周查看', '按月查看'],
      success: (res) => {
        // 根据选择切换视图模式
      }
    });
  },

  /**
   * 获取账本详情
   */
  fetchBookDetail: function (bookId) {
    wx.showLoading({
      title: '加载中...',
    });

    // 使用现有的获取账本列表API，然后从列表中找到对应ID的账本
    getAccountBookDetail({ accountbook_id: bookId }).then(res => {
      wx.hideLoading();

      if (res && res.code === 1) {
        const bookList = res.data || [];

        if (bookList) {
          // 处理图片URL
          bookList.image = bookList.image ? util.getImageUrl(bookList.image) : '';

          // 判断是否为默认账本
          const isDefaultBook = bookList.is_default === '1';

          this.setData({
            bookList: bookList,
            isDefaultBook: isDefaultBook
          });

          // 获取账本分类和统计信息
          this.fetchBookCategories(bookId);
          this.fetchBookStatistics(bookId);
        } else {
          wx.showToast({
            title: '未找到该账本',
            icon: 'none'
          });

          setTimeout(() => {
            wx.navigateBack();
          }, 1500);
        }
      } else {
        wx.showToast({
          title: res.msg || '获取账本详情失败',
          icon: 'none'
        });
      }
    }).catch(err => {
      wx.hideLoading();
      wx.showToast({
        title: '网络请求失败',
        icon: 'none'
      });
      console.error('获取账本详情失败:', err);
    });
  },

  /**
   * 获取账本分类
   */
  fetchBookCategories: function (bookId) {
    // 这里应该调用获取账本分类的API
    // 现在使用示例数据
    const demoCategories = [
      { id: 1, name: '餐饮', color: '#FF9800', icon: '/static/icon/food.png' },
      { id: 2, name: '交通', color: '#2196F3', icon: '/static/icon/transport.png' },
      { id: 3, name: '购物', color: '#E91E63', icon: '/static/icon/shopping.png' },
      { id: 4, name: '娱乐', color: '#9C27B0', icon: '/static/icon/entertainment.png' },
      { id: 5, name: '医疗', color: '#00BCD4', icon: '/static/icon/medical.png' },
      { id: 6, name: '住房', color: '#4CAF50', icon: '/static/icon/house.png' }
    ];

    this.setData({
      categories: demoCategories
    });
  },

  /**
   * 获取账本统计信息
   */
  fetchBookStatistics: function (bookId) {
    // 这里应该调用获取账本统计的API
    // 现在使用示例数据
    const demoStatistics = {
      total_count: 42,
      total_income: '3528.50',
      total_expense: '2316.75'
    };

    this.setData({
      bookStatistics: demoStatistics
    });
  },

  /**
   * 设置为默认账本
   */
  onSetAsDefault: function () {
    wx.showLoading({
      title: '设置中...',
    });

    setDefaultBook(this.data.bookId).then(res => {
      wx.hideLoading();

      if (res && res.code === 1) {
        wx.showToast({
          title: '设置成功',
          icon: 'success'
        });

        // 更新当前页面状态
        this.setData({
          isDefaultBook: true,
          'bookDetail.is_default': '1'
        });
      } else {
        wx.showToast({
          title: res.msg || '设置失败',
          icon: 'none'
        });
      }
    }).catch(err => {
      wx.hideLoading();
      wx.showToast({
        title: '网络请求失败',
        icon: 'none'
      });
      console.error('设置默认账本失败:', err);
    });
  },

  /**
   * 分享账本
   */
  onShareBook: function () {
    wx.showToast({
      title: '分享功能暂未开放',
      icon: 'none'
    });
  },

  /**
   * 导出数据
   */
  onExportData: function () {
    wx.showToast({
      title: '导出功能暂未开放',
      icon: 'none'
    });
  },

  /**
   * 查看记录
   */
  onViewRecords: function () {
    // 跳转到账本记录页面
    wx.navigateTo({
      url: `/packageA/pages/bookRecords/bookRecords?id=${this.data.bookId}`
    });
  },

  /**
   * 删除账本
   */
  onDeleteBook: function () {
    if (this.data.isDefaultBook) {
      wx.showToast({
        title: '默认账本不能删除',
        icon: 'none'
      });
      return;
    }

    this.setData({
      showDeleteConfirm: true
    });
  },

  /**
   * 取消删除
   */
  onCancelDelete: function () {
    this.setData({
      showDeleteConfirm: false
    });
  },

  /**
   * 确认删除
   */
  onConfirmDelete: function () {
    // 这里应该调用删除账本的API
    wx.showLoading({
      title: '删除中...',
    });

    // 模拟API调用
    setTimeout(() => {
      wx.hideLoading();

      wx.showToast({
        title: '删除成功',
        icon: 'success'
      });

      this.setData({
        showDeleteConfirm: false
      });

      // 返回到账本列表页
      setTimeout(() => {
        wx.navigateBack();
      }, 1500);
    }, 1000);
  },

  /**
   * 管理分类
   */
  onManageCategories: function () {
    // 跳转到分类管理页面
    wx.navigateTo({
      url: `/packageA/pages/categoryManage/categoryManage?book_id=${this.data.bookId}`
    });
  },

  /**
   * 用户点击右上角分享
   */
  onShareAppMessage: function () {
    return {
      title: '我的记账本 - 有才记账',
      path: '/pages/index/index'
    };
  },

  /**
   * 监听页面滚动
   */
  onPageScroll: function(e) {
    // 更新页面滚动的Y坐标
    this.setData({
      scrollY: e.scrollTop
    });
  },
  
  /**
   * 设置数据类型（支出/收入/其他）
   */
  setDataType: function(e) {
    const type = e.currentTarget.dataset.type;
    this.setData({
      dataType: type
    });
    
    // 根据类型重新加载对应数据
    this.loadCategoryData(type);
  },
  
  /**
   * 加载分类数据
   */
  loadCategoryData: function(type) {
    // 实际项目中，这里会根据类型从服务器获取不同的分类数据
    console.log('加载', type, '类型的分类数据');
    
    // 模拟加载
    wx.showLoading({
      title: '加载中...',
    });
    
    // 这里简单模拟不同类型的数据，真实项目应该调用API
    const demoData = {
      expense: [
        { 
          id: 1, 
          name: '柴米油盐', 
          icon: '/static/icon/home.png', 
          amount: '450.00', 
          count: 5, 
          percentage: 54.88,
          color: '#FFA556'
        },
        { 
          id: 2, 
          name: '餐饮', 
          icon: '/static/icon/food.png', 
          amount: '263.00', 
          count: 6, 
          percentage: 32.07,
          color: '#FF6E6E'
        },
        { 
          id: 3, 
          name: '手续费', 
          icon: '/static/icon/fee.png', 
          amount: '155.00', 
          count: 10, 
          percentage: 18.9,
          color: '#73C0DE'
        },
        { 
          id: 4, 
          name: '水果', 
          icon: '/static/icon/fruit.png', 
          amount: '10.00', 
          count: 1, 
          percentage: 1.22,
          color: '#91CC75'
        },
        { 
          id: 5, 
          name: '娱乐', 
          icon: '/static/icon/entertainment.png', 
          amount: '2.00', 
          count: 1, 
          percentage: 0.24,
          color: '#FAC858'
        }
      ],
      income: [
        { 
          id: 1, 
          name: '工资', 
          icon: '/static/icon/salary.png', 
          amount: '1000.00', 
          count: 1, 
          percentage: 100,
          color: '#73C0DE'
        }
      ],
      other: []
    };
    
    setTimeout(() => {
      wx.hideLoading();
      
      // 根据选择的类型设置对应的分类数据
      this.setData({
        categories: demoData[type] || []
      });
    }, 300);
  },

 
  /**
   * 设置图表类型
   */
  setChartType: function(e) {
    const type = e.currentTarget.dataset.type;
    this.setData({
      chartType: type
    });
    
    // 更新图表显示
    this.updateChartDisplay();
  },
  
  /**
   * 更新图表显示
   */
  updateChartDisplay: function() {
    console.log('更新图表显示为', this.data.chartType);
    // 实际项目中，这里会根据chartType重绘图表
    // 可能需要使用第三方图表库如ECharts
  },
  
  /**
   * 刷新图表
   */
  refreshChart: function() {
    console.log('刷新图表');
    // 实际项目中，这里可能会重新请求数据或切换显示的数据维度
    
    // 模拟刷新效果
    wx.showLoading({
      title: '刷新中...',
    });
    
    setTimeout(() => {
      wx.hideLoading();
      wx.showToast({
        title: '已刷新',
        icon: 'success',
        duration: 1000
      });
    }, 500);
  },

  /**
   * 加载月报数据
   */
  fetchMonthReport: function() {
    // 实际项目中，这里会从服务器获取数据
    // 这里使用示例数据，真实项目应该调用API
    console.log('加载月报数据');
    
    // 模拟数据加载
    wx.showLoading({
      title: '加载中...',
    });
    
    // 模拟延迟
    setTimeout(() => {
      wx.hideLoading();
      
      // 设置月报相关数据
      const monthlyStats = {
        expense: this.data.monthlyStats.expense || '750.00',
        income: this.data.monthlyStats.income || '1000.00',
        balance: '250.0',
        budget: this.data.monthlyStats.budget || '-550.00',
        other: '8860.00'
      };
      
      // 加载分类数据
      this.setData({
        monthlyStats: monthlyStats
      });
      
      // 加载分类数据
      this.loadCategoryData('expense');
    }, 500);
  },
}); 