<view class="container">
  <!-- 顶部导航 -->
  <customNav isTitle="记账偏好"></customNav>

  <!-- 页面说明 -->
  <view class="page-desc">
    <view class="desc-icon">
      <t-icon name="setting" size="48rpx" />
    </view>
    <view class="desc-text">个性化设置记账相关信息。</view>
  </view>
  <!-- 账单设置 -->
  <view class="section">
    <view class="section-title">账单</view>
    <!-- 每月开始日期 -->
    <view class="setting-item" bindtap="selectMonthStartDay">
      <view class="item-label">每月开始日期</view>
      <view class="item-value">
        <text>每月{{selectedDay}}号</text>
        <t-icon name="chevron-right" size="40rpx" color="#BBBBBB" />
      </view>
    </view>
    <view class="item-desc">每月账单统计开始日期</view>
    <!-- 每日账单收支显示 -->
    <view class="setting-item">
      <view class="item-label">每日账单收支显示</view>
      <view class="item-value">
        <switch checked="{{showDailyIncome}}" bindchange="toggleDailyIncome" color="#a5ddb9" />
      </view>
    </view>
    <view class="item-desc">显示每天的收入和支出统计</view>
    <!-- 账单显示设置 -->
    <view class="setting-item" bindtap="goToBillDisplaySettings">
      <view class="item-label">账单显示设置</view>
      <view class="item-value">
        <text>账单列表显示样式设置</text>
        <t-icon name="chevron-right" size="40rpx" color="#BBBBBB" />
      </view>
    </view>
  </view>
  <!-- 主页设置 -->
  <view class="section">
    <view class="section-title">主页</view>
    <!-- 默认查询账本 -->
    <view class="setting-item" bindtap="selectDefaultBook">
      <view class="item-label">默认查询账本</view>
      <view class="item-value">
        <text>{{allBooksSelected ? bookList.length + '个账本' : defaultBook}}</text>
        <t-icon name="chevron-right" size="40rpx" color="#BBBBBB" />
      </view>
    </view>
    <view class="item-desc">首页、账单、报表页面默认查询账本</view>
  </view>
  <!-- 资产设置 -->
  <view class="section">
    <view class="section-title">资产</view>
    <!-- 资产月消费统计 -->
    <view class="setting-item" bindtap="selectMonthlyExpenseMethod">
      <view class="item-label">资产月消费统计</view>
      <view class="item-value">
        <text>{{monthlyExpenseMethod}}</text>
        <t-icon name="chevron-right" size="40rpx" color="#BBBBBB" />
      </view>
    </view>
    <view class="item-desc">选择月消费统计算法模式</view>
    <!-- 资产统计图 -->
    <view class="setting-item" bindtap="selectAssetChartSettings">
      <view class="item-label">资产统计图</view>
      <view class="item-value">
        <text>根据每天的资产变动生成资产统计图</text>
        <t-icon name="chevron-right" size="40rpx" color="#BBBBBB" />
      </view>
    </view>
  </view>
  <!-- 记账页面设置 -->
  <view class="section">
    <view class="section-title">记账页面设置</view>
    <!-- 记账页面设置 -->
    <view class="setting-item" bindtap="goToBookingPageSettings">
      <view class="item-label">记账页面设置</view>
      <view class="item-value">
        <text>账单页面个性化设置</text>
        <t-icon name="chevron-right" size="40rpx" color="#BBBBBB" />
      </view>
    </view>
  </view>
</view>
<!-- 账单周期设置弹窗 -->
<t-popup visible="{{showDatePickerPopup}}" bind:visible-change="onPopupChange" placement="center">
  <view class="date-picker-popup">
    <view class="popup-header">
      <view class="close-btn" bindtap="closeDatePicker">
        <t-icon name="close" size="48rpx" />
      </view>
      <view class="popup-title">设置账单周期</view>
      <view class="popup-subtitle">例如账单月周期为：{{billCycleExample}}</view>
      <view class="popup-desc">账单周期影响范围：每月账单、预算等其他功能</view>
    </view>
    <view class="date-grid">
      <view class="date-row">
        <view wx:for="{{7}}" wx:key="index" class="date-cell {{selectedDay === index+1 ? 'selected' : ''}}" bindtap="selectDay" data-day="{{index+1}}">
          {{index+1}}
        </view>
      </view>
      <view class="date-row">
        <view wx:for="{{7}}" wx:key="index" class="date-cell {{selectedDay === index+8 ? 'selected' : ''}}" bindtap="selectDay" data-day="{{index+8}}">
          {{index+8}}
        </view>
      </view>
      <view class="date-row">
        <view wx:for="{{7}}" wx:key="index" class="date-cell {{selectedDay === index+15 ? 'selected' : ''}}" bindtap="selectDay" data-day="{{index+15}}">
          {{index+15}}
        </view>
      </view>
      <view class="date-row">
        <view wx:for="{{7}}" wx:key="index" class="date-cell {{selectedDay === index+22 ? 'selected' : ''}}" bindtap="selectDay" data-day="{{index+22}}">
          {{index+22}}
        </view>
      </view>
      <view class="date-row">
        <view wx:for="{{daysInLastRow}}" wx:key="index" class="date-cell {{selectedDay === index+29 ? 'selected' : ''}}" bindtap="selectDay" data-day="{{index+29}}">
          {{index+29}}
        </view>
      </view>
    </view>
    <view class="popup-footer">
      <view class="cancel-btn" bindtap="closeDatePicker">取消</view>
      <view class="confirm-btn" bindtap="confirmDateSelection">确认</view>
    </view>
  </view>
</t-popup>
<!-- 账本选择弹窗 -->

<!-- 使用封装的账本选择器组件 -->
<book-selector
  show="{{showBookSelector}}"
  book-list="{{bookList}}"
  is-multi-select="{{isMultiSelect}}"
  is-card-mode="{{isCardMode}}"
  selected-book-ids="{{selectedBookIds}}"
  all-books-selected="{{allBooksSelected}}"
  bind:close="closeBookSelector"
  bind:visibleChange="onBookSelectorChange"
  bind:viewModeChange="toggleViewMode"
  bind:selectModeChange="toggleSelectMode"
  bind:select="selectBook"
  bind:selectAll="selectAllBooks"
  bind:selectionChange="toggleBookSelection"
  bind:confirm="confirmBookSelection"
  bind:add="addNewBook"
  bind:reload="reloadBookList"
/>

<!-- 记账页面设置弹窗 -->
<t-popup visible="{{showBookingPagePopup}}" bind:visible-change="onBookingPagePopupChange" placement="center">
  <view class="booking-page-popup">
    <view class="popup-header">
      <view class="close-btn" bindtap="closeBookingPagePopup">
        <t-icon name="close" size="48rpx" />
      </view>
      <view class="popup-title">记账页面设置</view>
    </view>
    <scroll-view scroll-y class="popup-content">
      <!-- 默认资产设置 -->
      <view class="setting-section">
        <view class="section-title">默认资产设置</view>
        <view class="setting-item">
          <view class="item-left">
            <view class="item-label">未选择资产提示</view>
            <view class="item-desc">没有选择资产会强制提示</view>
          </view>
          <view class="item-value">
            <switch checked="{{showAssetReminder}}" bindchange="toggleShowAssetReminder" color="#a5ddb9" />
          </view>
        </view>
        <view class="setting-item">
          <view class="item-left">
            <view class="item-label">分类资产记忆</view>
            <view class="item-desc">开启后默认选择此分类上次选择的资产</view>
          </view>
          <view class="item-value">
            <switch checked="{{rememberCategoryAsset}}" bindchange="toggleRememberCategoryAsset" color="#a5ddb9" />
          </view>
        </view>
        <view class="setting-item" bindtap="openAccountSelector">
          <view class="item-left">
            <view class="item-label">默认选择资产账户</view>
            <view class="item-desc">记账页面默认选择的资产账户</view>
          </view>
          <view class="item-value">
            <text>{{currentAccount.name}}</text>
            <t-icon name="chevron-right" size="40rpx" color="#BBBBBB" />
          </view>
        </view>
      </view>
      <!-- 默认账本设置 -->
      <view class="setting-section">
        <view class="section-title">默认账本设置</view>
        <view class="setting-item" bindtap="selectDefaultBookMode">
          <view class="item-left">
            <view class="item-label">默认选择账本</view>
            <view class="item-desc">跟随模式｜会随主页账本切换记账页面账本</view>
          </view>
          <view class="item-value">
            <text>跟随模式</text>
            <t-icon name="chevron-right" size="40rpx" color="#BBBBBB" />
          </view>
        </view>
      </view>
      <!-- 一般设置 -->
      <view class="setting-section">
        <view class="section-title">一般设置</view>
        <view class="setting-item" bindtap="selectCategoryStyleType">
          <view class="item-left">
            <view class="item-label">记账分类样式</view>
            <view class="item-desc">记账页面选择分类样式</view>
          </view>
          <view class="item-value">
            <text>{{categoryStyle}}</text>
            <t-icon name="chevron-right" size="40rpx" color="#BBBBBB" />
          </view>
        </view>
        <!-- 仅在分类样式为"列表翻页"时显示行数设置 -->
        <view class="setting-item" bindtap="selectCategoryRows" wx:if="{{categoryStyle === '列表翻页'}}">
          <view class="item-left">
            <view class="item-label">分类显示行数</view>
            <view class="item-desc">记账页面分类显示行数，默认3行</view>
          </view>
          <view class="item-value">
            <text>{{selectedRows}}行</text>
            <t-icon name="chevron-right" size="40rpx" color="#BBBBBB" />
          </view>
        </view>
        <view class="setting-item" bindtap="selectDateType">
          <view class="item-left">
            <view class="item-label">日期类型</view>
            <view class="item-desc">可设置「年月日」或「年月日时分」</view>
          </view>
          <view class="item-value">
            <text>{{dateType}}</text>
            <t-icon name="chevron-right" size="40rpx" color="#BBBBBB" />
          </view>
        </view>
        <view class="setting-item" bindtap="goToBookingFeatures">
          <view class="item-left">
            <view class="item-label">记账功能</view>
            <view class="item-desc">定制需要的功能的开关</view>
          </view>
          <view class="item-value">
            <t-icon name="chevron-right" size="40rpx" color="#BBBBBB" />
          </view>
        </view>
        <view class="setting-item">
          <view class="item-left">
            <view class="item-label">记录未来1个月内账单</view>
            <view class="item-desc">不建议开启，开启后可记录未来1个月内账单，如今天1月1号记录2月1号的账单，容易做假账。</view>
          </view>
          <view class="item-value">
            <switch checked="{{allowFutureRecords}}" bindchange="toggleAllowFutureRecords" color="#a5ddb9" />
          </view>
        </view>
        <view class="setting-item">
          <view class="item-left">
            <view class="item-label">资产余额不足检验(仅新增)</view>
            <view class="item-desc">开启后，资产进行余额扣减不可负数如银行卡余额1元记账2元将无法记录</view>
          </view>
          <view class="item-value">
            <switch checked="{{checkBalanceShortage}}" bindchange="toggleCheckBalanceShortage" color="#a5ddb9" />
          </view>
        </view>
        <view class="setting-item">
          <view class="item-left">
            <view class="item-label">记账键盘震动反馈</view>
            <view class="item-desc">点击键盘时震动反馈</view>
          </view>
          <view class="item-value">
            <switch checked="{{keyboardVibration}}" bindchange="toggleKeyboardVibration" color="#a5ddb9" />
          </view>
        </view>
        <view class="setting-item">
          <view class="item-left">
            <view class="item-label">收入记账反馈动态效果</view>
            <view class="item-desc">记账成功时的动画效果</view>
          </view>
          <view class="item-value">
            <switch checked="{{incomeAnimation}}" bindchange="toggleIncomeAnimation" color="#a5ddb9" />
          </view>
        </view>
      </view>
    </scroll-view>
  </view>
</t-popup>
<!-- 资产账户选择弹窗 -->
<accountSelector
  show="{{showAccountSelector}}"
  mode="{{accountSelectorMode}}"
  accounts="{{accounts}}"
  themeColor="{{selectedColor}}"
  showSettings="{{false}}"
  selectedAccountId="{{currentAccountType === 'from' ? currentAccount.id : (currentAccountType === 'to' ? currentAccount2.id : 0)}}"
  bind:modeChange="handleAccountSelectorModeChange"
  bind:select="handleAccountSelect"
  bind:add="handleAddAccount"
  bind:close="closeAccountSelector"
  bind:settings="handleAccountSettings"
  bind:refresh="handleRefreshAccounts"
></accountSelector>
<!-- 分类样式选择弹窗 -->
<t-popup visible="{{showCategoryStylePopup}}" bind:visible-change="onCategoryStylePopupChange" placement="bottom">
  <view class="category-style-popup">
    <view class="popup-header">
      <view class="close-btn" bindtap="closeCategoryStylePopup">
        <t-icon name="close" size="48rpx" />
      </view>
      <view class="popup-title">请设置分类样式类型</view>
    </view>
    <view class="style-list">
      <view class="style-item" bindtap="selectCategoryStyle" data-style="list">
        <view class="item-label">列表翻页</view>
        <view class="item-arrow">
          <t-icon name="chevron-right" size="40rpx" color="#BBBBBB" />
        </view>
      </view>
      <view class="style-item" bindtap="selectCategoryStyle" data-style="card">
        <view class="item-label">卡片</view>
        <view class="item-arrow">
          <t-icon name="chevron-right" size="40rpx" color="#BBBBBB" />
        </view>
      </view>
    </view>
  </view>
</t-popup>
<!-- 分类显示行数选择弹窗 -->
<t-popup visible="{{showRowsSelector}}" bind:visible-change="onRowsSelectorChange" placement="bottom">
  <view class="rows-selector-popup">
    <view class="popup-header">
      <view class="close-btn" bindtap="closeRowsSelector">
        <t-icon name="close" size="48rpx" />
      </view>
      <view class="popup-title">选择行数</view>
    </view>
    <view class="rows-grid">
      <view class="rows-row">
        <view class="row-cell {{selectedRows === 1 ? 'selected' : ''}}" bindtap="selectRows" data-rows="1">
          1
        </view>
        <view class="row-cell {{selectedRows === 2 ? 'selected' : ''}}" bindtap="selectRows" data-rows="2">
          2
        </view>
        <view class="row-cell {{selectedRows === 3 ? 'selected' : ''}}" bindtap="selectRows" data-rows="3">
          3
        </view>
        <view class="row-cell {{selectedRows === 4 ? 'selected' : ''}}" bindtap="selectRows" data-rows="4">
          4
        </view>
        <view class="row-cell {{selectedRows === 5 ? 'selected' : ''}}" bindtap="selectRows" data-rows="5">
          5
        </view>
        <view class="row-cell {{selectedRows === 6 ? 'selected' : ''}}" bindtap="selectRows" data-rows="6">
          6
        </view>
        <view class="row-cell {{selectedRows === 7 ? 'selected' : ''}}" bindtap="selectRows" data-rows="7">
          7
        </view>
      </view>
      <view class="rows-row">
        <view class="row-cell {{selectedRows === 8 ? 'selected' : ''}}" bindtap="selectRows" data-rows="8">
          8
        </view>
        <view class="row-cell {{selectedRows === 9 ? 'selected' : ''}}" bindtap="selectRows" data-rows="9">
          9
        </view>
        <view class="row-cell {{selectedRows === 10 ? 'selected' : ''}}" bindtap="selectRows" data-rows="10">
          10
        </view>
        <view class="row-cell {{selectedRows === 11 ? 'selected' : ''}}" bindtap="selectRows" data-rows="11">
          11
        </view>
        <view class="row-cell {{selectedRows === 12 ? 'selected' : ''}}" bindtap="selectRows" data-rows="12">
          12
        </view>
        <view class="row-cell {{selectedRows === 13 ? 'selected' : ''}}" bindtap="selectRows" data-rows="13">
          13
        </view>
        <view class="row-cell {{selectedRows === 14 ? 'selected' : ''}}" bindtap="selectRows" data-rows="14">
          14
        </view>
      </view>
      <view class="rows-row">
        <view class="row-cell {{selectedRows === 15 ? 'selected' : ''}}" bindtap="selectRows" data-rows="15">
          15
        </view>
        <view class="row-cell {{selectedRows === 16 ? 'selected' : ''}}" bindtap="selectRows" data-rows="16">
          16
        </view>
        <view class="row-cell {{selectedRows === 17 ? 'selected' : ''}}" bindtap="selectRows" data-rows="17">
          17
        </view>
        <view class="row-cell {{selectedRows === 18 ? 'selected' : ''}}" bindtap="selectRows" data-rows="18">
          18
        </view>
        <view class="row-cell {{selectedRows === 19 ? 'selected' : ''}}" bindtap="selectRows" data-rows="19">
          19
        </view>
        <view class="row-cell {{selectedRows === 20 ? 'selected' : ''}}" bindtap="selectRows" data-rows="20">
          20
        </view>
        <view class="row-cell {{selectedRows === 21 ? 'selected' : ''}}" bindtap="selectRows" data-rows="21">
          21
        </view>
      </view>
      <view class="rows-row">
        <view class="row-cell {{selectedRows === 22 ? 'selected' : ''}}" bindtap="selectRows" data-rows="22">
          22
        </view>
        <view class="row-cell {{selectedRows === 23 ? 'selected' : ''}}" bindtap="selectRows" data-rows="23">
          23
        </view>
        <view class="row-cell {{selectedRows === 24 ? 'selected' : ''}}" bindtap="selectRows" data-rows="24">
          24
        </view>
        <view class="row-cell {{selectedRows === 25 ? 'selected' : ''}}" bindtap="selectRows" data-rows="25">
          25
        </view>
        <view class="row-cell {{selectedRows === 26 ? 'selected' : ''}}" bindtap="selectRows" data-rows="26">
          26
        </view>
        <view class="row-cell {{selectedRows === 27 ? 'selected' : ''}}" bindtap="selectRows" data-rows="27">
          27
        </view>
        <view class="row-cell {{selectedRows === 28 ? 'selected' : ''}}" bindtap="selectRows" data-rows="28">
          28
        </view>
      </view>
      <view class="rows-row">
        <view class="row-cell {{selectedRows === 29 ? 'selected' : ''}}" bindtap="selectRows" data-rows="29">
          29
        </view>
        <view class="row-cell {{selectedRows === 30 ? 'selected' : ''}}" bindtap="selectRows" data-rows="30">
          30
        </view>
        <view class="row-cell {{selectedRows === 31 ? 'selected' : ''}}" bindtap="selectRows" data-rows="31">
          31
        </view>
      </view>
    </view>
    <view class="popup-footer">
      <view class="cancel-btn" bindtap="closeRowsSelector">取消</view>
      <view class="confirm-btn" bindtap="confirmRowsSelection">确定</view>
    </view>
  </view>
</t-popup>
<!-- 日期类型选择弹窗 -->
<t-popup visible="{{showDateTypePopup}}" bind:visible-change="onDateTypePopupChange" placement="bottom">
  <view class="date-type-popup">
    <view class="popup-header">
      <view class="close-btn" bindtap="closeDateTypePopup">
        <t-icon name="close" size="48rpx" />
      </view>
      <view class="popup-title">请设置时间类型</view>
    </view>
    <view class="date-type-list">
      <view class="date-type-item" bindtap="selectDate" data-type="date">
        <view class="type-label">年月日</view>
        <view class="type-desc">如：2023年1月1日</view>
        <view class="type-hint">仅需选择日期，简化时间对记账的分类有更求</view>
        <view class="item-arrow">
          <t-icon name="chevron-right" size="40rpx" color="#BBBBBB" />
        </view>
      </view>
      <view class="date-type-item" bindtap="selectDate" data-type="datetime">
        <view class="type-label">年月日时分</view>
        <view class="type-desc">如：2023年1月1日 12点0分</view>
        <view class="type-hint">选择日期后还需选择时间，精确记账时间</view>
        <view class="item-arrow">
          <t-icon name="chevron-right" size="40rpx" color="#BBBBBB" />
        </view>
      </view>
    </view>
  </view>
</t-popup>
<!-- 记账功能设置弹窗 -->
<t-popup visible="{{showFeaturePopup}}" bind:visible-change="onFeaturePopupChange" placement="bottom">
  <view class="feature-popup">
    <view class="popup-header">
      <view class="close-btn" bindtap="closeFeaturePopup">
        <t-icon name="close" size="48rpx" />
      </view>
      <view class="popup-title">记账功能</view>
    </view>
    <view class="feature-list">
      <view class="feature-item">
        <view class="feature-info">
          <view class="feature-name">资产账户</view>
          <view class="feature-desc">记账选择资产自动扣款</view>
        </view>
        <view class="feature-switch">
          <switch checked="{{features.assetAccount}}" bindchange="toggleFeature" data-feature="assetAccount" color="#a5ddb9" />
        </view>
      </view>
      <view class="feature-item">
        <view class="feature-info">
          <view class="feature-name">多账本</view>
          <view class="feature-desc">账单根据场景分开</view>
        </view>
        <view class="feature-switch">
          <switch checked="{{features.multiBook}}" bindchange="toggleFeature" data-feature="multiBook" color="#a5ddb9" />
        </view>
      </view>
      <view class="feature-item">
        <view class="feature-info">
          <view class="feature-name">报销</view>
        </view>
        <view class="feature-switch">
          <switch checked="{{features.reimbursement}}" bindchange="toggleFeature" data-feature="reimbursement" color="#a5ddb9" />
        </view>
      </view>
      <view class="feature-item">
        <view class="feature-info">
          <view class="feature-name">标签</view>
          <view class="feature-desc">根据标签区分账单</view>
        </view>
        <view class="feature-switch">
          <switch checked="{{features.tags}}" bindchange="toggleFeature" data-feature="tags" color="#a5ddb9" />
        </view>
      </view>
      <view class="feature-item">
        <view class="feature-info">
          <view class="feature-name">优惠</view>
          <view class="feature-desc">支出账单优惠券</view>
        </view>
        <view class="feature-switch">
          <switch checked="{{features.discount}}" bindchange="toggleFeature" data-feature="discount" color="#a5ddb9" />
        </view>
      </view>
      <view class="feature-item">
        <view class="feature-info">
          <view class="feature-name">不计入</view>
          <view class="feature-desc">不计入收支/预算</view>
        </view>
        <view class="feature-switch">
          <switch checked="{{features.notIncluded}}" bindchange="toggleFeature" data-feature="notIncluded" color="#a5ddb9" />
        </view>
      </view>
      <view class="feature-item">
        <view class="feature-info">
          <view class="feature-name">账单模板</view>
        </view>
        <view class="feature-switch">
          <switch checked="{{features.billTemplate}}" bindchange="toggleFeature" data-feature="billTemplate" color="#a5ddb9" />
        </view>
      </view>
      <view class="feature-item">
        <view class="feature-info">
          <view class="feature-name">账单图片</view>
        </view>
        <view class="feature-switch">
          <switch checked="{{features.billImage}}" bindchange="toggleFeature" data-feature="billImage" color="#a5ddb9" />
        </view>
      </view>
    </view>
  </view>
</t-popup>
