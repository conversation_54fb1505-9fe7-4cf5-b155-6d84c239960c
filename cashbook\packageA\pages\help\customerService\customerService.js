// packageA/pages/help/customerService/customerService.js
import { getKefu } from '../../../../api/basic/index';
const util = require('../../../../utils/index');
Page({

  /**
   * 页面的初始数据
   */
  data: {
    kefuInfo: {
      kefu_tel: '',
      kefu_qrcode: ''
    },
    qrcodeUrl: ''
  },

  /**
   * 获取客服信息
   */
  fetchKefuInfo() {
    wx.showLoading({
      title: '加载中',
    });

    getKefu().then(res => {
      console.log('客服信息:', res);
      if (res && res.data) {
        const kefuInfo = res.data;
        const qrcodeUrl = kefuInfo.kefu_qrcode ? util.getImageUrl(kefuInfo.kefu_qrcode) : '';

        this.setData({
          kefuInfo,
          qrcodeUrl
        });
      }
      wx.hideLoading();
    }).catch(err => {
      console.error('获取客服信息失败:', err);
      wx.hideLoading();
      wx.showToast({
        title: '获取客服信息失败',
        icon: 'none'
      });
    });
  },

  /**
   * 拨打客服电话
   */
  callKefuPhone() {
    const { kefu_tel } = this.data.kefuInfo;
    console.log(kefu_tel, 'kefu_tel=================');
    if (kefu_tel) {
      wx.makePhoneCall({
        phoneNumber: kefu_tel,
        success: () => {
          console.log('拨打电话成功');
        },
        fail: (err) => {
          console.error('拨打电话失败:', err);
        }
      });
    } else {
      wx.showToast({
        title: '暂无客服电话',
        icon: 'none'
      });
    }
  },

  /**
   * 生命周期函数--监听页面加载
   */
  onLoad(options) {
    // 设置导航栏标题
    wx.setNavigationBarTitle({
      title: '联系客服',
    });

    // 获取客服信息
    this.fetchKefuInfo();
  },

  /**
   * 生命周期函数--监听页面初次渲染完成
   */
  onReady() {

  },

  /**
   * 生命周期函数--监听页面显示
   */
  onShow() {
    // 页面显示时如果没有数据则重新获取
    if (!this.data.kefuInfo.kefu_tel && !this.data.kefuInfo.kefu_qrcode) {
      this.fetchKefuInfo();
    }
  },

  /**
   * 生命周期函数--监听页面隐藏
   */
  onHide() {

  },

  /**
   * 生命周期函数--监听页面卸载
   */
  onUnload() {

  },

  /**
   * 页面相关事件处理函数--监听用户下拉动作
   */
  onPullDownRefresh() {
    this.fetchKefuInfo();
    wx.stopPullDownRefresh();
  },

  /**
   * 页面上拉触底事件的处理函数
   */
  onReachBottom() {

  },

  /**
   * 用户点击右上角分享
   */
  onShareAppMessage() {
    return {
      title: '小猫记账 - 客服服务',
      path: '/packageA/pages/help/customerService/customerService'
    };
  }
})