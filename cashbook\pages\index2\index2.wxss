.content {
  overflow: hidden;
  /* 改为hidden防止整体页面滚动 */
  height: 100vh;
  position: relative;
}

/* 滚动容器样式 */
.scroll-container {
  position: relative;
  margin-top: 10px;
  padding-bottom: 20px;
  -webkit-overflow-scrolling: touch;
  /* 使iOS上滚动更流畅 */
}

.box {
  background-color: #a1b584;
  color: white;
  position: fixed;
  top: 0;
  left: 0;
  border-radius: 0 0 15px 15px;
  width: 100%;
  /* z-index: 999; */
  /* 动态设置高度 */
  /* height: 100px; */
  /* 暂时隐藏超出的图片 */
  overflow: hidden;
}

.title {
  text-align: center;
  margin-top: 60px;
  color: #000;
  font-weight: 700;
  /* border: 1px solid; */
  position: relative;
  z-index: 9999999;
  display: flex;
  align-items: center;
  justify-content: space-between;
  padding: 0 15px;
}

.navPic {
  text-align: center;
}

.navPic image {
  width: 400px;
  height: 200px;
  transform: translateY(-40px);

}

.pic {
  display: grid;
  grid-template-columns: 1fr 1fr;
  width: fit-content;
  position: absolute;
  gap: 10px;
  right: 10px;
  top: 0;
}

.pic image {
  width: 25px;
  height: 25px;
}

/* 卡片模块 */
.card {
  position: fixed;
  width: 85%;
  top: 180px;
  left: 50%;
  transform: translateX(-50%);
  background-color: white;
  padding: 10px;
  border-radius: 10px;
  z-index: 9;
  box-shadow: 2px 2px 1px 2px rgba(219, 218, 218, 0.6);
}

.card2 {
  /* border: 1px solid; */
  margin: 10px 20px;
  background-color: #fff;
  padding: 10px;
  /* z-index: 1000; */
  position: relative;
  border-radius: 15px;
}

.showAcount image {
  width: 30px;
  height: 30px;
  object-fit: cover;
}

/* .hide image{
  width: 20px;
  height: 20px;
} */
.colonmOne {
  display: grid;
  grid-template-columns: 1fr 1fr;
  align-items: center;
  /* padding-top: 20px; */
}

.cardprice {
  font-size: 64rpx;
}

.showAcount {
  justify-self: end;
}

.COleft {
  color: #8b8b8b;
}

.zc {
  justify-self: end;
  background-color: #bec9a9;
  padding: 6px 4px;
  border-radius: 10px;
  font-size: 24rpx;
}

.colonmTwo {
  display: grid;
  grid-template-columns: 1fr 1fr;
  align-items: center;
  padding-bottom: 10px;
}


.regist {
  background-color: #a1b584;
  margin: 0 10px;
  border-radius: 20px;
  padding: 10px 0;
  text-align: center;
  margin-top: 15px;
}

/* 卡片end */

/* 列表头 */
.list {
  box-sizing: border-box;
  padding: 0 10px;
  margin-bottom: 150px;
}

.listDetail {
  height: 380px;
  overflow-y: auto;
  background-color: #fff;
  box-sizing: border-box;
  padding: 0 10px 100px 10px;
}

.three {
  color: #828282
}

.listColonm {
  padding-bottom: 10px;
}

.listColonm view:nth-child(2) {
  justify-self: end;
}

.ldItem {
  display: grid;
  grid-template-columns: 55px auto 1fr;
  gap: 10px;
  align-items: center;
  margin: 5px 0;
}

.ldItem view:nth-child(1) {
  width: 55px;
  height: 55px;
  border: 1px solid;
  border-radius: 5px;
  display: grid;
  align-items: center;
  text-align: center;
}

.ldItem view:nth-child(2) {
  /* border: 1px solid; */
}

.ldItem view:nth-child(3) {
  justify-self: end;
}

.remark {
  text-align: center;
  color: #828282;
  margin-top: 30px;
}

.remark2 {
  text-align: center;
  color: #a1b584;
  margin-top: 15px;
}

.oneColonm {
  display: grid;
  grid-template-columns: 1fr auto;
  align-items: center;
  margin: 5px 10px;
  padding: 3px 0;
}

.header-buttons {
  display: flex;
  align-items: center;
  gap: 5px;
}

.time {
  background-color: #a1b584;
  padding: 3px 8px;
  border-radius: 15px;
  color: white;
  width: fit-content;
  font-size: 13px;
  justify-self: end;
}

/* 移除不需要的样式 */
.time.active,
.amount-button {
  display: none;
}

/* 日期区域样式 */
.date-section {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin: 0 10px;
  padding: 5px 0;
  color: #828282;
  font-size: 14px;
}

.date-info {
  display: flex;
  align-items: center;
}

.today-tag {
  background-color: #a1b584;
  color: white;
  border-radius: 10px;
  padding: 1px 5px;
  margin-left: 5px;
  font-size: 12px;
}

.expense-info {
  color: #828282;
}

/* 账单容器 */
.bill-container {
  margin: 0;
  padding-bottom: 120px;
  /* 增加底部内边距，避免内容被遮挡 */
  min-height: calc(100vh - 470px);
  /* 设置最小高度确保有足够的滚动空间 */
}

/* 账单列表项样式 */
.bill-item {
  display: flex;
  flex-direction: row;
  padding: 15px 10px;
  background-color: #fff;
  margin: 0 10px 1px 10px;
  border-radius: 10px;
  position: relative;
  align-items: center;
  margin-bottom: 10px;
}

.bill-icon {
  width: 45px;
  height: 45px;
  background-color: #f0f0f0;
  border-radius: 50%;
  display: flex;
  justify-content: center;
  align-items: center;
  margin-right: 15px;
}

.bill-icon image {
  width: 25px;
  height: 25px;
}

.bill-content {
  flex: 1;
  display: flex;
  flex-direction: column;
  justify-content: center;
}

.bill-title {
  font-size: 15px;
  color: #333;
  margin-bottom: 5px;
}

.bill-desc {
  font-size: 11px;
  color: #999;
  white-space: nowrap;
  overflow: hidden;
  text-overflow: ellipsis;
  max-width: 150px;
}

.bill-amount {
  font-size: 15px;
  font-weight: 500;
  color: #333;
  position: absolute;
  right: 10px;
  top: 15px;
}

.bill-remark {
  font-size: 11px;
  color: #999;
  position: absolute;
  right: 10px;
  bottom: 15px;
}

.transfer-remark {
  position: absolute;
  right: 10px;
  bottom: 15px;
  font-size: 11px;
  color: #999;
}

.mySclace {
  position: none;
}

.chagePosition {
  position: none;
}

/* 底部导航空间 */
.bottom-nav-spacer {
  height: 70px;
  width: 100%;
}

/* 新增账本选择区域的样式 */
.cashbook-section {
  display: flex;
  align-items: center;
  background-color: rgba(255, 255, 255, 0.8);
  border-radius: 20px;
  padding: 6px 12px;
  max-width: 180px;
}

.cashbook-icon {
  margin-right: 5px;
  width: 20px;
  height: 20px;
}

.cashbook-icon image {
  width: 100%;
  height: 100%;
  object-fit: contain;
}

.cashbook-info {
  font-size: 14px;
  color: #000;
  font-weight: 500;
  white-space: nowrap;
  overflow: hidden;
  text-overflow: ellipsis;
}

/* 添加加载中提示的样式 */
.loading-container {
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  padding: 40rpx 0;
}

.loading-spinner {
  width: 60rpx;
  height: 60rpx;
  border: 4rpx solid #f3f3f3;
  border-top: 4rpx solid #8dc63f;
  border-radius: 50%;
  animation: spin 1s linear infinite;
  margin-bottom: 20rpx;
}

.loading-text {
  font-size: 28rpx;
  color: #999;
}

@keyframes spin {
  0% {
    transform: rotate(0deg);
  }

  100% {
    transform: rotate(360deg);
  }
}