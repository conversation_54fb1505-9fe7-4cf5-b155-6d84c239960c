// packageA/pages/personalize/personalize.js
Page({
  /**
   * 页面的初始数据
   */
  data: {
    iconPath: '/static/icon/calendar.png',
  },

  /**
   * 生命周期函数--监听页面加载
   */
  onLoad: function (options) {
    
  },

  /**
   * 获取右侧文本点击事件
   */
  getText: function(e) {
    console.log('管理按钮被点击', e);
    // 这里可以添加管理功能的逻辑
  },

  /**
   * 导航到主题设置页面
   */
  navigateToTheme: function() {
    
    wx.navigateTo({
      url: '/packageA/pages/theme/theme',
    });
  },

  /**
   * 导航到主题风格设置页面
   */
  navigateToThemeStyle: function() {
    wx.navigateTo({
      url: '/packageA/pages/themeStyle/themeStyle',
    });
  },

  /**
   * 颜色选择点击事件（保留原有功能）
   */
  onBoxClick: function(e) {
    const color = e.currentTarget.dataset.color;
    console.log('选择的颜色:', color);
    // 这里可以添加颜色选择的逻辑
  }
})