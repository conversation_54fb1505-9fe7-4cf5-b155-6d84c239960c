<!--packageA/pages/help/articleCategory/articleCategory.wxml-->
<view class="article-category-container">
  <!-- 顶部导航 -->
  <!-- <view class="nav-bar">
    <view class="back-btn" bindtap="goBack">
      <image src="/static/icon/back.png" mode="aspectFit"></image>
    </view>
    <view class="category-title">{{categoryName}}</view>
  </view> -->

  <!-- 文章列表 -->
  <view class="article-list" wx:if="{{!loading && articles.length > 0}}">
    <!-- 分类说明 -->
    <view class="category-header" wx:if="{{categoryDescription}}">
      <view class="category-description">{{categoryDescription}}</view>
    </view>

    <!-- 文章列表项 -->
    <view class="article-item" wx:for="{{articles}}" wx:key="id" bindtap="viewArticle" data-id="{{item.id}}">
      <view class="article-left">
        <view class="article-title">{{item.title}}</view>
        <view class="article-desc">{{item.description || '暂无描述'}}</view>
        <!-- <view class="article-meta">
          <text class="article-date">{{item.publish_time || '未知时间'}}</text>
          <text class="article-views" wx:if="{{item.view_count}}">{{item.view_count}} 阅读</text>
        </view> -->
      </view>
    </view>
    
    <!-- 加载更多 -->
    <view class="load-more" wx:if="{{hasMore}}">
      <view class="loading-icon"></view>
      <text>加载更多...</text>
    </view>
    <view class="no-more" wx:else>
      <text>-- 已经到底了 --</text>
    </view>
  </view>
  
  <!-- 加载中 -->
  <view class="loading-container" wx:if="{{loading}}">
    <view class="loading-icon"></view>
    <text>加载中...</text>
  </view>
  
  <!-- 空状态 -->
  <view class="empty-container" wx:if="{{!loading && articles.length === 0}}">
    <text>暂无文章</text>
  </view>
</view> 