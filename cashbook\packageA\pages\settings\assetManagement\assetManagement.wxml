<view class="asset-management-container">
  <!-- 顶部状态栏 -->
  <view class="status-bar" style="height: {{statusBarHeight}}px;"></view>

  <!-- 顶部导航栏 -->
  <view class="navigation-bar">
    <view class="back-button" bindtap="navigateBack">
      <t-icon name="chevron-left" size="48rpx" color="#333333" />
    </view>

    <!-- 标签栏 -->
    <view class="tab-bar">
      <view class="tab {{activeTab === '资金' ? 'active' : ''}}" bindtap="switchTab" data-tab="资金">
        <text>资金</text>
      </view>
      <view class="tab {{activeTab === '投资' ? 'active' : ''}}" bindtap="switchTab" data-tab="投资">
        <text>投资</text>
      </view>
      <view class="tab {{activeTab === '应收' ? 'active' : ''}}" bindtap="switchTab" data-tab="应收">
        <text>应收</text>
      </view>
      <view class="tab {{activeTab === '应付' ? 'active' : ''}}" bindtap="switchTab" data-tab="应付">
        <text>应付</text>
      </view>
    </view>
  </view>

  <!-- 资产列表 -->
  <scroll-view scroll-y class="asset-list">
    <!-- 加载中状态 -->
    <view class="loading-container" wx:if="{{loading}}">
      <t-loading theme="circular" size="40rpx" />
      <text class="loading-text">加载中...</text>
    </view>

    <!-- 无数据状态 -->
    <view class="empty-container" wx:elif="{{!currentAccounts.length}}">
      <text class="empty-text">暂无{{activeTab}}账户数据</text>
    </view>

    <!-- 账户列表 -->
    <block wx:else>
      <view class="asset-card" wx:for="{{currentAccounts}}" wx:key="id" bindtap="selectAsset" data-id="{{item.id}}" data-name="{{item.name}}" data-type="{{item.type}}">
        <view class="asset-card-left">
          <view class="asset-icon">
            <image src="{{item.image || '/static/icon/card.png'}}" mode="aspectFit"></image>
          </view>
          <view class="asset-info">
            <view class="asset-name">{{item.name}}</view>
            <view class="asset-type">{{item.type_text}}</view>
          </view>
        </view>
        <view class="asset-card-right">
          <image class="bg-icon" src="{{item.image || '/static/icon/card.png'}}" mode="aspectFit"></image>
          <!-- <view class="selected-mark" wx:if="{{selectedAssetId === item.id}}">
            <image src="/static/icon/active.png" mode="aspectFit"></image>
          </view> -->
        </view>
      </view>
    </block>
  </scroll-view>
</view>
