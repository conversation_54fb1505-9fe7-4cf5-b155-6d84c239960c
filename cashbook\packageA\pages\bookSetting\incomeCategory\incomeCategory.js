import { getIncomeCategoryList } from '../../../../api/book/index';
const util = require('../../../../utils/index.js')
import { getCategoryList,addCategory,editCategory} from '../../../../api/category/index.js';
Page({
  /**
   * 页面的初始数据
   */
  data: {
    activeTab: 'expenses', // 当前激活的标签: expenses（支出）或 income（收入）
    categories: [], // 分类列表
    selectedBook: '默认账本', // 选中的账本
    currentBook: '默认账本', // 当前显示的账本名称
    statusBarHeight: 0, // 状态栏高度
    navBarHeight: 44, // 导航栏高度
    activeCategory: null, // 当前展开的分类ID，默认为null（全部折叠）
    showMorePopup: false, // 显示更多选项弹出层
    currentCategoryId: null, // 当前操作的分类ID
    accountbook_id: null, // 账本ID
    needReload: true, // 是否需要重新加载数据

    parentId:'', // 父类ID，默认为空
    accountbook_id: '', // 账本ID，默认为空

    // 演示数据
    expensesssssCategories: [
      { id: 1, name: '餐饮', icon: '/static/icons/food.png', isExpanded: false },
      { id: 2, name: '柴米油盐', icon: '/static/icons/grocery.png', isExpanded: false },
      { id: 3, name: '物业水电', icon: '/static/icons/utilities.png', isExpanded: false },
      { id: 4, name: '住宿', icon: '/static/icons/accommodation.png', isExpanded: false },
      { id: 5, name: '娱乐', icon: '/static/icons/entertainment.png', isExpanded: false },
      { id: 6, name: '美妆', icon: '/static/icons/beauty.png', isExpanded: false },
      { id: 7, name: '旅游', icon: '/static/icons/travel.png', isExpanded: false },
      { id: 8, name: '水果', icon: '/static/icons/fruits.png', isExpanded: false },
      { id: 9, name: '任天野', icon: '/static/icons/games.png', isExpanded: false }
    ],
    incomeCategories: [
      { id: 101, name: '工资', icon: '/static/icons/salary.png', isExpanded: false },
      { id: 102, name: '奖金', icon: '/static/icons/bonus.png', isExpanded: false },
      { id: 103, name: '兼职', icon: '/static/icons/part-time.png', isExpanded: false },
      { id: 104, name: '理财', icon: '/static/icons/investment.png', isExpanded: false },
      { id: 105, name: '其他收入', icon: '/static/icons/other-income.png', isExpanded: false }
    ],
    // 默认子分类数据（实际应该从后端获取）
    defaultSubCategories: {
      // 餐饮分类的子分类
      1: [
        { id: 11, name: '三餐', icon: '/static/icons/meal.png' },
        { id: 12, name: '调味油盐', icon: '/static/icons/spice.png' },
        { id: 13, name: '食材', icon: '/static/icons/ingredient.png' },
        { id: 14, name: '零食', icon: '/static/icons/snack.png' },
        { id: 15, name: '奶茶', icon: '/static/icons/milk-tea.png' },
        { id: 16, name: '咖啡', icon: '/static/icons/coffee.png' }
      ],
      // 其他默认子分类
      2: [
        { id: 21, name: '调料', icon: '/static/icons/spice.png' },
        { id: 22, name: '大米', icon: '/static/icons/rice.png' },
        { id: 23, name: '油', icon: '/static/icons/oil.png' }
      ],
      // 收入分类的子分类
      101: [
        { id: 1011, name: '月薪', icon: '/static/icons/salary.png' },
        { id: 1012, name: '年薪', icon: '/static/icons/annual-salary.png' }
      ]
    },

    // 账单迁移相关数据
    showMigrationPopup: false, // 是否显示账单迁移弹窗
    migrationData: {
      categoryId: null, // 待迁移的分类ID
      categoryName: '', // 待迁移的分类名称
      categoryIcon: '', // 待迁移的分类图标
      imageType: '', // 图标类型
      iconContent: '', // 图标内容
      targetCategoryId: null, // 目标分类ID
      targetCategoryName: '' // 目标分类名称
    },

    // 分类选择器相关数据
    showCategorySelector: false, // 是否显示分类选择器
    availableCategories: [], // 可选择的分类列表

    // 更多选项弹窗相关数据
    currentCategoryInfo: null, // 当前选中的分类信息
    currentCategoryName: '', // 当前选中的分类名称
    isSubCategory: false, // 是否是子分类
    parentCategoryId: null // 父分类ID（当选中子分类时）
  },

  /**
   * 生命周期函数--监听页面加载
   */
  onLoad: function (options) {
    console.log(options,'options');

    // 获取系统信息
    this.getSystemInfo();

    // 获取账本ID
    if (options.id) {
      this.setData({
        accountbook_id: options.id
      });
      console.log('获取到账本ID:', options.id);
    }

    // 根据参数设置初始标签
    if (options.type && options.type === 'income') {
      this.setData({
        activeTab: 'income'
      });
    }

    // 设置需要加载数据标志
    this.setData({
      needReload: true
    });
  },
  onShow: function() {
    // 只有在需要重新加载数据时才调用loadCategories
    if (this.data.needReload) {
      // console.log('需要重新加载数据');

      // 加载分类数据
      this.loadCategories();
      // 重置标志
      this.setData({
        needReload: false
      });
    }
  },

  /**
   * 获取系统信息，设置导航栏高度
   */
  getSystemInfo: function() {
    try {
      const systemInfo = wx.getSystemInfoSync();
      // 状态栏高度
      const statusBarHeight = systemInfo.statusBarHeight;
      // 导航栏高度，默认44px
      const navBarHeight = 44;

      this.setData({
        statusBarHeight: statusBarHeight,
        navBarHeight: navBarHeight
      });
    } catch (e) {
      console.error('获取系统信息失败', e);
    }
  },

  /**
   * 加载分类数据
   */
  loadCategories: function () {
    // 构建请求参数
    const params = {
      accountbook_id: this.data.accountbook_id || '',
      type: this.data.activeTab === 'expenses' ? 'expenses' : 'income',
      // 不传父类ID，获取全部分类
    };

    console.log('加载分类数据，请求参数:', params);

    // 调用接口获取分类数据
    getIncomeCategoryList(params).then(res => {
      if (res && res.data) {
        // 确保所有分类的初始状态为折叠
        const categoriesData = res.data.map(item => {
          item.isExpanded = false;

          // 处理图标类型
          if (item.image) {
            // 检查是否是文字图标
            if (typeof item.image === 'string' && item.image.startsWith('text:')) {
              item.imageType = 'text';
              item.iconContent = item.image.substring(5); // 去掉'text:'前缀
            }
            // 检查是否是emoji图标
            else if (typeof item.image === 'string' && item.image.startsWith('emoji:')) {
              item.imageType = 'emoji';
              item.iconContent = item.image.substring(6); // 去掉'emoji:'前缀
            }
            // 检查是否是base64格式图片
            else if (typeof item.image === 'string' && item.image.startsWith('data:image/')) {
              item.imageType = 'image';
              // base64格式图片不需要通过getImageUrl处理
            }
            // 普通图片
            else {
              item.imageType = 'image';
              item.image = util.getImageUrl(item.image);
            }
          }

          // 处理子分类数据
          if (item.child && Array.isArray(item.child)) {
            item.child = item.child.map(childItem => {
              // 处理子分类图标类型
              if (childItem.image) {
                // 检查是否是文字图标
                if (typeof childItem.image === 'string' && childItem.image.startsWith('text:')) {
                  childItem.imageType = 'text';
                  childItem.iconContent = childItem.image.substring(5); // 去掉'text:'前缀
                }
                // 检查是否是emoji图标
                else if (typeof childItem.image === 'string' && childItem.image.startsWith('emoji:')) {
                  childItem.imageType = 'emoji';
                  childItem.iconContent = childItem.image.substring(6); // 去掉'emoji:'前缀
                }
                // 检查是否是base64格式图片
                else if (typeof childItem.image === 'string' && childItem.image.startsWith('data:image/')) {
                  childItem.imageType = 'image';
                  // base64格式图片不需要通过getImageUrl处理
                }
                // 普通图片
                else {
                  childItem.imageType = 'image';
                  childItem.image = util.getImageUrl(childItem.image);
                }
              }
              return childItem;
            });
          } else {
            item.child = []; // 确保child始终是数组
          }

          return item;
        });

        // 更新分类数据
        this.setData({
          categories: categoriesData,
          activeCategory: null
        });
      } else {
        // 接口调用失败时使用本地数据
        let categoriesData = this.data.activeTab === 'income'
          ? JSON.parse(JSON.stringify(this.data.expensesssssCategories))
          : JSON.parse(JSON.stringify(this.data.incomeCategories));

        // 确保所有分类的初始状态为折叠
        categoriesData = categoriesData.map(item => {
          item.isExpanded = false;

          return item;
        });

        this.setData({
          categories: categoriesData,
          activeCategory: null
        });
      }
    }).catch(err => {
      console.error('获取分类数据失败', err);

      // 接口调用出错时使用本地数据
      let categoriesData = this.data.activeTab === 'income'
        ? JSON.parse(JSON.stringify(this.data.expensesssssCategories))
        : JSON.parse(JSON.stringify(this.data.incomeCategories));

      // 确保所有分类的初始状态为折叠
      categoriesData = categoriesData.map(item => {
        item.isExpanded = false;
        return item;
      });

      this.setData({
        categories: categoriesData,
        activeCategory: null
      });
    });
  },

  /**
   * 切换分类的展开/折叠状态
   */
  toggleCategory: function(e) {
    // 获取被点击的分类ID
    const categoryId = e.currentTarget.dataset.id;

    // 打印日志，用于调试
    console.log('当前点击的分类ID:', categoryId);

    // 获取当前分类列表的副本
    const categories = JSON.parse(JSON.stringify(this.data.categories));

    // 遍历分类列表，设置展开/折叠状态
    categories.forEach(item => {
      if (item.id === categoryId) {
        // 如果是点击的分类，切换其展开状态
        item.isExpanded = !item.isExpanded;
        // console.log(`分类 ${item.id} 展开状态: ${item.isExpanded}`);
      } else {
        // 其他分类全部折叠（手风琴效果）
        item.isExpanded = false;
      }
    });

    // 更新页面数据
    this.setData({
      categories: categories,
      activeCategory: categories.find(item => item.isExpanded)?.id || null
    });

    // console.log('更新后的分类列表:', this.data.categories);
    // console.log('当前展开的分类ID:', this.data.activeCategory);
  },

  /**
   * 切换标签（支出/收入）
   */
  switchTab: function (e) {
    const tabType = e.currentTarget.dataset.type;
    if (this.data.activeTab !== tabType) {
      this.setData({
        activeTab: tabType,
        needReload: true
      });

      // 切换标签后重新加载分类
      this.loadCategories();

      // 重置标志，因为已经加载了数据
      this.setData({
        needReload: false
      });
    }
  },

  /**
   * 切换账本
   */
  switchBook: function () {
    wx.showActionSheet({
      itemList: ['默认账本', '生活账本', '工作账本'],
      success: (res) => {
        if (!res.cancel) {
          const selectedBook = ['默认账本', '生活账本', '工作账本'][res.tapIndex];
          this.setData({
            selectedBook: selectedBook,
            currentBook: selectedBook,
            needReload: true
          });
          // 切换账本后重新加载分类
          this.loadCategories();

          // 重置标志，因为已经加载了数据
          this.setData({
            needReload: false
          });
        }
      }
    });
  },

  /**
   * 编辑子分类
   */
  editSubCategory: function (e) {
    const subCategoryId = e.currentTarget.dataset.id;
    const parentId = e.currentTarget.dataset.parentId;
    console.log('编辑子分类，子分类ID:', subCategoryId, '父分类ID:', parentId);

    // 查找父分类
    const parentCategory = this.data.categories.find(item => item.id === parentId);
    if (parentCategory && parentCategory.child) {
      // 在父分类的子分类中查找当前分类
      const currentCategory = parentCategory.child.find(subItem => subItem.id === subCategoryId);

      if (currentCategory) {
        // 更新当前分类信息，并显示子分类选项弹窗
        this.setData({
          showMorePopup: true,
          currentCategoryId: subCategoryId,
          currentCategoryInfo: currentCategory,
          currentCategoryName: currentCategory.name || '未命名分类',
          isSubCategory: true,
          parentCategoryId: parentId
        });
      } else {
        console.error('未找到子分类信息，subCategoryId:', subCategoryId);
        wx.showToast({
          title: '未找到子分类信息',
          icon: 'none'
        });
      }
    } else {
      console.error('未找到父分类信息，parentId:', parentId);
      wx.showToast({
        title: '未找到父分类信息',
        icon: 'none'
      });
    }

    // 阻止事件冒泡，避免触发父元素的点击事件
    // e.stopPropagation();
  },

  /**
   * 添加子分类
   */
  addSubCategory: function (e) {
    const parentId = e.currentTarget.dataset.parentId;
    console.log(e.currentTarget.dataset,'e.currentTarget.dataset');

    // 设置需要重新加载数据标志
    this.setData({
      needReload: true
    });

    wx.navigateTo({
      url: `/packageA/pages/bookSetting/addSubCategory/addSubCategory?type=${this.data.activeTab}&id=${parentId}&accountbook_id=${this.data.accountbook_id}`
    });

    // 阻止事件冒泡，避免触发父元素的点击事件
    // e.stopPropagation();
  },

  /**
   * 显示更多选项弹出层
   */
  showMoreOptions: function (e) {
    // console.log('showMoreOptions 事件对象:', e);
    // console.log('dataset:', e.currentTarget.dataset);

    const categoryId = e.currentTarget.dataset.id;
    const parentId = e.currentTarget.dataset.parentId;

    console.log('显示更多选项，分类ID:', categoryId, '父分类ID:', parentId);

    let currentCategory = null;
    let isSubCategory = false;

    // 判断是否为子分类
    if (parentId) {
      isSubCategory = true;
      // 查找父分类
      const parentCategory = this.data.categories.find(item => item.id === parentId);
      if (parentCategory && parentCategory.child) {
        // 在父分类的子分类中查找当前分类
        currentCategory = parentCategory.child.find(subItem => subItem.id === categoryId);
      }
    } else {
      // 查找主分类
      currentCategory = this.data.categories.find(item => item.id === categoryId);
    }

    console.log('找到的分类信息:', currentCategory);

    if (!currentCategory) {
      console.error('未找到分类信息，categoryId:', categoryId);
      wx.showToast({
        title: '未找到分类信息',
        icon: 'none'
      });
      return;
    }

    // 更新当前分类信息
    this.setData({
      showMorePopup: true,
      currentCategoryId: categoryId,
      currentCategoryInfo: currentCategory,
      currentCategoryName: currentCategory.name || '未命名分类',
      isSubCategory: isSubCategory,
      parentCategoryId: parentId || null
    });
  },

  /**
   * 关闭更多选项弹出层
   */
  closeMorePopup: function() {
    this.setData({
      showMorePopup: false,
      currentCategoryId: null
    });
  },

  /**
   * 备份子分类
   */
  backupSubCategory: function() {
    // 先保存当前分类ID，再关闭弹出层
    const parentId = this.data.currentCategoryId;
    console.log(`为分类 ID: ${parentId} 添加子分类`);
    this.closeMorePopup();

    if (!parentId) {
      wx.showToast({
        title: '分类ID获取失败',
        icon: 'none'
      });
      return;
    }

    // 设置需要重新加载数据标志
    this.setData({
      needReload: true
    });

    // 导航到添加子分类页面，传递activeTab、parentId和accountbook_id参数
    wx.navigateTo({
      url: `/packageA/pages/bookSetting/addSubCategory/addSubCategory?type=${this.data.activeTab}&id=${parentId}&accountbook_id=${this.data.accountbook_id}`
    });
  },

  /**
   * 编辑分类
   */
  editCategory: function() {
    // 先保存当前分类ID，再关闭弹出层
    const categoryId = this.data.currentCategoryId;
    console.log('编辑分类，分类ID:', categoryId);
    this.closeMorePopup();

    // if (!categoryId) {
    //   wx.showToast({
    //     title: '分类ID获取失败',
    //     icon: 'none'
    //   });
    //   return;
    // }

    // 设置需要重新加载数据标志
    this.setData({
      needReload: true
    });

    // 导航到编辑分类页面，传递activeTab、categoryId和accountbook_id参数
    wx.navigateTo({
      url: `/packageA/pages/categoryEdit/categoryEdit?type=${this.data.activeTab}&id=${categoryId}&accountbook_id=${this.data.accountbook_id}`
    });
  },

  /**
   * 弹出层的编辑分类
   */
  editCategoryName: function() {
    // 先保存当前分类ID，再关闭弹出层
    const categoryId = this.data.currentCategoryId;
    console.log('编辑分类名称，分类ID:', categoryId);
    this.closeMorePopup();

    // if (!categoryId) {
    //   wx.showToast({
    //     title: '分类ID获取失败',
    //     icon: 'none'
    //   });
    //   return;
    // }

    // 设置需要重新加载数据标志
    this.setData({
      needReload: true
    });

    // 导航到编辑页面，传递activeTab、categoryId和accountbook_id参数，并添加isEdit=true参数
    wx.navigateTo({
      url: `/packageA/pages/bookSetting/addSubCategory/addSubCategory?type=${this.data.activeTab}&id=${categoryId}&accountbook_id=${this.data.accountbook_id}&isEdit=true`
    });
  },

  /**
   * 分类排序
   */
  sortCategory: function() {
    const categoryId = this.data.currentCategoryId;
    console.log('分类排序，分类ID:', categoryId);
    this.closeMorePopup();

    // 设置需要重新加载数据标志
    this.setData({
      needReload: true
    });

    // 导航到分类排序页面，传递activeTab和accountbook_id参数，pid=0表示主分类排序
    wx.navigateTo({
      url: `/packageA/pages/bookSetting/incomeCategory/sortCategory/sortCategory?type=${this.data.activeTab}&accountbook_id=${this.data.accountbook_id}&pid=0`
    });
  },

  /**
   * 子分类排序
   */
  sortSubCategory: function() {
    // 优先使用parentCategoryId，如果没有则使用currentCategoryId
    const parentId = this.data.parentCategoryId || this.data.currentCategoryId;
    console.log('子分类排序，父分类ID:', this.data.parentCategoryId, '当前分类ID:', this.data.currentCategoryId, '最终使用ID:', parentId);
    this.closeMorePopup();

    if (!parentId) {
      wx.showToast({
        title: '分类ID获取失败',
        icon: 'none'
      });
      return;
    }

    // 设置需要重新加载数据标志
    this.setData({
      needReload: true
    });

    // 导航到分类排序页面，传递activeTab、accountbook_id和pid参数，pid为父分类ID
    wx.navigateTo({
      url: `/packageA/pages/bookSetting/incomeCategory/sortCategory/sortCategory?type=${this.data.activeTab}&accountbook_id=${this.data.accountbook_id}&pid=${parentId}`
    });
  },

  /**
   * 批量编辑
   */
  batchEdit: function() {
    this.closeMorePopup();
    wx.showToast({
      title: '批量编辑功能',
      icon: 'none'
    });
  },

  /**
   * 删除分类（从弹出层调用）
   */
  deleteCategoryFromPopup: function() {
    // 先保存当前分类ID，再关闭弹出层
    const categoryId = this.data.currentCategoryId;
    console.log('删除分类，分类ID:', categoryId);
    this.closeMorePopup();

    // if (!categoryId) {
    //   wx.showToast({
    //     title: '分类ID获取失败',
    //     icon: 'none'
    //   });
    //   return;
    // }

    this.deleteCategory(categoryId);
  },

  /**
   * 删除分类
   */
  deleteCategory: function (categoryId) {
    wx.showModal({
      title: '提示',
      content: '确定要删除该分类吗？删除后无法恢复。',
      success: (res) => {
        if (res.confirm) {
          // 这里仅是演示，实际应该调用API删除
          let categories = [...this.data.categories];
          categories = categories.filter(item => item.id !== categoryId);

          // 更新分类列表
          this.setData({
            categories: categories
          });

          // 同时更新源数据
          const sourceKey = this.data.activeTab === 'income' ? 'expensesCategories' : 'incomeCategories';
          const sourceData = [...this.data[sourceKey]].filter(item => item.id !== categoryId);

          const updateData = {};
          updateData[sourceKey] = sourceData;

          this.setData(updateData);

          wx.showToast({
            title: '分类已删除',
            icon: 'success'
          });
        }
      }
    });
  },

  /**
   * 添加分类
   */
  addCategory: function () {
    // 设置需要重新加载数据标志
    this.setData({
      needReload: true
    });

    // 导航到添加子分类页面，传递activeTab和accountbook_id参数，并添加isMain=true参数表示添加主分类
    wx.navigateTo({
      url: `/packageA/pages/bookSetting/addSubCategory/addSubCategory?type=${this.data.activeTab}&accountbook_id=${this.data.accountbook_id}&isMain=true`
    });
  },

  /**
   * 返回上一页
   */
  goBack: function () {
    wx.navigateBack();
  },

  /**
   * 设为主分类
   */
  setAsMainCategory: function() {
    // 先保存当前分类ID和信息，再关闭弹出层
    const categoryId = this.data.currentCategoryId;
    const currentCategory = this.data.currentCategoryInfo;
    const parentId = this.data.parentCategoryId;

    console.log('设为主分类，分类ID:', categoryId, '父分类ID:', parentId);
    this.closeMorePopup();

    if (!categoryId || !currentCategory) {
      wx.showToast({
        title: '分类信息获取失败',
        icon: 'none'
      });
      return;
    }

    // 显示加载中
    wx.showLoading({
      title: '处理中...',
    });

    // 构建请求参数
    const params = {
      // 分类ID，根据当前分类信息获取
      category_id: categoryId,
      pid: 0, // 设置为0表示改为主分类
      // 名称，根据当前分类信息获取
      name: currentCategory.name,
      // 类型，根据activeTab确定
      type: this.data.activeTab,
      // 账本id
      accountbook_id: this.data.accountbook_id
    };

    // 添加图标参数
    if (currentCategory.image) {
      params.image = currentCategory.image;
    }

    console.log('设为主分类参数:', params);

    // 调用编辑分类API
    editCategory(params).then(res => {
      wx.hideLoading();

      if (res && res.code === 1) {
        wx.showToast({
          title: '设置成功',
          icon: 'success'
        });

        // 设置需要重新加载数据标志
        this.setData({
          needReload: true
        });

        // 重新加载分类数据
        this.loadCategories();
      } else {
        wx.showToast({
          title: res.msg || '设置失败',
          icon: 'none'
        });
      }
    }).catch(err => {
      wx.hideLoading();

      wx.showToast({
        title: '网络请求失败',
        icon: 'none'
      });

      console.error('设为主分类失败:', err);
    });
  },

  /**
   * 从弹窗中编辑子分类
   */
  editSubCategoryFromPopup: function(e) {
    const subCategoryId = e.currentTarget.dataset.id;
    const parentId = e.currentTarget.dataset.parentId;
    console.log('从弹窗编辑子分类，子分类ID:', subCategoryId, '父分类ID:', parentId);

    // 关闭弹窗
    this.closeMorePopup();

    // 设置需要重新加载数据标志
    this.setData({
      needReload: true
    });

    // 导航到编辑子分类页面，传递activeTab、subCategoryId、parentId和accountbook_id参数，并添加isEditSub=true参数
    wx.navigateTo({
      url: `/packageA/pages/bookSetting/addSubCategory/addSubCategory?type=${this.data.activeTab}&id=${subCategoryId}&parentId=${parentId}&accountbook_id=${this.data.accountbook_id}&isEditSub=true`
    });
  },

  /**
   * 显示账单迁移弹窗
   */
  showMigrationPopup: function() {
    const currentCategory = this.data.currentCategoryInfo;
    const categoryId = this.data.currentCategoryId;
    const categoryName = this.data.currentCategoryName;

    console.log('显示账单迁移弹窗，分类信息:', currentCategory);

    if (!currentCategory || !categoryId) {
      wx.showToast({
        title: '分类信息获取失败',
        icon: 'none'
      });
      return;
    }

    // 关闭更多选项弹窗
    this.closeMorePopup();

    // 设置迁移数据
    const migrationData = {
      categoryId: categoryId,
      categoryName: categoryName,
      categoryIcon: currentCategory.image || '/static/icon/set.png',
      imageType: currentCategory.imageType || 'image',
      iconContent: currentCategory.iconContent || '',
      targetCategoryId: null,
      targetCategoryName: ''
    };

    // 显示账单迁移弹窗
    this.setData({
      showMigrationPopup: true,
      migrationData: migrationData
    });
  },

  /**
   * 关闭账单迁移弹窗
   */
  closeMigrationPopup: function() {
    this.setData({
      showMigrationPopup: false,
      migrationData: {
        categoryId: null,
        categoryName: '',
        categoryIcon: '',
        imageType: '',
        iconContent: '',
        targetCategoryId: null,
        targetCategoryName: ''
      }
    });
  },

  /**
   * 选择迁移目标分类
   */
  selectMigrationTarget: function() {
    // 获取当前分类数据，排除待迁移的分类
    const currentCategoryId = this.data.migrationData.categoryId;
    const allCategories = JSON.parse(JSON.stringify(this.data.categories)); // 深拷贝避免修改原数据

    // 过滤掉待迁移的分类，避免迁移到自己
    const availableCategories = allCategories.filter(category => {
      if (category.id === currentCategoryId) {
        return false; // 排除待迁移的主分类
      }

      // 如果有子分类，也要过滤掉待迁移的子分类
      if (category.child && category.child.length > 0) {
        category.child = category.child.filter(subCategory => subCategory.id !== currentCategoryId);
      }

      return true;
    });

    // 调试：打印分类数据，检查图标信息
    availableCategories.forEach((category, index) => {
      console.log(`分类 ${index}:`, {
        id: category.id,
        name: category.name,
        image: category.image,
        imageType: category.imageType,
        iconContent: category.iconContent
      });

      if (category.child && category.child.length > 0) {
        category.child.forEach((subCategory, subIndex) => {
          console.log(`  子分类 ${subIndex}:`, {
            id: subCategory.id,
            name: subCategory.name,
            image: subCategory.image,
            imageType: subCategory.imageType,
            iconContent: subCategory.iconContent
          });
        });
      }
    });

    // 设置可选择的分类数据并显示选择器
    this.setData({
      availableCategories: availableCategories,
      showCategorySelector: true
    });
  },

  /**
   * 关闭分类选择器
   */
  closeCategorySelector: function() {
    this.setData({
      showCategorySelector: false,
      availableCategories: []
    });
  },

  /**
   * 选择目标分类
   */
  selectTargetCategory: function(e) {
    const categoryId = e.currentTarget.dataset.id;
    const categoryName = e.currentTarget.dataset.name;
    const categoryType = e.currentTarget.dataset.type;
    const parentName = e.currentTarget.dataset.parentName;

    console.log('选择目标分类:', { categoryId, categoryName, categoryType, parentName });

    // 构建显示名称
    let displayName = categoryName;
    if (categoryType === 'sub' && parentName) {
      displayName = `${parentName} - ${categoryName}`;
    }

    // 更新迁移数据
    const migrationData = { ...this.data.migrationData };
    migrationData.targetCategoryId = categoryId;
    migrationData.targetCategoryName = displayName;

    this.setData({
      migrationData: migrationData
    });

    // 关闭分类选择器
    this.closeCategorySelector();

    wx.showToast({
      title: `已选择：${displayName}`,
      icon: 'success',
      duration: 1500
    });
  },

  /**
   * 确认迁移
   */
  confirmMigration: function() {
    const migrationData = this.data.migrationData;

    if (!migrationData.targetCategoryId) {
      wx.showToast({
        title: '请选择目标分类',
        icon: 'none'
      });
      return;
    }

    // 显示确认对话框
    wx.showModal({
      title: '确认迁移',
      content: `确定要将「${migrationData.categoryName}」分类下的账单迁移到「${migrationData.targetCategoryName}」吗？`,
      success: (res) => {
        if (res.confirm) {
          // 执行迁移操作
          this.performMigration();
        }
      }
    });
  },

  /**
   * 执行账单迁移
   */
  performMigration: function() {
    const migrationData = this.data.migrationData;

    wx.showLoading({
      title: '迁移中...'
    });

    // 这里应该调用实际的迁移API
    // 目前只是模拟操作
    setTimeout(() => {
      wx.hideLoading();

      wx.showToast({
        title: '迁移成功',
        icon: 'success'
      });

      // 关闭迁移弹窗
      this.closeMigrationPopup();

      // 重新加载分类数据
      this.setData({
        needReload: true
      });
      this.loadCategories();
    }, 2000);
  }
});
