// pages/login/verifyCode/verifyCode.js
const { register } = require('../../../api/login/index');

Page({
  data: {
    verifyCode: '', // 验证码字符串
    codeArr: [], // 验证码数组，用于显示
    isFocus: true, // 是否聚焦输入框
    activeIndex: 0, // 当前激活的输入框索引
    countdown: 48, // 倒计时秒数
    nickname: '', // 用户昵称
    phone: '', // 手机号
    password: '', // 密码
    showSuccessDialog: false // 是否显示注册成功弹窗
  },

  onLoad: function(options) {
    // 获取从注册页面传递过来的参数
    if (options) {
      this.setData({
        nickname: decodeURIComponent(options.nickname || ''),
        phone: decodeURIComponent(options.phone || ''),
        password: decodeURIComponent(options.password || '')
      });
    }
    
    // 开始倒计时
    this.startCountdown();
  },

  // 开始倒计时
  startCountdown: function() {
    const timer = setInterval(() => {
      if (this.data.countdown > 0) {
        this.setData({
          countdown: this.data.countdown - 1
        });
      } else {
        clearInterval(timer);
      }
    }, 1000);
    this.timer = timer;
  },

  // 聚焦输入框
  focusInput: function() {
    this.setData({
      isFocus: true
    });
  },

  // 处理输入
  onInput: function(e) {
    const value = e.detail.value;
    
    // 将输入的值转为数组
    const codeArr = value.split('');
    
    // 更新激活的输入框索引
    const activeIndex = value.length < 4 ? value.length : 3;
    
    this.setData({
      verifyCode: value,
      codeArr: codeArr,
      activeIndex: activeIndex
    });
  },

  // 清除验证码
  clearCode: function() {
    this.setData({
      verifyCode: '',
      codeArr: [],
      activeIndex: 0,
      isFocus: true
    });
  },

  // 确认验证码
  confirmCode: function() {
    if (this.data.verifyCode.length !== 4) {
      wx.showToast({
        title: '请输入4位验证码',
        icon: 'none'
      });
      return;
    }
    
    // 显示加载中
    wx.showLoading({
      title: '注册中...',
    });
    
    // 准备注册参数
    const registerData = {
      nickname: this.data.nickname,
      mobile: this.data.phone,
      password: this.data.password,
      repeat: this.data.password,
      captcha: this.data.verifyCode
      // from_user_id 可选，暂不传递
    };
    
    // 调用注册接口
    register(registerData).then(response => {
      wx.hideLoading();
      
      if (response.code === 1) {
        // 注册成功，保存用户信息
        const userInfo = response.data.userinfo;
        wx.setStorageSync('token', userInfo.token);
        wx.setStorageSync('userInfo', userInfo);
        
        // 显示成功弹窗
        this.setData({
          showSuccessDialog: true
        });
      } else {
        // 注册失败
        wx.showToast({
          title: response.msg || '注册失败',
          icon: 'none'
        });
      }
    }).catch(error => {
      wx.hideLoading();
      wx.showToast({
        title: '注册失败，请稍后重试',
        icon: 'none'
      });
      console.error('注册失败:', error);
    });
  },

  // 关闭成功弹窗并跳转到登录页
  closeSuccessDialog: function() {
    this.setData({
      showSuccessDialog: false
    });
    
    // 跳转到手机号登录页面
    wx.navigateTo({
      url: '/pages/login/phoneLogin/phoneLogin'
    });
  },

  // 返回上一页
  goBack: function() {
    wx.navigateBack();
  },

  onUnload: function() {
    // 页面卸载时清除定时器
    if (this.timer) {
      clearInterval(this.timer);
    }
  }
});