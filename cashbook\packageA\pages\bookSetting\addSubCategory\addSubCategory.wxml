<!--packageA/pages/bookSetting/addSubCategory/addSubCategory.wxml-->
<view class="container">
  <!-- 顶部导航栏 -->
  <!-- <view class="nav-bar">
    <view class="back-icon" bindtap="goBack">
      <van-icon name="arrow-left" size="20px" color="#333" />
    </view>
    <view class="page-title">添加子分类</view>
    <view class="right-btn">
      <view class="custom-btn" bindtap="onCustomize">自定义</view>
    </view>
  </view> -->

  <!-- 一级分类显示 -->
  <view class="primary-category" wx:if="{{!isEditMode && !isMainCategory}}">
    <view class="primary-category-title">一级分类</view>
    <view class="primary-category-content">
      <view class="primary-category-name">{{parentCategory || '加载中...'}}</view>
      <image class="primary-category-icon" src="{{parentCategoryIcon || '/static/icon/set.png'}}" mode="aspectFit"></image>
    </view>
  </view>

  <!-- 分类名称输入 -->
  <view class="input-section">
    <!-- <view class="section-title">分类名称</view> -->
    <input-field
        label="分类名称"
        value="{{categoryName}}"
        placeholder="限4个汉字或10个英文"
        field-name="categoryName"
        highlight-color="#59b5dd"
        right-icon="{{selectedIcon}}"
        maxlength="10"
        bind:input="onInput"
        bind:focus="onInputFocus"
        bind:blur="onInputBlur"
      />
  </view>

  <!-- 文字作为图标开关 -->
  <view class="switch-section">
    <view class="switch-title">
      <text>文字作为图标</text>
      <text class="switch-subtitle">开启后会将分类名称的首字作为图标</text>
    </view>
    <!-- <view class="switch-preview" wx:if="{{useTextAsIcon && categoryName}}">
      <view class="text-icon">{{textIconContent || (categoryName ? categoryName.charAt(0) : '')}}</view>
    </view> -->
    <switch checked="{{useTextAsIcon}}" bindchange="onSwitchChange" color="#4395ff" />
  </view>

  <!-- 提示文本 -->
  <view class="tips-text">
    <text>可以用emoji😊开头，图标自动为emoji😊图标。</text>
    <text>选择分类图标，名称将自动填充，自己也可以修改分类名称哦~</text>
    <text>如出现图标不显示情况，请更新到最新版本即可~PS: 暂不支持自定义颜色emoji</text>
  </view>

  <!-- 图标选择区域 -->
  <view class="category-section">
    <!-- 分类标签选择 -->
    <view class="category-tabs">
      <scroll-view scroll-x="true" class="category-tabs-scroll">
        <view 
          wx:for="{{categoryList}}" 
          wx:key="id" 
          class="category-tab {{currentCategoryIndex === index ? 'active' : ''}}"
          bindtap="switchCategory"
          data-index="{{index}}"
        >
          {{item.name}}
        </view>
      </scroll-view>
    </view>
    
    <!-- 图标网格 -->
    <view class="icon-grid">
      <block wx:if="{{currentCategoryIndex !== null && categoryList[currentCategoryIndex].child && categoryList[currentCategoryIndex].child.length > 0}}">
        <view 
          class="icon-item" 
          wx:for="{{categoryList[currentCategoryIndex].child}}" 
          wx:key="id" 
          bindtap="selectSubCategoryIcon" 
          data-index="{{index}}"
        >
          <view class="icon-wrapper">
            <!-- 根据图标类型显示不同内容 -->
            <block wx:if="{{item.imageType === 'text'}}">
              <view class="text-icon">{{item.iconContent}}</view>
            </block>
            <block wx:elif="{{item.imageType === 'emoji'}}">
              <view class="emoji-icon">{{item.iconContent}}</view>
            </block>
            <block wx:else>
              <image src="{{item.image}}" mode="aspectFit"></image>
            </block>
          </view>
          <text class="icon-name">{{item.name}}</text>
        </view>
      </block>
      <view class="empty-tip" wx:if="{{currentCategoryIndex === null || !categoryList[currentCategoryIndex].child || categoryList[currentCategoryIndex].child.length === 0}}">
        <text>该分类下暂无图标</text>
      </view>
    </view>
  </view>

  <!-- 底部保存按钮 -->
  <view class="save-button" bindtap="saveCategory">保存</view>
  
  <!-- 底部固定的自定义按钮 -->
  <view class="custom-button-container">
    <view class="custom-button" bindtap="onCustomize">
      <view class="custom-button-icon">
        <image src="/static/icon/crown.png" mode="aspectFit"></image>
      </view>
      <text class="custom-button-text">自定义</text>
    </view>
  </view>
</view>