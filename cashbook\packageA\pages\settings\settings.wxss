/* 整体容器 */
.container {
  display: flex;
  flex-direction: column;
  min-height: 100vh;
  background-color: #f5f5f5;
  padding-bottom: 60px;
}



/* 设置区域 */
.section {
  margin: 10px 0;
  background-color: #fff;
}

.section-header {
  display: flex;
  align-items: center;
  padding: 15px;
}

.section-indicator {
  width: 3px;
  height: 16px;
  background-color: #4CAF50;
  margin-right: 8px;
  border-radius: 1.5px;
}

.section-title {
  font-size: 14px;
  color: #333;
}

/* 设置项 */
.setting-item {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 15px;
  border-bottom: 1px solid #f5f5f5;
}

.setting-item:last-child {
  border-bottom: none;
}

.item-left {
  display: flex;
  align-items: center;
}

.item-icon {
  width: 24px;
  height: 24px;
  margin-right: 10px;
}

.item-icon image {
  width: 100%;
  height: 100%;
}

.item-text {
  font-size: 14px;
  color: #333;
}

.item-right {
  width: 16px;
  height: 16px;
}

.item-right image {
  width: 100%;
  height: 100%;
}

/* 退出登录按钮 */
.logout-button {
  margin: 20px 15px;
  height: 44px;
  background-color: #a5d6a7;
  border-radius: 22px;
  display: flex;
  justify-content: center;
  align-items: center;
  color: #fff;
  font-size: 16px;
}

/* 底部导航栏 */
.tab-bar {
  position: fixed;
  bottom: 0;
  left: 0;
  right: 0;
  height: 50px;
  background-color: #fff;
  display: flex;
  justify-content: space-around;
  align-items: center;
  border-top: 1px solid #f0f0f0;
}

.tab-item {
  display: flex;
  justify-content: center;
  align-items: center;
  width: 24px;
  height: 24px;
}

.tab-item image {
  width: 100%;
  height: 100%;
}