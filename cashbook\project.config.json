{"compileType": "miniprogram", "libVersion": "3.8.2", "packNpmManually": true, "packNpmRelationList": [{"packageJsonPath": "./package.json", "miniprogramNpmDistDir": "./miniprogram/"}], "setting": {"coverView": true, "es6": true, "postcss": true, "minified": true, "enhance": true, "showShadowRootInWxmlPanel": true, "packNpmRelationList": [], "babelSetting": {"ignore": [], "disablePlugins": [], "outputPath": ""}, "ignoreUploadUnusedFiles": true, "compileWorklet": false, "uglifyFileName": false, "uploadWithSourceMap": true, "packNpmManually": false, "minifyWXSS": true, "minifyWXML": true, "localPlugins": false, "disableUseStrict": false, "useCompilerPlugins": ["sass"], "condition": false, "swc": false, "disableSWC": true}, "condition": {}, "editorSetting": {"tabIndent": "auto", "tabSize": 2}, "appid": "wx890b4e22426c6458", "packOptions": {"ignore": [], "include": []}, "simulatorPluginLibVersion": {}}