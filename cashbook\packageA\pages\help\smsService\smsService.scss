/* packageA/pages/help/smsService/smsService.wxss */

// Variables
$primary-color: #0052d9;
$primary-gradient: linear-gradient(135deg, $primary-color, #4080ff);
$header-gradient: linear-gradient(90deg, $primary-color, #4080ff);
$card-bg: #fff;
$light-bg: #f7f9fc;
$text-dark: #333;
$text-light: #666;
$text-lighter: #999;

// Shadows
$shadow-sm: 0 2rpx 8rpx rgba(0, 0, 0, 0.05);
$shadow-md: 0 4rpx 16rpx rgba(0, 0, 0, 0.08);
$shadow-lg: 0 8rpx 24rpx rgba(0, 0, 0, 0.12);

// Radii
$radius-sm: 8rpx;
$radius-md: 16rpx;
$radius-lg: 24rpx;

// Base styles
page {
  background-color: $light-bg;
}

// Override TDesign collapse styles - add this at the top to ensure it has priority
.t-collapse-panel__content--expanded.t-collapse-panel__content--bottom::after {
  display: none !important;
}

.t-collapse-panel__content {
  margin-bottom: 0 !important;
  padding-bottom: 0 !important;
}

.category-container {
  display: flex;
  flex-direction: column;
  min-height: 100vh;
  
  // Gradient background - shortened to keep tabs visible
  &::before {
    content: '';
    position: fixed;
    top: 0;
    left: 0;
    right: 0;
    height: 200rpx;
    background: $primary-gradient;
    z-index: -1;
  }
}

/* TDesign Tabs 样式增强 */
.custom-tabs {
  --td-tab-item-active-color: #{$primary-color};
  --td-tab-track-color: $primary-color;
  background-color: #fff;
  margin-bottom: 30rpx;
  padding: 20rpx 20rpx 0;
  position: relative;
  z-index: 10;
  border-radius: 0 0 24rpx 24rpx;
  box-shadow: $shadow-md;

  .t-tabs__item {
    color: $text-dark;
    font-size: 32rpx;
    transition: all 0.3s ease;
    
    &--active {
      color: $primary-color !important;
      font-weight: 600 !important;
      transform: translateY(-4rpx);
    }
  }

  .t-tabs__track {
    background-color: $primary-color !important;
    height: 4rpx !important;
    border-radius: 4rpx;
  }
}

/* 内容区域容器 */
.category-content {
  flex: 1;
  padding: 0 24rpx 24rpx;
  position: relative;
  z-index: 1;
  margin-top: 20rpx;
}

/* 精美卡片设计 */
.category-section {
  margin-bottom: 40rpx;
  background-color: $card-bg;
  border-radius: $radius-lg;
  overflow: hidden;
  box-shadow: $shadow-md;
  transform-origin: center top;
  animation: slideInUp 0.5s cubic-bezier(0.23, 1, 0.32, 1) both;
  position: relative;
  
  // Animaiton delays for staggered effect
  &:nth-child(2) { animation-delay: 0.1s; }
  &:nth-child(3) { animation-delay: 0.2s; }
  &:nth-child(4) { animation-delay: 0.3s; }
  &:nth-child(5) { animation-delay: 0.4s; }
  
  // Top gradient bar
  // &::after {
  //   content: '';
  //   position: absolute;
  //   top: 0;
  //   left: 0;
  //   right: 0;
  //   height: 6rpx;
  //   background: $header-gradient;
  // }
}

.section-header {
  position: relative;
  padding: 28rpx 32rpx;
  border-bottom: 1rpx solid rgba(0, 0, 0, 0.05);
  display: flex;
  align-items: center;
}

.section-title {
  font-size: 32rpx;
  font-weight: 600;
  color: $text-dark;
  margin-left: 16rpx;
  position: relative;
  
  &::before {
    content: '';
    display: block;
    width: 8rpx;
    height: 32rpx;
    border-radius: 4rpx;
    background-color: $primary-color;
    position: absolute;
    left: -16rpx;
    top: 50%;
    transform: translateY(-50%);
  }
}

/* 分类网格布局增强 */
.category-grid {
  display: flex;
  flex-wrap: wrap;
  padding: 0rpx 16rpx;
  // justify-content: space-between;
}

.category-item {
  display: flex;
  flex-direction: column;
  align-items: center;
  width: 25%;
  // margin-bottom: 40rpx;
  transition: all 0.3s cubic-bezier(0.175, 0.885, 0.32, 1.275);
  
  &:active {
    transform: scale(0.92);
  }
  
  // Icon styling
  .category-icon {
    display: flex;
    justify-content: center;
    align-items: center;
    width: 100rpx;
    height: 100rpx;
    border-radius: 50%;
    background: linear-gradient(135deg, #f0f4ff, #eaeffe);
    margin-bottom: 16rpx;
    box-shadow: $shadow-sm, inset 0 2rpx 4rpx rgba(255, 255, 255, 0.8);
    position: relative;
    overflow: hidden;
    transition: all 0.3s ease;
    
    // Light effect
    &::before {
      content: '';
      position: absolute;
      top: -50%;
      left: -50%;
      width: 200%;
      height: 200%;
      background: radial-gradient(circle at center, rgba(255, 255, 255, 0.8) 0%, transparent 60%);
      opacity: 0;
      transform: scale(0.5);
      transition: opacity 0.6s, transform 0.6s;
      z-index: 1;
    }
    
    // Icon or text inside
    image {
      width: 56rpx;
      height: 56rpx;
      position: relative;
      z-index: 2;
      filter: drop-shadow(0 2rpx 4rpx rgba(0, 0, 0, 0.1));
    }
    
    text {
      font-size: 32rpx;
      color: $text-dark;
      font-weight: 500;
      position: relative;
      z-index: 2;
    }
  }
  
  // Hover/active states
  &:hover, &:active {
    .category-icon::before {
      opacity: 0.8;
      transform: scale(1);
    }
    
    .category-name {
      color: $primary-color;
    }
  }
  
  // Category label
  .category-name {
    font-size: 26rpx;
    color: $text-light;
    text-align: center;
    max-width: 160rpx;
    overflow: hidden;
    text-overflow: ellipsis;
    white-space: nowrap;
    transition: color 0.3s;
  }
}

/* Collapse组件增强 */
.collapse-container {
  margin-bottom: 16rpx;
  overflow: hidden;
  border-radius: $radius-sm;
  
  // TDesign Collapse overrides
  .t-collapse {
    --td-collapse-border-radius: #{$radius-sm};
    --td-collapse-content-padding: 0;
    --td-collapse-header-height: 96rpx;
    background-color: transparent;
    border-radius: $radius-sm;
    overflow: hidden;
  }
  
  .t-collapse-panel__header {
    // background-color: #f9fafc;
    padding: 0 24rpx;
    position: relative;
    transition: background-color 0.3s;
    
    &:active {
      background-color: #f0f4ff;
    }
  }
}

.collapse-content {
  padding: 24rpx 16rpx;
  // border-top: 1rpx solid rgba(0, 0, 0, 0.05);
}

/* 加载状态美化 */
.loading-container {
  display: flex;
  flex-direction: column;
  justify-content: center;
  align-items: center;
  height: 300rpx;
  
  .t-loading {
    color: $primary-color !important;
  }
}

/* 空状态美化 */
.empty-state {
  display: flex;
  justify-content: center;
  align-items: center;
  padding: 120rpx 0;
  
  .t-empty__description {
    color: $text-lighter !important;
    font-size: 28rpx !important;
  }
}

/* 高级动画效果 */
@keyframes slideInUp {
  from {
    opacity: 0;
    transform: translateY(40rpx);
  }
  to {
    opacity: 1;
    transform: translateY(0);
  }
}

/* 精美滚动条 */
::-webkit-scrollbar {
  width: 8rpx;
  background-color: transparent;
}

::-webkit-scrollbar-thumb {
  background-color: rgba($primary-color, 0.3);
  border-radius: 4rpx;
}

/* 支持深色模式 */
@media (prefers-color-scheme: dark) {
  // Dark mode variables
  $dark-bg: #121212;
  $dark-card: #222;
  $dark-surface: #2a2a2a;
  $dark-text: #e0e0e0;
  $dark-text-lighter: #b0b0b0;
  $dark-shadow-sm: 0 2rpx 8rpx rgba(0, 0, 0, 0.2);
  $dark-shadow-md: 0 4rpx 16rpx rgba(0, 0, 0, 0.25);
  $dark-shadow-lg: 0 8rpx 24rpx rgba(0, 0, 0, 0.3);
  
  page {
    background-color: $dark-bg;
  }

  .category-container::before {
    background: linear-gradient(135deg, #003089, #0052d9);
    opacity: 0.7;
  }
  
  .category-section {
    background-color: $dark-card;
    box-shadow: $dark-shadow-md;
  }
  
  .section-header {
    border-bottom: 1rpx solid rgba(255, 255, 255, 0.05);
  }
  
  .section-title {
    color: $dark-text;
  }
  
  .category-item {
    .category-icon {
      background: linear-gradient(135deg, #2a2a2a, #333);
      box-shadow: $dark-shadow-sm, inset 0 1rpx 2rpx rgba(255, 255, 255, 0.1);
      
      text {
        color: $dark-text;
      }
    }
    
    .category-name {
      color: $dark-text-lighter;
    }
  }
  
  .collapse-container {
    .t-collapse-panel__header {
      background-color: $dark-surface;
      
      &:active {
        background-color: #333;
      }
    }
  }
  
  .collapse-content {
    background-color: $dark-surface;
    // border-top: 1rpx solid rgba(255, 255, 255, 0.05);
  }
  
  .empty-state .t-empty__description {
    color: #aaa !important;
  }
}