import request from '../request';

/**
 * 获取分类列表
 * @param {Object} data - 请求参数
 * @param {String} data.accountbook_id - 账本ID
 * @param {Number} data.type - 分类类型(1:收入, 2:支出)
 * @param {String} data.pid - 父类ID，为空返回全部分类
 */
export function getCategoryList(data) {
  return request({
    url: 'book/category',
    method: 'POST',
    data: data
  });
}

/**
 * 添加分类
 * @param {Object} data - 请求参数
 * @param {String} data.accountbook_id - 账本ID，为空则是默认账本
 * @param {String} data.type - 类型（非必传）：income=收入，expenses=支出
 * @param {String} data.pid - 父类ID，0=无
 * @param {String} data.name - 分类名称
 * @param {Number} data.image - 分类图标
 */
export function addCategory(data) {
  return request({
    url: 'book/add_category',
    method: 'POST',
    data: data
  });
}

/**
 * 编辑分类
 * @param {Object} data - 请求参数
 * @param {String} data.category_id - 分类ID
 * @param {String} data.name - 分类名称
 * @param {String} data.icon - 分类图标
 * @param {Number} data.use_text_icon - 是否使用文字作为图标(0:否, 1:是)
 */
export function editCategory(data) {
  return request({
    url: 'book/edit_category',
    method: 'POST',
    data: data
  });
}

/**
 * 删除分类
 * @param {Object} data - 请求参数
 * @param {String} data.category_id - 分类ID
 */
export function deleteCategory(data) {
  return request({
    url: '/book/del_category',
    method: 'POST',
    data: data
  });
}

/**
 * 收支分类排序
 * @param {Object} data - 请求参数
 * @param {Array} data.category_ids - 分类ID数组
 */
export function sortCategory(data) {
  return request({
    url: 'book/sort_category',
    method: 'POST',
    data: data
  });
}

/**
 * 账单分类迁移
 * @param {Object} data - 请求参数
 * @param {String} data.category_id - 要迁移的分类ID
 * @param {String} data.migrate_id - 迁移到新分类的ID
 */
export function migrateBillCategory(data) {
  return request({
    url: 'book/bill_migrate_category',
    method: 'POST',
    data: data
  });
}