<view class="month-picker-container" wx:if="{{visible}}">
  <view class="month-picker-mask" bindtap="onCancel"></view>
  <view class="month-picker-content">
    <!-- 顶部年份选择区域 -->
    <view class="year-selector"
          bindtouchstart="touchStart"
          bindtouchmove="touchMove"
          bindtouchend="touchEnd">
      <view class="current-year">{{currentYear}}年{{currentMonth}}月</view>
      <view class="year-display">
        <view class="year">{{currentYear}}年</view>
        <view class="year-controls">
          <view class="year-control up" bindtap="prevYear">
            <text class="arrow">^</text>
          </view>
          <view class="year-control down" bindtap="nextYear">
            <text class="arrow">v</text>
          </view>
        </view>
      </view>
    </view>

    <!-- 月份网格作为垂直滑动的swiper -->
    <swiper class="month-swiper" vertical="true" duration="300" display-multiple-items="1" skip-hidden-item-layout="true" easing-function="easeInOutCubic">
      <swiper-item>
        <view class="month-grid">
          <!-- 第一行 -->
          <view
            class="month-item {{currentMonth === 1 ? 'active' : ''}} {{!isMonthSelectable(currentYear, 1) ? 'disabled' : ''}}"
            data-month="1"
            bindtap="selectMonth">1月</view>
          <view
            class="month-item {{currentMonth === 2 ? 'active' : ''}} {{!isMonthSelectable(currentYear, 2) ? 'disabled' : ''}}"
            data-month="2"
            bindtap="selectMonth">2月</view>
          <view
            class="month-item {{currentMonth === 3 ? 'active' : ''}} {{!isMonthSelectable(currentYear, 3) ? 'disabled' : ''}}"
            data-month="3"
            bindtap="selectMonth">3月</view>
          <view
            class="month-item {{currentMonth === 4 ? 'active' : ''}} {{!isMonthSelectable(currentYear, 4) ? 'disabled' : ''}}"
            data-month="4"
            bindtap="selectMonth">4月</view>

          <!-- 第二行 -->
          <view
            class="month-item {{currentMonth === 5 ? 'active' : ''}} {{!isMonthSelectable(currentYear, 5) ? 'disabled' : ''}}"
            data-month="5"
            bindtap="selectMonth">5月</view>
          <view
            class="month-item {{currentMonth === 6 ? 'active' : ''}} {{!isMonthSelectable(currentYear, 6) ? 'disabled' : ''}}"
            data-month="6"
            bindtap="selectMonth">6月</view>
          <view
            class="month-item {{currentMonth === 7 ? 'active' : ''}} {{!isMonthSelectable(currentYear, 7) ? 'disabled' : ''}}"
            data-month="7"
            bindtap="selectMonth">7月</view>
          <view
            class="month-item {{currentMonth === 8 ? 'active' : ''}} {{!isMonthSelectable(currentYear, 8) ? 'disabled' : ''}}"
            data-month="8"
            bindtap="selectMonth">8月</view>

          <!-- 第三行 -->
          <view
            class="month-item {{currentMonth === 9 ? 'active' : ''}} {{!isMonthSelectable(currentYear, 9) ? 'disabled' : ''}}"
            data-month="9"
            bindtap="selectMonth">9月</view>
          <view
            class="month-item {{currentMonth === 10 ? 'active' : ''}} {{!isMonthSelectable(currentYear, 10) ? 'disabled' : ''}}"
            data-month="10"
            bindtap="selectMonth">10月</view>
          <view
            class="month-item {{currentMonth === 11 ? 'active' : ''}} {{!isMonthSelectable(currentYear, 11) ? 'disabled' : ''}}"
            data-month="11"
            bindtap="selectMonth">11月</view>
          <view
            class="month-item {{currentMonth === 12 ? 'active' : ''}} {{!isMonthSelectable(currentYear, 12) ? 'disabled' : ''}}"
            data-month="12"
            bindtap="selectMonth">12月</view>
        </view>
      </swiper-item>
    </swiper>

    <!-- 底部按钮区域 -->
    <view class="picker-actions">
      <view class="action-btn cancel" bindtap="onCancel">取消</view>
      <view class="action-btn confirm" bindtap="onConfirm">确定</view>
    </view>
  </view>
</view>