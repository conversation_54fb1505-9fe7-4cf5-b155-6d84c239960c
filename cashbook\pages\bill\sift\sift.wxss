/* pages/bill/sift/sift.wxss */
.container{
 padding-bottom: 100px;  
}
.navbox{
  display: grid;
  grid-template-columns: 1fr 3fr 1fr;
  border: 1px solid;
  padding: 60px 10px 10px 10px;
  box-sizing: border-box;
  /* font-weight: 700; */
  margin:0 !important;
}
.tablist{
  display: grid;
  grid-template-columns: repeat(4,1fr);

}
.navitem{
  text-align: center;
  border-radius: 10px;

}
.navitemActive{
  background-color: #414656;
  color: #fff;
}

.cate1Mouds{
  margin: 0 10px;
}
.RBcard{
  background-color: #fff;
  padding:  10px;
  border-radius: 15px;
  margin: 10px 0;
 
}
.RBheader{
  display: grid;
  grid-template-columns: 1fr 1fr;
  align-items: center;
  position: relative;
  padding: 0 0 0 10px;
}
.RBheader view:nth-child(1)::before{
  content: '';
  position: absolute;
  top: 5px;
  left: 0px;
  width: 5px;
  height: 90%;
  background:linear-gradient(to bottom,#aee05d,#cae4a0,#fff);
  border-radius: 8px;
}
.RBheader view:nth-child(2){
   justify-self: end;
  width: fit-content;
  background-color: #f5f5f5;
  border-radius: 15px;
   padding: 5px 10px;
   color: #818181;
}
.rbTwo{
  margin: 10px 0 0 0;
}

.rbInput{
  background-color: #f6f6f6;
  padding: 10px;
  border-radius: 15px;
  
}

.sz{
  display: grid;
  grid-template-columns: repeat(2,1fr);
  width: fit-content;
  margin: auto;
  gap: 10px;
}
.szitem{
  text-align: center;
  border-radius: 10px;
  background-color: #f5f5f5;
  padding:  2px 4px ;
  width: fit-content;
  box-sizing: border-box;
}
.iconAbso{
  position: absolute;
  left: -22px;
}
.shitBtn{
   text-align: center;
   width: 100%;
   position: fixed;
   bottom: 30px;
   left: 50%;
   transform: translateX(-50%);
   background:linear-gradient(#d8e1ce,#ccd9bf,#c1d0af);
   padding: 10px 0;
   font-weight: 700;
   width: 95%;
   margin: auto;
   border-radius: 15px;
}

.shitheader{
  display: grid;
  grid-template-columns: auto auto 1fr;
  align-items: center;
  gap: 15px;
}

.shitEnd{
  background-color: #f3f3f3;
  padding: 3px 5px;
   justify-self: end;
  border-radius: 10px;
}

.cate2list{

  padding: 20px;
  box-sizing: border-box;
  display: grid;
  grid-template-columns: repeat(4,1fr);
  gap: 10px;
}
.cate2item{
  text-align: center;
  /* border: 1px solid; */
  border-radius: 15px;
  background-color: #f5f5f5;
  padding: 4px 8px;
}
.cate2itemActive{
  background-color: #c3d2b3;
}

/* 模块3 */
.tagsitem{
  display: grid;
  grid-template-columns: repeat(6,1fr);
  gap: 0;
 
}
.titem{

  width: fit-content;
  padding: 5px 10px ;
  border-radius: 10px;
  background-color: #f5f5f5;
}
.cate4list{
  padding: 10px 0;
}
.cate4item{
  display: grid;
  grid-template-columns: auto auto 1fr;
  align-items: center;
  gap: 10px;
  border-bottom: 1px solid rgb(202, 200, 200);
  padding: 10px 0;
}
.circle{
  width: 20px;
  height: 20px;
  border-radius: 50%;
  border: 1px solid #aee05d;
   justify-self: end;
 position: relative;
}
.entityCircle{
  position: absolute;
  top: 50%;
  left: 50%;
  transform: translate(-50%,-50%);
  width: 10px;
  height: 10px;
  background-color: #aee05d;
  border-radius: 50%;
}