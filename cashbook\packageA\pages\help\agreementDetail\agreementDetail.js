// packageA/pages/help/agreementDetail/agreementDetail.js
import { getAgreementDetail } from '../../../../api/basic/index';
// const WxParse = require('../../../../wxParse/wxParse.js');

Page({

  /**
   * 页面的初始数据
   */
  data: {
    agreement: {
      title: '',
      content: ''
    },
    loading: true,
    error: false,
    currentYear: new Date().getFullYear()
  },

  /**
   * 生命周期函数--监听页面加载
   */
  onLoad(options) {
    if (options && options.title) {
      // 设置页面标题
      wx.setNavigationBarTitle({
        title: options.title || '协议详情',
      });
      
      // 获取协议详情
      this.fetchAgreementDetail(options.title);
    } else {
      this.setData({
        loading: false,
        error: true
      });
      
      wx.showToast({
        title: '参数错误',
        icon: 'none'
      });
    }
  },

  /**
   * 生命周期函数--监听页面初次渲染完成
   */
  onReady() {

  },

  /**
   * 获取协议详情
   */
  fetchAgreementDetail(title) {
    this.setData({ loading: true });
    
    getAgreementDetail({ title }).then(res => {
      console.log('协议详情:', res);
      if (res && res.data) {
        const agreement = res.data;
        
        this.setData({
          agreement,
          loading: false
        });
        
        // 如果有使用WxParse富文本解析，则解析HTML内容
        // if (agreement.content && typeof WxParse === 'object' && WxParse.wxParse) {
        //   try {
        //     WxParse.wxParse('article', 'html', agreement.content, this, 0);
        //   } catch (e) {
        //     console.error('解析HTML内容失败:', e);
        //   }
        // }
      } else {
        this.setData({
          loading: false,
          error: true
        });
      }
    }).catch(err => {
      console.error('获取协议详情失败:', err);
      this.setData({
        loading: false,
        error: true
      });
      
      wx.showToast({
        title: '获取协议详情失败',
        icon: 'none'
      });
    });
  },

  /**
   * 生命周期函数--监听页面显示
   */
  onShow() {

  },

  /**
   * 生命周期函数--监听页面隐藏
   */
  onHide() {

  },

  /**
   * 生命周期函数--监听页面卸载
   */
  onUnload() {

  },

  /**
   * 页面相关事件处理函数--监听用户下拉动作
   */
  onPullDownRefresh() {
    const title = this.data.agreement.title;
    if (title) {
      this.fetchAgreementDetail(title);
    }
    wx.stopPullDownRefresh();
  },

  /**
   * 页面上拉触底事件的处理函数
   */
  onReachBottom() {

  },

  /**
   * 用户点击右上角分享
   */
  onShareAppMessage() {
    return {
      title: this.data.agreement.title || '小猫记账 - 协议详情',
      path: `/packageA/pages/help/agreementDetail/agreementDetail?title=${this.data.agreement.title}`
    };
  }
})