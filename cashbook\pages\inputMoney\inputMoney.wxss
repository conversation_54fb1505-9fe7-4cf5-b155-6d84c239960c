.box {
  background-color: #fbfbfb;
}

.mynav {
  position: fixed;
  width: 100%;
  z-index: 1000;
  /* 确保导航栏在最上层 */
  /* background-color: #c0cfae; */
  /* height: 120px; */
  overflow-x: hidden;
  /* 隐藏横向滚动条 */
  box-sizing: border-box;
  /* 确保padding不会增加元素宽度 */
  left: 0;
  /* 确保导航栏左对齐 */
  right: 0;
  /* 确保导航栏右对齐 */
}

/* 导航栏占位元素，防止内容上移 */
.nav-placeholder {
  width: 100%;
  visibility: hidden;
  /* 不可见但占据空间 */
}

.list {
  margin: 20px 10px;
  display: grid;
  grid-template-columns: repeat(5, 1fr);
  gap: 10px;
}

.item {
  display: grid;
  align-items: center;
  text-align: center;
}

/* 食物图标样式 */
.food-icon {
  position: relative;
}

.toppic::after {
  content: '';
  position: absolute;
  bottom: -2px;
  right: 0px;
  width: 16px;
  height: 16px;
  background-color: #888687;
  /* 默认颜色 */
  border-radius: 50%;
  z-index: 1;
  transition: background-color 0.3s ease;
  /* 添加过渡效果 */
}

/* 当元素处于active状态时，伪元素的颜色变化 */
.active::after {
  background-color: #303749;
  /* active状态的颜色 */
}

/* 添加小圆点内的三个点 */
.toppic::before {
  content: '...';
  position: absolute;
  bottom: 2px;
  right: 3px;
  font-size: 12px;
  color: white;
  z-index: 2;
  transition: color 0.3s ease;
  /* 添加过渡效果 */
}

/* 确保active状态下文本颜色保持白色 */
.active::before {
  color: white;
}

.top {
  position: relative;
  height: 60px;
}

.toppic {
  position: absolute;
  top: 50%;
  left: 50%;
  transform: translate(-50%, -50%);
  background-color: #f2f2f2;
  border-radius: 50%;
  width: 50px;
  height: 50px;
}

.active {
  background-color: #c3ceb0;
}

.active2 {
  background-color: #3f4955;
}

.toppic image {
  position: absolute;
  top: 50%;
  left: 50%;
  transform: translate(-50%, -50%);
  width: 30px;
  height: 30px;
  object-fit: cover;
}

.top .more {
  position: absolute;
  bottom: -5px;
  right: -5px;
  transform: translate(-50%, -50%);
  background-color: #9a9a9a;
  width: 20px;
  height: 20px;
  border-radius: 50%;
}


/* 转账模块 */
.zz {
  padding: 10px;
}

.help {
  color: #c0cfae;
  text-align: end;
}


.sxf {
  display: flex;
  align-items: center;
  padding: 0;
  height: 19px; /* 设置固定高度，与zczh保持一致 */
  flex: 1;
}

.sxf image {
  width: 18px;
  height: 18px;
  object-fit: cover;
  padding-right: 10px;
}

.fee-display-text {
  font-size: 14px;
  color: #333;
  padding: 5px 0;
}

.clear-btn {
  position: absolute;
  right: 5px;
  top: 50%;
  transform: translateY(-50%);
  width: 24px;
  height: 24px;
  background-color: #333;
  border-radius: 50%;
  display: flex;
  justify-content: center;
  align-items: center;
  z-index: 10;
  padding: 5px;
  /* 增加点击区域 */
  box-sizing: content-box;
  /* 确保padding不影响大小 */
}

.clear-icon {
  color: white;
  font-size: 18px;
  line-height: 1;
  font-weight: bold;
  display: flex;
  justify-content: center;
  align-items: center;
  width: 100%;
  height: 100%;
}

.sxfitem {
  padding: 3px 6px;
  border-radius: 15px;
  font-size: 14px;
  display: flex;
  align-items: center;
  justify-content: center;
  height: 19px; /* 设置固定高度，与zczh保持一致 */
  min-width: 50px;
}

.sxfitemActive {
  background-color: #c0cfae !important;
}

.sxfMouds {
  display: flex;
  justify-content: space-between;
  gap: 10px;
  align-items: center;
  height: 39px; /* 设置固定高度，与oneClonm保持一致 */
  margin: 10px 0;
  position: relative;
}

.twoleaveColnm {
  display: flex;
  justify-content: space-between;
  align-items: center;
  color: #707070;
  padding: 10px;
  gap: 10px;
  background-color: #f0f0f0;
  border-radius: 40px;
  height: 19px; /* 设置固定高度，与zczh保持一致 */
  flex: 1;
  overflow: hidden;
}

.calute {
  font-size: 14px;
  color: #000;
  background-color: #f0f0f0;
  padding: 10px 15px;
  border-radius: 20px;
  white-space: nowrap;
  display: flex;
  justify-content: center;
  align-items: center;
  text-align: center;
  height: 19px; /* 确保与zczh高度一致 */
}

.textIntro {
  margin: 20px 0;
  color: #9f9f9f;
}


/* 借换模块 */

.borrowAndReturn {
  margin: 10px;

}

.brtab {
  display: grid;
  grid-template-columns: auto 1fr;
  gap: 10px;
}

.britem {
  /* border: 1px solid; */
  width: fit-content;
  padding: 4px 8px;
  border-radius: 15px;
}

.brActive {
  background-color: #414659;
  color: #fff;
}

.tbcitem {
  text-align: center;
}

.tbcpic {
  width: 50px;
  height: 50px;
  border-radius: 50%;
  display: grid;
  align-items: center;
  justify-content: center;
  margin: auto;
  background-color: #f2f2f2;
}

.tbcpic image {
  width: 30px;
  height: 30px;
}

.tabActive {
  background-color: #beccaf;
}

.one {
  display: grid;
  grid-template-columns: auto 1fr;
  background-color: #f3f3f3;
  padding: 10px;
  /* border: 1px solid; */
  border-radius: 30px;
}

.onepic image {
  width: 20px;
  height: 20px;
  padding-right: 10px;
}

.description {
  padding: 10px;
  color: #b6b6b6;
}

.reimbursement {
  margin: 10px;
  /* padding: 10px; */
}

.RBcard {
  background-color: #fff;
  padding: 10px;
  border-radius: 15px;
  margin: 10px 0;
}

.RBheader {
  display: grid;
  grid-template-columns: 1fr 1fr;
  align-items: center;
  position: relative;
  padding: 0 0 0 10px;
}

.RBheader view:nth-child(1)::before {
  content: '';
  position: absolute;
  top: 5px;
  left: 0px;
  width: 5px;
  height: 90%;
  background: linear-gradient(to bottom, #aee05d, #cae4a0, #fff);
  border-radius: 8px;
}

.RBheader view:nth-child(2) {
  justify-self: end;
  width: fit-content;
  background-color: #f5f5f5;
  border-radius: 15px;
  padding: 5px 10px;
  color: #818181;
}

.myswiper {
  /* 高度通过JS动态设置 */
  overflow: visible !important;
  /* 确保内容不会被裁剪 */
  min-height: 500px;
  /* 设置最小高度 */
}

/* 确保swiper-item内容不会被裁剪 */
.myswiper .wx-swiper-dot {
  height: auto !important;
  overflow: visible !important;
}

/* 借还页面swiper样式 */
.br-swiper {
  overflow: visible !important;
}

.br-swiper swiper-item {
  overflow: visible !important;
  height: auto !important;
}

.tabcontent {
  display: grid;
  grid-template-columns: repeat(5, 1fr);
  gap: 10px;
  margin: 10px 0;
  overflow: visible;
}


.custom-tabs {
  margin-bottom: 32rpx;
}

.custom-panel {
  color: var(--td-text-color-primary);
}

.custom-panel__content {
  height: 240rpx;
  font-size: 28rpx;
  color: var(--td-text-color-placeholder);
  padding: 40rpx;
  display: flex;
  justify-content: center;
  align-items: center;
}


/* 确保swiper-item内容能够完全显示 */
.swiper-item-content {
  height: auto;
  overflow: visible;
}

.rbTwo {
  margin: 10px 0;
  position: relative;
}

.rbTwo image {
  position: absolute;
  top: 50%;
  transform: translateY(-50%);
  left: 10px;
  width: 20px;
  height: 20px;
}

.RBtime {
  margin: 10px 0;
}

.rbInput {
  background-color: #f6f6f6;
  padding: 10px;
  border-radius: 15px;
}

.rbtimeInfo {
  display: grid;
  grid-template-columns: 1fr auto;
  align-items: center;
  color: #b2b2b2;
}

.rbicon image {
  width: 15px;
  height: 15px;
  object-fit: cover;
}

.zbinfo {
  display: grid;
  grid-template-columns: auto 1fr;
  align-items: center;
  gap: 10px;
}

.zbinfo .avator {
  width: 50px;
  height: 50px;
  border-radius: 50%;
  display: grid;
  align-items: center;
}

.arrRIght image {
  width: 20px;
  height: 20px;
}

.zbIcon image {
  width: 20px;
  height: 20px;
}

.avator image {
  width: 40px;
  height: 40px;
  border-radius: 50%;
  display: flex;
  justify-self: center;
}

.select {
  position: relative;
  width: fit-content;
  width: 55px !important;
  text-align: right;
}

.select image {
  position: absolute;
  top: 50%;
  transform: translateY(-50%);
  left: 10px;
  width: 20px;
  height: 20px;
}

.oneClonm .zczh {
  display: flex;
  justify-content: space-between;
  align-items: center;
  background-color: #f0f0f0;
  border-radius: 40px;
  padding: 10px;
  flex: 1;
  overflow: hidden;
  height: 19px; /* 设置固定高度，与account-label保持一致 */
}

.oneClonm .zczh .account-icon-left {
  width: 24px;
  height: 24px;
  min-width: 24px;
  /* 防止被挤压 */
  display: flex;
  justify-content: center;
  align-items: center;
}

.oneClonm .zczh .account-icon-left image {
  width: 24px;
  height: 24px;
  object-fit: contain;
}

.oneClonm .zczh .account-info {
  flex: 1;
  display: flex;
  flex-direction: row;
  align-items: center;
  /* overflow: hidden; */
  white-space: nowrap;
  text-overflow: ellipsis;
  max-width: 60%;
  height: 19px; /* 确保高度一致 */
}
.oneClonm .zczh .account-info1 {
  flex: 1;
  display: flex;
  flex-direction: row;
  align-items: center;
  /* overflow: hidden; */
  white-space: nowrap;
  text-overflow: ellipsis;
  max-width: 100%;
  height: 19px; /* 确保高度一致 */
}

.oneClonm .zczh .account-name {
  font-size: 14px;
  font-weight: bold;
  color: #333;
  margin-right: 8px;
  white-space: nowrap;
}

.oneClonm .zczh .account-subname {
  font-size: 14px;
  color: #333;
  overflow: hidden;
  max-width: 60%;
  /* 限制最大宽度 */
}

.oneClonm .zczh .account-icon-right {
  width: 24px;
  height: 24px;
  min-width: 24px;
  /* 防止被挤压 */
  display: flex;
  justify-content: center;
  align-items: center;
  /* background-color: #ffb700; */
  border-radius: 50%;
}

.oneClonm .zczh .account-icon-right image {
  width: 24px;
  height: 24px;
  object-fit: contain;
}

.oneClonm {
  position: relative;
  margin: 10px 0;
  display: flex;
  justify-content: space-between;
  align-items: center;
  height: 39px; /* 设置固定高度，确保所有oneClonm元素高度一致 */
}

.account-label {
  font-size: 14px;
  color: #000;
  background-color: #f0f0f0;
  padding: 10px 15px;
  border-radius: 20px;
  white-space: nowrap;
  display: flex;
  justify-content: center;
  align-items: center;
  text-align: center;
  height: 19px; /* 确保与zczh高度一致 */
}

.account-label1 {
  font-size: 14px;
  color: #000;
  background-color: #f0f0f0;
  padding: 12px 15px;
  border-radius: 20px;
  white-space: nowrap;
  display: flex;
  justify-content: center;
  align-items: center;
  text-align: center;
}

/* t-message 组件样式覆盖 */
.t-message {
  text-align: center !important;
  /* 居中显示文本 */
  width: auto !important;
  /* 宽度占满父容器 */
  top: 80px !important;
  /* 增加距离顶部的距离 */
  left: 50% !important;
  /* 居中显示 */
  transform: translateX(-50%) !important;
  /* 居中显示 */
  border-radius: 16px !important;
  /* 添加圆角 */
  height: 80rpx !important;
  /* 增加高度 */
}

.t-message__text-wrap {
  text-align: center !important;
  /* 居中显示文本 */
}

.t-message__content {
  text-align: center !important;
  /* 居中显示文本 */
  font-size: 16px !important;
  /* 增加字体大小 */
}

/* 警告类型消息样式 */
.t-message--warning {
  text-align: center !important;
  /* 居中显示文本 */
  background-color: #FFEB3B !important;
  /* 设置黄色背景 */
  color: #333 !important;
  /* 设置文字颜色 */
}

.fee-input-container {
  position: relative;
  display: flex;
  align-items: center;
  flex: 1;
  width: 83%;
  height: 19px; /* 设置固定高度，与zczh保持一致 */
}

.fee-input-container input {
  flex: 1;
  height: 19px;
  font-size: 14px;
  line-height: 19px;
}

.clear-btn {
  position: absolute;
  right: -10px;
  top: 50%;
  transform: translateY(-50%);
  width: 5px;
  height: 5px;
  display: flex;
  align-items: center;
  justify-content: center;
  color: #fff;
  font-size: 12px;
  z-index: 1000;
}

.tranfs {
  display: flex;
  align-items: center;
  justify-content: center;
  padding: 10px;
  color: #707070;
  cursor: pointer;
  transition: all 0.3s ease;
  border-radius: 20px;
  position: relative;
}

.tranfs:active {
  transform: scale(0.95);
  background-color: #f0f0f0;
}

.tranfs::after {
  content: '';
  position: absolute;
  width: 100%;
  height: 100%;
  border-radius: 20px;
  background-color: rgba(0, 0, 0, 0.05);
  opacity: 0;
  transition: opacity 0.3s ease;
}

.tranfs:active::after {
  opacity: 1;
}

.tranfs image {
  width: 20px;
  height: 20px;
  margin-right: 5px;
  transition: transform 0.3s ease;
}

.tranfs:active image {
  transform: rotate(180deg);
}
