import { getAccounts } from '../../../../api/account/index'
const util = require('../../../../utils/index.js')

Page({
  data: {
    // 资产类型列表
    assetTypes: [
      {
        id: 1,
        name: '信用卡',
        type: 'credit',
        icon: '/static/icon/card.png',
        description: '负债'
      },
      {
        id: 2,
        name: '借记卡',
        type: 'debit',
        icon: '/static/icon/card2.png',
        description: '资产'
      },
      {
        id: 3,
        name: '支付宝',
        type: 'alipay',
        icon: '/static/icon/hz.png',
        description: '资产'
      },
      {
        id: 4,
        name: '微信钱包',
        type: 'wechat',
        icon: '/static/icon/scan.png',
        description: '资产'
      },
      {
        id: 5,
        name: '现金钱包',
        type: 'cash',
        icon: '/static/icon/sr.png',
        description: '资产'
      },
      {
        id: 6,
        name: '花呗',
        type: 'huabei',
        icon: '/static/icon/xh.png',
        description: '负债'
      },
      {
        id: 7,
        name: '公积金',
        type: 'fund',
        icon: '/static/icon/home.png',
        description: '资产'
      },
      {
        id: 8,
        name: '京东金融',
        type: 'jdfinance',
        icon: '/static/icon/jr.png',
        description: '资产'
      },
      {
        id: 9,
        name: '京东白条',
        type: 'jdwhite',
        icon: '/static/icon/share.png',
        description: '负债'
      }
    ],

    // 当前激活的标签
    activeTab: '资金',

    // 状态栏高度
    statusBarHeight: 0,

    // 当前选中的资产ID
    selectedAssetId: 4,

    // 当前选中的资产名称
    selectedAssetName: '微信钱包',

    // 账户列表数据
    accountsData: {},

    // 当前显示的账户列表
    currentAccounts: [],

    // 加载状态
    loading: false
  },

  /**
   * 生命周期函数--监听页面加载
   */
  onLoad: function (options) {
    // 获取系统信息
    const systemInfo = wx.getSystemInfoSync()

    // 获取状态栏高度
    const statusBarHeight = systemInfo.statusBarHeight

    // 设置状态栏高度和安全区域
    this.setData({
      statusBarHeight: statusBarHeight,
      safeAreaTop: systemInfo.safeArea ? systemInfo.safeArea.top : statusBarHeight
    })

    // 如果有传入的默认选中资产ID，则设置
    if (options && options.selectedId) {
      this.setData({
        selectedAssetId: parseInt(options.selectedId)
      })
    }

    // 如果有传入的默认选中标签，则设置
    if (options && options.activeTab) {
      this.setData({
        activeTab: options.activeTab
      })
    }

    // 获取账户列表数据
    this.fetchAccountsData()
  },

  /**
   * 获取账户列表数据
   */
  fetchAccountsData: function () {
    this.setData({ loading: true })

    getAccounts()
      .then((res) => {
        if (res && res.code === 1 && res.data) {
          // 遍历所有分类下的账户，处理图片路径
          const accountsData = res.data

          // 遍历每个分类下的账户列表
          Object.keys(accountsData).forEach((category) => {
            const accounts = accountsData[category]
            if (Array.isArray(accounts)) {
              accounts.forEach((account) => {
                // 设置图片路径
                account.image = account.image ? util.getImageUrl(account.image) : ''
              })
            }
          })

          this.setData({
            accountsData: accountsData,
            loading: false
          })

          // 根据当前选中的标签更新显示的账户列表
          this.updateCurrentAccounts()
        } else {
          wx.showToast({
            title: res.msg || '获取账户列表失败',
            icon: 'none'
          })
          this.setData({ loading: false })
        }
      })
      .catch((err) => {
        console.error('获取账户列表失败:', err)
        wx.showToast({
          title: '获取账户列表失败',
          icon: 'none'
        })
        this.setData({ loading: false })
      })
  },

  /**
   * 根据当前标签更新显示的账户列表
   */
  updateCurrentAccounts: function () {
    const { activeTab, accountsData } = this.data

    if (accountsData && accountsData[activeTab]) {
      this.setData({
        currentAccounts: accountsData[activeTab]
      })
    } else {
      this.setData({
        currentAccounts: []
      })
    }
  },

  /**
   * 切换标签
   */
  switchTab: function (e) {
    const tab = e.currentTarget.dataset.tab
    this.setData({
      activeTab: tab
    })

    // 根据标签更新显示的账户列表
    this.updateCurrentAccounts()
  },

  /**
   * 选择资产
   */
  selectAsset: function (e) {
    const name = e.currentTarget.dataset.name
    const type = e.currentTarget.dataset.type || ''
    console.log(name)

    // 如果是信用卡或借记卡，跳转到银行选择器页面
    if (name === '信用卡' || name === '借记卡') {
      wx.navigateTo({
        url: `/packageA/pages/settings/bankSelector/bankSelector?type=${name}`
      })
    } else if (name === '报销') {
      wx.navigateTo({
        url: '/packageA/pages/settings/createAccount/createAccount?accountCategory=reimbursement'
      })
    } else if (name === '借出') {
      wx.navigateTo({
        url: '/packageA/pages/settings/createAccount/createAccount?accountCategory=lendOut'
      })
    } else if (name === '借入') {
      wx.navigateTo({
        url: '/packageA/pages/settings/createAccount/createAccount?accountCategory=borrowIn'
      })
    } else {
      // 跳转到新建账户页面，并传递资产类型
      wx.navigateTo({
        url: `/packageA/pages/settings/createAccount/createAccount?type=${type}`
      })
    }
  },

  /**
   * 返回上一页
   */
  navigateBack: function () {
    wx.navigateBack()
  }
})
