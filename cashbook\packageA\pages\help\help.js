// packageA/pages/help/help.js
import { getVideos } from '../../../api/basic/index';
const util = require('../../../utils/index');
Page({

  /**
   * 页面的初始数据
   */
  data: {
    guideList: [], // 初始化为空数组
    loading: true,  // 添加加载状态
    showVideoPopup: false, // 控制视频弹窗显示
    fadeOutClass: '', // 控制淡出动画
    currentVideo: {
      id: '',
      title: '',
      videoUrl: ''
    },
    videoContext: null,
    isPlaying: true,
    videoProgress: 0,
    playbackRate: '1.0'
  },

  /**
   * 显示视频弹窗
   */
  showVideoPopup(e) {
    const { id, title, video } = e.currentTarget.dataset;
    const guide = this.data.guideList.find(item => item.id == id);
    
    if (guide && guide.video) {
      // 处理视频URL
      const videoUrl = util.getImageUrl(guide.video);
      
      // 重置淡出动画类
      this.setData({
        fadeOutClass: '',
        showVideoPopup: true,
        currentVideo: {
          id: id,
          title: guide.title || title || '视频介绍',
          videoUrl: videoUrl
        },
        isPlaying: true,
        videoProgress: 0,
        playbackRate: '1.0'
      }, () => {
        // 创建视频上下文
        this.data.videoContext = wx.createVideoContext('popupVideo', this);
      });
    } else {
      wx.showToast({
        title: '视频暂未上传',
        icon: 'none'
      });
    }
  },

  /**
   * 隐藏视频弹窗
   */
  hideVideoPopup() {
    // 先添加淡出动画类
    this.setData({
      fadeOutClass: 'fade-out'
    });
    
    // 等待动画完成后再隐藏弹窗
    setTimeout(() => {
      // 停止视频播放
      if (this.data.videoContext) {
        this.data.videoContext.stop();
      }
      
      this.setData({
        showVideoPopup: false,
        fadeOutClass: ''
      });
    }, 300); // 与CSS中的动画时长一致
  },
  
  /**
   * 更新视频进度
   */
  onVideoTimeUpdate(e) {
    const { currentTime, duration } = e.detail;
    if (duration > 0) {
      const progress = (currentTime / duration) * 100;
      this.setData({
        videoProgress: progress.toFixed(1)
      });
    }
  },

  /**
   * 切换播放/暂停
   */
  togglePlayPause() {
    if (this.data.isPlaying) {
      this.data.videoContext.pause();
    } else {
      this.data.videoContext.play();
    }
    
    this.setData({
      isPlaying: !this.data.isPlaying
    });
  },
  
  /**
   * 切换播放速率
   */
  changePlaybackRate() {
    // 播放速率选项
    const rates = ['0.5', '0.8', '1.0', '1.25', '1.5', '2.0'];
    const currentIndex = rates.indexOf(this.data.playbackRate);
    const nextIndex = (currentIndex + 1) % rates.length;
    const newRate = rates[nextIndex];
    
    this.data.videoContext.playbackRate(parseFloat(newRate));
    this.setData({
      playbackRate: newRate
    });
  },

  /**
   * 视频播放结束事件
   */
  onVideoEnded() {
    console.log('视频播放结束');
    this.setData({
      isPlaying: false
    });
  },

  /**
   * 视频播放错误事件
   */
  onVideoError(e) {
    console.error('视频播放错误:', e.detail.errMsg);
    wx.showToast({
      title: '视频播放出错',
      icon: 'none'
    });
  },

  /**
   * 查看视频介绍 (旧方法，弃用但保留)
   */
  viewVideo(e) {
    const { id } = e.currentTarget.dataset;
    const guide = this.data.guideList.find(item => item.id === id);
    
    if (guide && guide.video) {
      // 打开视频
      wx.navigateTo({
        url: `/packageA/pages/help/videoDetail/videoDetail?id=${id}`,
      });
    } else {
      wx.showToast({
        title: '视频暂未上传',
        icon: 'none'
      });
    }
  },

  goIntroduction() {
    wx.navigateTo({
      url: '/packageA/pages/introduction/introduction',
    });
  },
  /**
   * 关闭页面
   */
  closePage() {
    wx.navigateBack();
  },

  /**
   * 查看更多
   */
  viewMore() {
    wx.navigateTo({
      url: '/packageA/pages/moreHelp/moreHelp',
    });
  },

  /**
   * 获取视频指南列表
   */
  fetchGuideList() {
    this.setData({ loading: true });
    
    getVideos().then(res => {
      // 假设接口返回的数据结构为 { data: [...] }
      const guideList = res.data || [];
      
      this.setData({
        guideList: guideList,
        loading: false
      });
    }).catch(err => {
      console.error('获取视频列表失败:', err);
      this.setData({ loading: false });
      wx.showToast({
        title: '获取视频列表失败',
        icon: 'none'
      });
    });
  },

  /**
   * 生命周期函数--监听页面加载
   */
  onLoad(options) {
    // 设置导航栏样式
    wx.setNavigationBarTitle({
      title: '帮助中心',
    });
    // 获取视频列表
    this.fetchGuideList();
  },

  /**
   * 生命周期函数--监听页面初次渲染完成
   */
  onReady() {

  },

  /**
   * 生命周期函数--监听页面显示
   */
  onShow() {
    // 页面显示时刷新数据
    if (this.data.guideList.length === 0) {
      this.fetchGuideList();
    }
  },

  /**
   * 生命周期函数--监听页面隐藏
   */
  onHide() {
    // 如果弹窗正在显示，关闭它
    if (this.data.showVideoPopup) {
      this.hideVideoPopup();
    }
  },

  /**
   * 生命周期函数--监听页面卸载
   */
  onUnload() {
    // 确保清理视频资源
    if (this.data.videoContext) {
      this.data.videoContext.stop();
    }
  },

  /**
   * 页面相关事件处理函数--监听用户下拉动作
   */
  onPullDownRefresh() {
    // 下拉刷新重新加载数据
    this.fetchGuideList();
    wx.stopPullDownRefresh();
  },

  /**
   * 页面上拉触底事件的处理函数
   */
  onReachBottom() {

  },

  /**
   * 用户点击右上角分享
   */
  onShareAppMessage() {
    return {
      title: '小本账 - 帮助中心',
      path: '/packageA/pages/help/help'
    };
  }
})