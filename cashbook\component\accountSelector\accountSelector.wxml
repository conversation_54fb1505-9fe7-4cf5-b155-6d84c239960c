<view class="container {{show ? 'show' : ''}}" bindtap="closeSelector">
  <view class="selector-content" catchtap="preventBubble" animation="{{animation}}">
    <!-- 顶部操作栏 -->
    <view class="header">
      <view class="header-left">
        <view class="close-btn" bindtap="closeSelector"></view>
      </view>
      <view class="header-right">
        <!-- 设置按钮，仅在showSettings为true时显示 -->
        <view class="settings-btn" bindtap="onSettingsClick" wx:if="{{showSettings}}">
          <image src="{{settingsIcon}}" mode="aspectFit" />
        </view>
        <view class="mode-switch" bindtap="toggleMode" style="background-color: {{themeColor}};">
          <text>{{mode === 'list' ? '列表' : '卡片'}}</text>
        </view>
        <view class="add-btn" bindtap="addAccount">
          <text>添加</text>
        </view>
      </view>
    </view>
    <!-- 列表模式 -->
    <view class="account-list" wx:if="{{mode === 'list'}}">
      <view class="account-item {{item.selected ? 'selected' : ''}} {{index === allAccounts.length - 1 ? 'no-account-item' : ''}}" wx:for="{{allAccounts}}" wx:key="id" bindtap="selectAccount" data-index="{{index}}">
        <!-- 普通账户项（非最后一个） -->
        <block wx:if="{{index !== allAccounts.length - 1}}">
          <view class="account-icon-left" wx:if="{{item.icon}}">
            <image src="{{item.icon}}" mode="aspectFit" />
            <view class="check-mark" wx:if="{{item.selected}}">
              <image src="/static/icon/check.png" mode="aspectFit" />
            </view>
          </view>
          <view class="account-info">
            <view class="account-name">{{item.name}}</view>
            <view class="account-subname" wx:if="{{item.subName && item.subName !== item.name}}">
              {{item.subName}}
            </view>
          </view>
          <view class="account-amount" wx:if="{{item.amount}}">{{item.amount}}</view>
        </block>
        <!-- 不选择具体账户项（最后一个） -->
        <block wx:else>
          <view class="no-account-icon">
            <image src="{{item.icon}}" mode="aspectFit" />
          </view>
          <view class="no-account-info">
            <view class="no-account-name">{{item.name}}</view>
            <view class="no-account-desc">{{item.subName || '仅计入收支账单，不计入资产'}}</view>
          </view>
        </block>
      </view>
    </view>
    <!-- 卡片模式 -->
    <view class="account-grid" wx:else>
      <view class="grid-container">
        <!-- 普通账户卡片，排除最后一个 -->
        <view class="grid-item {{item.selected ? 'selected' : ''}}" wx:for="{{allAccounts}}" wx:key="id" wx:if="{{index !== allAccounts.length - 1}}" bindtap="selectAccount" data-index="{{index}}">
          <view class="grid-item-content">
            <view class="grid-amount" wx:if="{{item.amount}}">{{item.amount}}</view>
            <view class="grid-info">
              <view class="grid-name">{{item.name}}</view>
              <view class="grid-subname" wx:if="{{item.subName && item.subName !== item.name}}">
                {{item.subName}}
              </view>
            </view>
            <view class="grid-icon">
              <image src="{{item.icon}}" mode="aspectFit" />
              <view class="check-mark-card" wx:if="{{item.selected}}">
                <image src="/static/icon/check.png" mode="aspectFit" />
              </view>
            </view>
          </view>
        </view>
      </view>
      <!-- 不选择具体账户项（最后一个） - 卡片模式下单独显示 -->
      <view class="no-account-card" wx:if="{{allAccounts.length > 0}}" bindtap="selectAccount" data-index="{{allAccounts.length - 1}}">
        <view class="no-account-icon">
          <image src="{{allAccounts[allAccounts.length - 1].icon}}" mode="aspectFit" />
        </view>
        <view class="no-account-info">
          <view class="no-account-name">{{allAccounts[allAccounts.length - 1].name}}</view>
          <view class="no-account-desc">{{allAccounts[allAccounts.length - 1].subName || '仅计入收支账单，不计入资产'}}</view>
        </view>
      </view>
    </view>
    <!-- 底部刷新按钮 -->
    <view class="refresh-btn" bindtap="refreshData">
      <text>重新加载数据</text>
    </view>
    <!-- 底部安全区域 -->
    <view class="safe-area-bottom"></view>
  </view>
</view>