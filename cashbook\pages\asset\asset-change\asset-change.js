// pages/asset-change/asset-change.js
const app = getApp()

Page({
  data: {
    // 图表数据点
    chartPoints: [
      { x: 10, y: 80 },
      { x: 25, y: 60 },
      { x: 40, y: 45 },
      { x: 55, y: 30 },
      { x: 70, y: 20 },
      { x: 85, y: 15 }
    ],

    // 迷你图表数据点
    miniChartPoints: [
      { x: 0, y: 70 },
      { x: 15, y: 50 },
      { x: 30, y: 40 },
      { x: 45, y: 35 },
      { x: 60, y: 30 },
      { x: 75, y: 25 },
      { x: 90, y: 20 }
    ],

    // 资产数据
    assetData: {
      totalDebt: -6270.0,
      totalAsset: 1200.0,
      debtComposition: {
        creditCard: {
          amount: -5260.0,
          percentage: 83.89
        },
        payable: {
          amount: -1010.0,
          percentage: 16.11
        }
      },
      assetComposition: {
        cash: {
          amount: 700.0,
          percentage: 58.33
        },
        receivable: {
          amount: 500.0,
          percentage: 41.67
        }
      }
    },

    // 银行卡信息
    bankCardInfo: {
      bankName: '招商银行',
      cardType: '信用卡',
      cardNumber: '4321',
      currentAmount: -4860.0,
      date: '2025年5月30日'
    },

    // 当前主题
    currentTheme: 'blue'
  },

  onLoad(options) {
    // 获取用户设置的主题色
    this.loadUserTheme()

    // 初始化页面数据
    this.initPageData()

    // 设置导航栏
    this.setNavigationBar()
  },

  onShow() {
    // 刷新数据
    this.refreshData()
  },

  // 加载用户主题设置
  loadUserTheme() {
    try {
      const theme = wx.getStorageSync('userTheme') || 'blue'
      this.setData({ currentTheme: theme })

      // 设置CSS变量
      this.setThemeVariables(theme)
    } catch (error) {
      console.error('加载主题失败:', error)
    }
  },

  // 设置主题变量
  setThemeVariables(theme) {
    const themeColors = {
      blue: {
        primary: '#1890ff',
        success: '#52c41a',
        error: '#ff4d4f',
        warning: '#faad14'
      },
      green: {
        primary: '#52c41a',
        success: '#73d13d',
        error: '#ff7875',
        warning: '#ffc53d'
      },
      purple: {
        primary: '#722ed1',
        success: '#95de64',
        error: '#ff85c0',
        warning: '#ffec3d'
      },
      orange: {
        primary: '#fa8c16',
        success: '#52c41a',
        error: '#ff4d4f',
        warning: '#fadb14'
      }
    }

    // 通过修改页面根元素的data-theme属性来切换主题
    if (typeof document !== 'undefined') {
      document.documentElement.setAttribute('data-theme', theme)
    }
  },

  // 设置导航栏
  setNavigationBar() {
    wx.setNavigationBarTitle({
      title: '资产变动'
    })

    // 设置导航栏颜色
    wx.setNavigationBarColor({
      frontColor: '#000000',
      backgroundColor: '#ffffff'
    })
  },

  // 初始化页面数据
  initPageData() {
    // 这里可以根据实际需求加载真实数据
    this.loadAssetData()
    this.initChart()
  },

  // 加载资产数据
  loadAssetData() {
    // 模拟异步加载数据
    wx.showLoading({
      title: '加载中...'
    })

    // 这里应该调用实际的API
    setTimeout(() => {
      // 模拟数据更新
      this.setData({
        assetData: this.data.assetData,
        bankCardInfo: this.data.bankCardInfo
      })

      wx.hideLoading()
    }, 500)
  },

  // 初始化图表
  initChart() {
    // 如果需要使用Canvas绘制更复杂的图表，可以在这里实现
    const ctx = wx.createCanvasContext('assetChart', this)

    // 简单的线图绘制示例
    this.drawLineChart(ctx)
  },

  // 绘制线图
  drawLineChart(ctx) {
    const { chartPoints } = this.data
    const canvasWidth = 300 // 画布宽度
    const canvasHeight = 150 // 画布高度

    // 设置线条样式
    ctx.setStrokeStyle('#1890ff')
    ctx.setLineWidth(2)

    // 开始绘制路径
    ctx.beginPath()

    chartPoints.forEach((point, index) => {
      const x = (point.x / 100) * canvasWidth
      const y = (point.y / 100) * canvasHeight

      if (index === 0) {
        ctx.moveTo(x, y)
      } else {
        ctx.lineTo(x, y)
      }
    })

    ctx.stroke()
    ctx.draw()
  },

  // 刷新数据
  refreshData() {
    this.loadAssetData()
  },

  // 返回上一页
  goBack() {
    wx.navigateBack({
      delta: 1
    })
  },

  // 跳转到设置页面
  goToSettings() {
    wx.navigateTo({
      url: '/pages/settings/settings'
    })
  },

  // 开启数据统计
  enableDataStatistics() {
    wx.showModal({
      title: '开启数据统计',
      content: '开启后将为您提供更详细的资产变动分析',
      confirmText: '开启',
      cancelText: '取消',
      success: (res) => {
        if (res.confirm) {
          // 处理开启数据统计的逻辑
          this.handleEnableStatistics()
        }
      }
    })
  },

  // 处理开启统计
  handleEnableStatistics() {
    wx.showLoading({
      title: '开启中...'
    })

    // 模拟API调用
    setTimeout(() => {
      wx.hideLoading()
      wx.showToast({
        title: '开启成功',
        icon: 'success'
      })

      // 刷新页面数据
      this.refreshData()
    }, 1000)
  },

  // 查看银行卡详情
  viewBankCardDetail() {
    const { bankCardInfo } = this.data

    wx.navigateTo({
      url: `/pages/bank-card-detail/bank-card-detail?cardId=${bankCardInfo.cardNumber}`
    })
  },

  // 分享页面
  onShareAppMessage() {
    return {
      title: '我的资产变动分析',
      path: '/pages/asset-change/asset-change',
      imageUrl: '/images/share-asset-change.png'
    }
  },

  // 分享到朋友圈
  onShareTimeline() {
    return {
      title: '资产变动分析 - 了解财务状态',
      imageUrl: '/images/share-timeline.png'
    }
  },

  // 页面卸载
  onUnload() {
    // 清理资源
  },

  // 下拉刷新
  onPullDownRefresh() {
    this.refreshData()

    setTimeout(() => {
      wx.stopPullDownRefresh()
    }, 1000)
  },

  // 触底加载更多
  onReachBottom() {
    // 如果有更多数据，可以在这里加载
  }
})
