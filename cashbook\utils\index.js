

// 图片URL处理函数
const getImageUrl = (url) => {
    // 如果url为空，返回默认头像
    if (!url) return '/static/icon/default_avatar.png';
    
    // 如果是完整URL(以http或https开头)，直接返回
    if (url.startsWith('http://') || url.startsWith('https://')) {
      return url;
    }
    
    // 如果是本地路径(以/static开头)，直接返回
    if (url.startsWith('/static/')) {
      return url;
    }
    
    // 否则，拼接基础URL
    const BASE_URL = 'http://www.youcai.com'; // 这里设置您的API域名
    return `${BASE_URL}${url}`;
  }
  
  // 导出工具函数
  module.exports = {
    getImageUrl
  }