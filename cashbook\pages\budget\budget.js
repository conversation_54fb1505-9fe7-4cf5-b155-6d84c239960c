// pages/budget/budget.js
import { getBudgetType } from '../../api/basic/index';
Page({

  /**
   * 页面的初始数据
   */
  data: {
    costlist: [
      { id: 1, name: '本月消费', price: '30292.00' },
      { id: 2, name: '月预算', price: '0.0' },
      { id: 3, name: '预算剩余', price: '100%' },
      { id: 4, name: '剩余时间', price: '7' },
    ],
    list2: [
      { id: 1, name: '本月日均消费', price: '212.22' },
      { id: 2, name: '日均预算', price: '12.22' },
      { id: 3, name: '剩余每日可消费金额', price: '292.22' },
    ],
    bottomId: 0,
    swperHeight: null,
    budgetTypes: [], // 存储预算类型数据
    currentTitle: '5月预算', // 当前显示的标题
    
    // 预算数据
    monthBudget: {
      total: 5000, // 总预算
      spent: 248, // 已消费
      remaining: 4838, // 剩余
      progress: 95, // 剩余百分比
    },
    yearBudget: {
      total: 60000, // 总预算
      spent: 248, // 已消费
      remaining: 59752, // 剩余
      progress: 99, // 剩余百分比
    },

    // 月份选择器数据
    showPicker: false,
    pickerYear: new Date().getFullYear(),
    pickerMonth: new Date().getMonth() + 1,
    selectedYear: new Date().getFullYear(),
    selectedMonth: new Date().getMonth() + 1
  },


  /**
   * 生命周期函数--监听页面加载
   */
  onLoad(options) {
    this.getViewHeight('#box2');
    this.fetchBudgetTypes();
    this.updateBudgetCircleProgress();

    // 初始化日期选择器的默认值
    const now = new Date();
    this.setData({
      pickerYear: now.getFullYear(),
      pickerMonth: now.getMonth() + 1,
      selectedYear: now.getFullYear(),
      selectedMonth: now.getMonth() + 1
    });
  },

  /**
   * 显示月份选择器
   */
  showMonthPicker() {
    this.setData({
      showPicker: true
    });
  },

  /**
   * 隐藏月份选择器
   */
  hideMonthPicker() {
    this.setData({
      showPicker: false
    });
  },

  /**
   * 处理月份选择
   */
  onMonthSelected(e) {
    const { year, month, date } = e.detail;
    console.log('选择的日期:', date, year, month);
    
    // 更新标题和选中的日期
    let newTitle = `${month}月预算`;
    
    // 如果是年视图则显示年度预算
    if (this.data.bottomId === 1) {
      newTitle = `${year}年预算`;
    }
    
    this.setData({
      selectedYear: year,
      selectedMonth: month,
      currentTitle: newTitle,
      showPicker: false
    });
    
    // 这里可以调用API获取对应年月的预算数据
    this.fetchBudgetData(year, month);
  },

  /**
   * 获取指定年月的预算数据
   */
  fetchBudgetData(year, month) {
    wx.showLoading({
      title: '加载中',
    });
    
    // 这里应该调用实际的API，为演示暂时使用模拟数据
    setTimeout(() => {
      wx.hideLoading();
      
      // 模拟预算数据
      const total = Math.floor(Math.random() * 10000) + 5000;
      const spent = Math.floor(Math.random() * total);
      const remaining = total - spent;
      const progress = Math.floor((remaining / total) * 100);
      
      if (this.data.bottomId === 0) {
        // 月预算
        this.setData({
          monthBudget: {
            total,
            spent,
            remaining,
            progress
          },
          'currentTitle': `${month}月预算`
        });
      } else {
        // 年预算
        this.setData({
          yearBudget: {
            total: total * 12,
            spent,
            remaining: (total * 12) - spent,
            progress: Math.floor(((total * 12) - spent) / (total * 12) * 100)
          },
          'currentTitle': `${year}年预算`
        });
      }
    }, 500);
  },

  /**
   * 更新预算进度圆环
   */
  updateBudgetCircleProgress() {
    // 根据月度预算计算进度
    const monthProgress = this.data.monthBudget.progress;
    const yearProgress = this.data.yearBudget.progress;
    
    // 更新月度预算圆环样式
    this.updateCircleStyle('.month-circle', monthProgress);
    
    // 更新年度预算圆环样式
    this.updateCircleStyle('.year-circle', yearProgress);
  },
  
  /**
   * 更新圆环样式
   */
  updateCircleStyle(selector, progress) {
    const angle = 3.6 * progress; // 将百分比转换为角度 (100% = 360度)
    let rotation = 0;
    let borderStyle = '';
    
    if (progress <= 25) {
      // 0-25%: 只有右上角有颜色
      rotation = 45;
      borderStyle = `
        border-top-color: #a4cfa0;
        border-right-color: #a4cfa0;
        border-bottom-color: transparent;
        border-left-color: transparent;
      `;
    } else if (progress <= 50) {
      // 25-50%: 右半部分有颜色
      rotation = 45;
      borderStyle = `
        border-top-color: #a4cfa0;
        border-right-color: #a4cfa0;
        border-bottom-color: transparent;
        border-left-color: transparent;
      `;
    } else if (progress <= 75) {
      // 50-75%: 右侧和底部有颜色
      rotation = 45;
      borderStyle = `
        border-top-color: #a4cfa0;
        border-right-color: #a4cfa0;
        border-bottom-color: #a4cfa0;
        border-left-color: transparent;
      `;
    } else {
      // 75-100%: 除了一小部分，其余都有颜色
      rotation = 45;
      borderStyle = `
        border-top-color: #a4cfa0;
        border-right-color: #a4cfa0;
        border-bottom-color: #a4cfa0;
        border-left-color: ${progress >= 100 ? '#a4cfa0' : 'transparent'};
      `;
    }
    
    // 这里不直接操作DOM，只是为了记录逻辑，实际上我们通过CSS和数据绑定来实现
    console.log(`Progress for ${selector}: ${progress}%, rotation: ${rotation}deg, style: ${borderStyle}`);
  },

  /**
   * 获取预算类型数据
   */
  fetchBudgetTypes() {
    wx.showLoading({
      title: '加载中',
    });

    getBudgetType().then(res => {
      wx.hideLoading();
      if (res && res.data) {
        // 处理API返回的数据
        const budgetTypes = this.processBudgetTypeData(res.data);
        this.setData({
          budgetTypes: budgetTypes
        });
      } else {
        // 如果API失败，回退到默认值
        this.setData({
          budgetTypes: [
            { type: 'month', name: '月账单' },
            { type: 'year', name: '年预算' }
          ]
        });
      }
    }).catch(err => {
      wx.hideLoading();
      console.error('获取预算类型失败:', err);
      // 如果API失败，回退到默认值
      this.setData({
        budgetTypes: [
          { type: 'month', name: '月账单' },
          { type: 'year', name: '年预算' }
        ]
      });
    });
  },

  /**
   * 处理预算类型数据
   */
  processBudgetTypeData(data) {
    // 根据实际API返回格式进行处理
    // 假设API返回的是一个数组，包含type和name字段
    // 如果格式不同，请根据实际数据结构调整处理方式
    return data.map(item => {
      return {
        type: item.type || '',
        name: item.name || (item.type === 'month' ? '月账单' : '年预算')
      };
    });
  },

  /**
   * 切换预算类型
   */
  changeBudgetType(e) {
    const { type, index } = e.currentTarget.dataset;
    console.log('切换预算类型:', type, index);
    
    this.setData({
      bottomId: index,
      currentTitle: index === 0 ? '5月预算' : '2023年预算'
    });
    
    // 根据类型加载对应内容
    if (index == 0) {
      this.getViewHeight('#box1');
    } else {
      this.getViewHeight('#box2');
    }
  },

  swperChange(e) {
    var cureent = e.detail.current;
    console.log('cureent', cureent);
    if (cureent == 0) {
      this.getViewHeight('#box1')
    } else {
      this.getViewHeight('#box2')
    }

    this.setData({
      bottomId: cureent,
      currentTitle: cureent === 0 ? '5月预算' : '2023年预算'
    })
  },
  getViewHeight(args) {
    if (args == null) {
      args = '#box2'
    }
    // 创建一个选择器查询对象
    const query = wx.createSelectorQuery();
    // 选择目标 view 标签
    query.select(args).boundingClientRect();
    // 执行查询操作
    query.exec((res) => {
      if (res[0]) {
        // 获取 view 的高度
        const height = res[0].height;
        this.setData({
          swperHeight: height
        });
        console.log('view 的高度为：', height);
      }
    });
  },
  
  // 保留原方法用于兼容
  changeBotm() {
    this.setData({
      bottomId: 0
    })
  },
  changeBotm2() {
    this.setData({
      bottomId: 1
    })
  },
  back() {
    wx.navigateBack();
  },

  /**
   * 前往设置页面
   */
  goToSetting() {
    wx.navigateTo({
      url: '/pages/my/budgetSet/budgetSet'
    });
  },
  
  /**
   * 添加分类预算
   */
  addCategoryBudget() {
    wx.navigateTo({
      url: '/pages/my/budgetSet/budgetSet?type=category'
    });
  },

  /**
   * 生命周期函数--监听页面初次渲染完成
   */
  onReady() {

  },

  /**
   * 生命周期函数--监听页面显示
   */
  onShow() {

  },

  /**
   * 生命周期函数--监听页面隐藏
   */
  onHide() {

  },

  /**
   * 生命周期函数--监听页面卸载
   */
  onUnload() {

  },

  /**
   * 页面相关事件处理函数--监听用户下拉动作
   */
  onPullDownRefresh() {

  },

  /**
   * 页面上拉触底事件的处理函数
   */
  onReachBottom() {

  },

  /**
   * 用户点击右上角分享
   */
  onShareAppMessage() {

  }
})