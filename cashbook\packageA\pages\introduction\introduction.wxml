<!--packageA/pages/introduction/introduction.wxml-->
<view class="introduction-container">
  <!-- 加载提示 -->
  <view class="loading-container" wx:if="{{loading}}">
    <view class="loading-icon"></view>
    <view class="loading-text">加载中...</view>
  </view>

  <!-- 正文内容 -->
  <block wx:else>
    <!-- 顶部标题 -->
    <view class="header">
      <view class="title">{{apiData.introduction.title}}</view>
      <view class="subtitle">{{apiData.introduction.content }}</view>
    </view>

    <!-- 引言内容 -->
    <!-- <view class="introduction-content" wx:if="{{apiData.introduction.content}}">
      <text class="intro-text">{{apiData.introduction.content}}</text>
    </view> -->

    <!-- 功能列表区域 -->
    <view class="functions-container">
      <!-- 遍历sections数组动态生成功能列表 -->
      <block wx:for="{{sections}}" wx:key="title" wx:for-item="section">
        <view class="section">
          <view class="section-title">{{section.title}}</view>
          
          <!-- 普通列表视图 -->
          <view class="section-content" wx:if="{{section.title !== '更多文章分类'}}">
            <block wx:for="{{section.items}}" wx:key="id" wx:for-item="functionItem">
              <view class="function-item" bindtap="navigateToFunction" data-type="{{functionItem.type}}" data-id="{{functionItem.id}}">
                <view class="function-icon">
                  <image src="{{functionItem.icon}}" mode="aspectFit"></image>
                </view>
                <view class="function-info">
                  <view class="function-name">{{functionItem.name}}</view>
                  <view class="function-desc">{{functionItem.desc}}</view>
                </view>
                <view class="arrow">
                  <image src="/static/icon/arrRight.png" mode="aspectFit"></image>
                </view>
              </view>
            </block>
          </view>
          
          <!-- 网格视图 - 用于"更多文章分类" -->
          <view class="section-grid" wx:if="{{section.title === '更多文章分类'}}">
            <block wx:for="{{section.items}}" wx:key="id" wx:for-item="functionItem">
              <view class="grid-item" bindtap="navigateToFunction" data-type="{{functionItem.type}}" data-id="{{functionItem.id}}">
                <view class="grid-icon">
                  <image src="{{functionItem.icon}}" mode="aspectFit"></image>
                </view>
                <view class="grid-name">{{functionItem.name}}</view>
              </view>
            </block>
          </view>
        </view>
      </block>
    </view>

    <!-- 客服信息 -->
    <view class="kefu-container" wx:if="{{apiData.kefuInfo.phone || apiData.kefuInfo.wechat}}">
      <view class="kefu-title">客服信息</view>
      <view class="kefu-info">
        <view class="kefu-item" wx:if="{{apiData.kefuInfo.phone}}">
          <view class="kefu-label">电话：</view>
          <view class="kefu-value" bindtap="makePhoneCall" data-phone="{{apiData.kefuInfo.phone}}">{{apiData.kefuInfo.phone}}</view>
        </view>
        <view class="kefu-item" wx:if="{{apiData.kefuInfo.wechat}}">
          <view class="kefu-label">微信：</view>
          <view class="kefu-value" bindtap="copyWechat" data-wechat="{{apiData.kefuInfo.wechat}}">{{apiData.kefuInfo.wechat}}</view>
        </view>
        <view class="kefu-item" wx:if="{{apiData.kefuInfo.workTime}}">
          <view class="kefu-label">工作时间：</view>
          <view class="kefu-value">{{apiData.kefuInfo.workTime}}</view>
        </view>
      </view>
    </view>

    <!-- 底部信息 -->
    <view class="footer">
      <view class="version">当前版本：{{version}}</view>
      <view class="copyright">© {{apiData.introduction.copyright || '2023 小猫记账 All Rights Reserved'}}</view>
    </view>
  </block>
</view>