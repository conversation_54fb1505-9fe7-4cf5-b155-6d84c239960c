<van-popup show="{{ visible }}" position="bottom" custom-style="border-radius: 35px; margin: 0 30rpx 30rpx; width: calc(100% - 60rpx); max-height: 85vh;" bind:close="onClose">
  <view class="share-container">
    <view class="share-header fixed-header">
      <view class="share-title">分享给朋友</view>
      <van-icon name="cross" size="20px" class="close-btn {{buttonPosition === 'left' ? 'btn-left' : 'btn-right'}}" bind:tap="onClose" />
    </view>
    <view class="share-content">
      <view class="bill-card" style="background-color: {{cardStyle.backgroundColor}}">
        <!-- 卡片头部 -->
        <view class="bill-header">
          <view class="bill-title">
            <view class="app-name" style="color: {{cardStyle.backgroundColor === storedColors.theme2.backgroundColor ? '#fff' : '#06866c'}}">
              小青账
            </view>
            <view class="app-desc" style="color: {{cardStyle.backgroundColor === storedColors.theme2.backgroundColor ? 'rgba(255,255,255,0.8)' : '#06866c'}}">
              和小蛙一起记账省钱
            </view>
          </view>
          <view class="frog-icon">
            <image src="/static/images/frog.png" mode="aspectFit" />
          </view>
        </view>
        <!-- 总计消费统计 -->
        <view class="bill-summary">
          <view class="summary-item" style="background-color: {{cardStyle.backgroundColor === storedColors.theme2.backgroundColor ? 'rgba(46, 46, 46, 0.8)' : 'rgba(255,255,255,0.15))'}}">
            <view class="summary-text" style="color: {{cardStyle.backgroundColor === storedColors.theme2.backgroundColor ? '#fff' : '#333'}}">
              共消费
              <text style="color: {{selectedColor}}">{{billData.expenseCount}}</text>
              笔，共计
            </view>
            <view class="summary-amount" style="color: {{selectedColor}}">
              {{billData.expenseTotal}}
            </view>
            <view class="summary-currency" style="color: {{cardStyle.backgroundColor === storedColors.theme2.backgroundColor ? '#fff' : '#333'}}">
              元
            </view>
          </view>
          <view class="summary-item" style="background-color: {{cardStyle.backgroundColor === storedColors.theme2.backgroundColor ? 'rgba(46, 46, 46, 0.8)' : 'rgba(255,255,255,0.15))'}}">
            <view class="summary-text" style="color: {{cardStyle.backgroundColor === storedColors.theme2.backgroundColor ? '#fff' : '#333'}}">
              共收款
              <text style="color: {{selectedColor}}">{{billData.incomeCount}}</text>
              笔，共计
            </view>
            <view class="summary-amount" style="color: {{selectedColor}}">
              {{billData.incomeTotal}}
            </view>
            <view class="summary-currency" style="color: {{cardStyle.backgroundColor === storedColors.theme2.backgroundColor ? '#fff' : '#333'}}">
              元
            </view>
          </view>
        </view>
        <!-- 日期分割线 -->
        <view class="date-divider">
          <view class="divider-line"></view>
          <view class="divider-date" style="color: {{cardStyle.backgroundColor === storedColors.theme2.backgroundColor ? '#fff' : '#666'}}">
            {{billData.date}} 消费明细
          </view>
          <view class="divider-line"></view>
        </view>
        <!-- 账单明细 -->
        <view class="bill-details-wrapper">
          <block wx:for="{{billData.items}}" wx:key="index">
            <view class="bill-item">
              <view class="item-left">
                <view class="item-icon" style="background-color: {{cardStyle.backgroundColor === storedColors.theme2.backgroundColor ? 'rgba(255,255,255,0.2)' : '#f5f5f5'}}">
                  <image src="{{item.icon}}" mode="aspectFit" />
                </view>
                <view class="item-category">
                  <text style="color: {{cardStyle.backgroundColor === storedColors.theme2.backgroundColor ? '#fff' : '#333'}}">
                    {{item.category}}
                  </text>
                  <text class="item-subcategory" style="color: {{cardStyle.backgroundColor === storedColors.theme2.backgroundColor ? 'rgba(255,255,255,0.8)' : '#666'}}">
                    {{item.subcategory}}
                  </text>
                </view>
              </view>
              <view class="item-right">
                <view class="item-amount" style="color: {{selectedColor}}">¥ {{item.amount}}</view>
                <view class="item-tag" style="color: {{cardStyle.backgroundColor === storedColors.theme2.backgroundColor ? '#fff' : '#666'}}; background-color: {{cardStyle.backgroundColor === storedColors.theme2.backgroundColor ? 'rgba(255,255,255,0.2)' : 'rgba(255,255,255,0.5)'}}">
                  {{item.tag}}
                </view>
              </view>
            </view>
          </block>
        </view>
      </view>
      <!-- 品牌底部 - 根据样式切换 -->
      <view class="brand-footer" wx:if="{{cardFormat !== 'none'}}" style="background-color: {{cardStyle.backgroundColor === storedColors.theme2.backgroundColor ? '#183241' : '#ffffff'}}">
        <!-- 青蛙样式 -->
        <view class="frog-brand" wx:if="{{cardFormat === 'frog'}}">
          <view class="brand-logo">
            <image src="/static/images/frog-mini.png" mode="aspectFit" />
          </view>
          <view class="brand-info">
            <view class="brand-name" style="color: {{selectedColor}}">小青账</view>
            <view class="brand-slogan" style="color: {{cardStyle.backgroundColor === storedColors.theme2.backgroundColor ? 'rgba(255,255,255,0.8)' : '#999'}}">
              享受记账乐趣，为财务自由做好准备～
            </view>
          </view>
        </view>
        <!-- 个人头像样式 -->
        <view class="user-brand" wx:elif="{{cardFormat === 'avatar'}}">
          <view class="user-info">
            <view class="user-avatar">
              <image src="/static/images/user-avatar.png" mode="aspectFit" class="avatar-image" />
            </view>
            <view class="user-details">
              <view class="user-name-wrapper">
                <view class="user-name" style="color: {{selectedColor}}">西风.</view>
                <view class="vip-badge" style="background-color: {{selectedColor}}; color: #fff;">
                  VIP
                </view>
              </view>
              <view class="user-record" style="color: {{cardStyle.backgroundColor === storedColors.theme2.backgroundColor ? 'rgba(255,255,255,0.8)' : '#999'}}">
                今天是你记账的第27天啦 🎯
              </view>
            </view>
          </view>
        </view>
      </view>
    </view>
    <view class="action-buttons fixed-footer">
      <button class="action-btn change-color" bind:tap="onChangeColor">更换颜色</button>
      <button class="action-btn format" bind:tap="onChangeFormat">样式</button>
      <button class="action-btn save-image" bind:tap="onSaveImage">保存图片</button>
    </view>
  </view>
</van-popup>
<!-- 样式选择弹窗 -->
<van-popup show="{{ formatPopupVisible }}" position="bottom" custom-style="border-radius: 35px; margin: 0 30rpx 30rpx; width: calc(100% - 60rpx);" bind:close="onCloseFormatPopup">
  <view class="format-popup">
    <view class="format-header">
      <view class="format-title">选择样式</view>
      <van-icon name="cross" size="20px" class="close-btn {{buttonPosition === 'left' ? 'btn-left' : 'btn-right'}}" bind:tap="onCloseFormatPopup" />
    </view>
    <view class="format-options">
      <view class="format-option" bindtap="selectFormat" data-format="frog">
        <view class="option-text">小青账</view>
        <van-icon name="arrow" />
      </view>
      <view class="format-option" bindtap="selectFormat" data-format="avatar">
        <view class="option-text">个人头像</view>
        <van-icon name="arrow" />
      </view>
      <view class="format-option" bindtap="selectFormat" data-format="none">
        <view class="option-text">无</view>
        <van-icon name="arrow" />
      </view>
    </view>
  </view>
</van-popup>