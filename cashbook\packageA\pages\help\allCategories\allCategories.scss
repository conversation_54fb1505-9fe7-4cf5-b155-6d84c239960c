/* packageA/pages/help/allCategories/allCategories.wxss */
.all-categories-container {
  min-height: 100vh;
  background-color: #f7f7f7;
  padding-bottom: 30rpx;
  
  /* 顶部导航栏 */
  .nav-bar {
    position: fixed;
    top: 0;
    left: 0;
    right: 0;
    height: 88rpx;
    background-color: #fff;
    display: flex;
    align-items: center;
    padding: 0 30rpx;
    z-index: 100;
    border-bottom: 1rpx solid #f0f0f0;
    
    .back-btn {
      width: 40rpx;
      height: 40rpx;
      
      image {
        width: 100%;
        height: 100%;
      }
    }
    
    .page-title {
      flex: 1;
      text-align: center;
      font-size: 32rpx;
      font-weight: 500;
      color: #333;
      padding-right: 40rpx; /* 平衡左边的返回按钮 */
    }
  }
  
  /* 分类列表样式 */
  .categories-list {    
    .category-item {
      display: flex;
      align-items: center;
      background-color: #fff;
      border-radius: 12rpx;
      padding: 24rpx 20rpx;
      margin-bottom: 20rpx;
      box-shadow: 0 2rpx 10rpx rgba(0, 0, 0, 0.05);
      
      .category-icon {
        width: 80rpx;
        height: 80rpx;
        border-radius: 10rpx;
        overflow: hidden;
        margin-right: 20rpx;
        background-color: #f3f7fb;
        display: flex;
        align-items: center;
        justify-content: center;
        
        image {
          width: 60%;
          height: 60%;
        }
        
        &.default-icon {
          background-color: #edf2f7;
          
          image {
            width: 50%;
            height: 50%;
            opacity: 0.6;
          }
        }
      }
      
      .category-info {
        flex: 1;
        
        .category-name {
          font-size: 30rpx;
          font-weight: 500;
          color: #333;
          margin-bottom: 4rpx;
        }
        
        .category-desc {
          font-size: 24rpx;
          color: #666;
          margin-bottom: 4rpx;
          display: -webkit-box;
          -webkit-line-clamp: 1;
          -webkit-box-orient: vertical;
          overflow: hidden;
        }
        
        .category-count {
          font-size: 22rpx;
          color: #999;
        }
      }
      
      .category-arrow {
        width: 32rpx;
        height: 32rpx;
        display: flex;
        align-items: center;
        justify-content: center;
        
        image {
          width: 100%;
          height: 100%;
          opacity: 0.3;
        }
      }
      
      &:active {
        opacity: 0.8;
        transform: scale(0.98);
        transition: all 0.2s;
      }
    }
  }
  
  /* 加载中样式 */
  .loading-container {
    display: flex;
    flex-direction: column;
    justify-content: center;
    align-items: center;
    height: 500rpx;
    
    .loading-icon {
      width: 60rpx;
      height: 60rpx;
      border: 4rpx solid #f3f3f3;
      border-top: 4rpx solid #3498db;
      border-radius: 50%;
      animation: spin 1s linear infinite;
      margin-bottom: 20rpx;
    }
    
    @keyframes spin {
      0% { transform: rotate(0deg); }
      100% { transform: rotate(360deg); }
    }
    
    text {
      font-size: 26rpx;
      color: #999;
    }
  }
  
  /* 空状态样式 */
  .empty-container {
    display: flex;
    flex-direction: column;
    justify-content: center;
    align-items: center;
    height: 500rpx;
    padding-top: 100rpx;
    
    image {
      width: 180rpx;
      height: 180rpx;
      margin-bottom: 20rpx;
    }
    
    text {
      font-size: 28rpx;
      color: #999;
    }
  }
}