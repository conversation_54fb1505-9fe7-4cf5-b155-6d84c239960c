<view class="content">
  <view class="nav">
    <customNav isRight="{{true}}" list="{{tablist}}" isIcon="{{true}}" bind:getTitleId="onGetTitleId" IconPath="{{iconpath}}"></customNav>
  </view>

  <view class="card3" bind:tap="toDetail" data-id="{{item}}" wx:for="{{lists}}">
    <view class="header3">
      <view>还剩那么多，加油哦~</view>
      <view bind:tap="toEdit" data-id="{{item}}">编辑</view>
    </view>
    <!-- 第二列 -->
    <view class="gridTmeplte">
      <view class="avator avatorBg brr">
        <view>{{item.image}}</view>
      </view>
      <view>
        <view>{{item.name}}</view>
        <view class="description2 noMP fs14">{{item.notes}}</view>
      </view>
      <view>
        <view>￥{{item.totoal_money}}</view>
        <view class="description2 noMP fs14" style="text-align: right">{{item.completed_num}}/{{item.goods_num}}件</view>
      </view>
    </view>
    <!-- 第三列 -->
    <view class="sp1">
      <view>预购{{item.starttime}}开始</view>
      <view>预购{{item.endtime}}结束</view>
    </view>
  </view>
  <view class="bottom_title" wx:if="{{more}}">暂无更多。。。</view>
  <view class="botmBtn" bind:tap="addShop">添加</view>
</view>
