.login-container {
  display: flex;
  flex-direction: column;
  padding: 40rpx;
  height: 100vh;
  background-color: #f8f8f8;
  position: relative;
}

/* 返回按钮 */
.back-button {
  position: absolute;
  top: 40rpx;
  left: 40rpx;
  width: 60rpx;
  height: 60rpx;
  z-index: 10;
}

.back-button image {
  width: 40rpx;
  height: 40rpx;
}

/* 登录表单 */
.login-form {
  margin-top: 120rpx;
  width: 100%;
}

.form-title {
  font-size: 36rpx;
  font-weight: bold;
  color: #333;
  margin-bottom: 60rpx;
}

/* 输入框样式 */
.input-group {
  position: relative;
  margin-bottom: 30rpx;
}

.form-input {
  width: 100%;
  height: 90rpx;
  background-color: #f0f0f0;
  border-radius: 45rpx;
  padding: 0 30rpx;
  font-size: 28rpx;
  color: #333;
}

/* 密码显示切换按钮 */
.password-toggle {
  position: absolute;
  right: 30rpx;
  top: 50%;
  transform: translateY(-50%);
  width: 40rpx;
  height: 40rpx;
}

.password-toggle image {
  width: 100%;
  height: 100%;
  opacity: 0.6;
}

/* 登录按钮 */
.login-btn {
  width: 100%;
  height: 90rpx;
  border-radius: 45rpx;
  background-color: #8dc63f;
  color: white;
  font-size: 32rpx;
  display: flex;
  align-items: center;
  justify-content: center;
  margin-top: 60rpx;
  margin-bottom: 40rpx;
}

/* 底部链接 */
.bottom-links {
  display: flex;
  justify-content: space-between;
  font-size: 26rpx;
  color: #666;
  margin-top: 20rpx;
}

.highlight-text {
  color: #8dc63f;
}

/* 装饰图 */
.decoration-image {
  position: absolute;
  bottom: 120rpx;
  right: 0;
  width: 300rpx;
  height: 300rpx;
  opacity: 0.9;
}

.decoration-image image {
  width: 100%;
  height: 100%;
}

/* 隐私政策 */
.privacy-policy {
  position: absolute;
  bottom: 40rpx;
  left: 0;
  width: 100%;
  display: flex;
  justify-content: center;
  font-size: 24rpx;
  color: #999;
}

.checkbox {
  display: flex;
  align-items: center;
}

.policy-link {
  color: #8dc63f;
}

checkbox .wx-checkbox-input {
  width: 30rpx;
  height: 30rpx;
  border-radius: 50%;
}