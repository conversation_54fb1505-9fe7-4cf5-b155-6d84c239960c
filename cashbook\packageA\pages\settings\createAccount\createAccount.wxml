<!-- 顶部状态栏 -->
<view class="status-bar" style="height: {{statusBarHeight}}px;"></view>

<!-- 顶部导航栏 -->
<view class="navigation-bar" style="height: {{navHeight}}px;">
  <view class="nav-left" bindtap="onBackTap">
    <van-icon name="arrow-left" />
  </view>
  <view class="nav-title">{{pageTitle}}</view>
</view>

<!-- 主内容区域 -->
<view class="create-account-container">
  <!-- 资产类型选择区域 - 仅普通账户显示 -->
  <block wx:if="{{!isReimbursement && !isLendOut && !isBorrowIn}}">
    <view class="section-title" wx:if="{{!isCreditCard && !isDebitCard}}">资产类型</view>
    <view class="section-title" wx:else>资金</view>

    <view class="asset-type-selector" bindtap="selectAssetType">
      <view class="asset-type-left">
        <view class="asset-icon">
          <image src="{{assetType.icon}}" mode="aspectFit"></image>
        </view>
        <view class="asset-name">{{assetType.name}}</view>
      </view>
      <view class="asset-type-right">
        <view class="arrow-icon">></view>
      </view>
    </view>

    <!-- 借记卡/信用卡选择银行 -->
    <view wx:if="{{isCreditCard || isDebitCard}}">
      <view class="asset-type-selector" bindtap="selectBank">
        <view class="asset-type-left">
          <view class="asset-icon">
            <image src="{{bankIcon || '/assets/icons/bank.png'}}" mode="aspectFit"></image>
          </view>
          <view class="asset-name">{{selectedBank || '选择银行'}}</view>
        </view>
        <view class="asset-type-right">
          <view class="arrow-icon">></view>
        </view>
      </view>
    </view>
  </block>

  <!-- 报销特有表单项 -->
  <block wx:if="{{isReimbursement}}">
    <!-- 基本信息 -->
    <view class="section-title">基本信息</view>

    <!-- 报销人 -->
    <view class="form-item">
      <input class="form-input" placeholder="用户名" value="{{reimbursementPerson}}" bindinput="onReimbursementPersonInput" />
    </view>

    <!-- 报销金额 -->
    <view class="form-item">
      <!-- <input class="form-input" type="digit" placeholder="账户余额" value="{{accountBalance}}" bindinput="onAccountBalanceInput" /> -->
      <view class="input-icon">
        <image src="/assets/icons/calculator.png" mode="aspectFit" class="icon-image"></image>
      </view>
    </view>
  </block>

  <!-- 借出特有表单项 -->
  <block wx:if="{{isLendOut}}">
    <!-- 基本信息 -->
    <view class="section-title">基本信息</view>

    <!-- 借款人 -->
    <view class="form-item">
      <input class="form-input" placeholder="借款给谁" value="{{borrower}}" bindinput="onBorrowerInput" />
    </view>

    <!-- 借出金额 -->
    <view class="form-item">
      <input class="form-input" type="digit" placeholder="借出金额" value="{{accountBalance}}" bindinput="onAccountBalanceInput" />
      <view class="input-icon">
        <image src="/assets/icons/calculator.png" mode="aspectFit" class="icon-image"></image>
      </view>
    </view>

    <!-- 余额同步区域 -->
    <view class="side-bar-section">
      <view class="side-bar" style="background-color: {{themeColor}};"></view>
      <view class="section-title">余额同步</view>
    </view>

    <view class="toggle-item">
      <view class="toggle-left">
        <view class="toggle-title">同时记一笔账单</view>
        <view class="toggle-desc">将金额记一笔对应类型账单</view>
      </view>
      <view class="toggle-right">
        <switch checked="{{syncBalance}}" bindchange="toggleSyncBalance" color="#20cc52" />
      </view>
    </view>

    <view class="toggle-desc-extra">
      如创建资产初始金额不为0并打开同时记一笔账单，将创建一笔调整资产的账单方便查看变动记录。
    </view>
  </block>

  <!-- 借入特有表单项 -->
  <block wx:if="{{isBorrowIn}}">
    <!-- 基本信息 -->
    <view class="section-title">基本信息</view>

    <!-- 出借人 -->
    <view class="form-item">
      <input class="form-input" placeholder="向谁借" value="{{lender}}" bindinput="onLenderInput" />
    </view>

    <!-- 借入金额 -->
    <view class="form-item">
      <input class="form-input" type="digit" placeholder="借入金额" value="{{borrowAmount}}" bindinput="onBorrowAmountInput" />
      <view class="input-icon">
        <image src="/assets/icons/calculator.png" mode="aspectFit" class="icon-image"></image>
      </view>
    </view>

    <!-- 余额同步区域 -->
    <view class="side-bar-section">
      <view class="side-bar" style="background-color: {{themeColor}};"></view>
      <view class="section-title">余额同步</view>
    </view>

    <view class="toggle-item">
      <view class="toggle-left">
        <view class="toggle-title">同时记一笔账单</view>
        <view class="toggle-desc">将金额记一笔对应类型账单</view>
      </view>
      <view class="toggle-right">
        <switch checked="{{syncBalance}}" bindchange="toggleSyncBalance" color="#20cc52" />
      </view>
    </view>

    <view class="toggle-desc-extra">
      如创建资产初始金额不为0并打开同时记一笔账单，将创建一笔调整资产的账单方便查看变动记录。
    </view>
  </block>

  <!-- 基本信息区域 - 仅普通账户显示 -->
  <block wx:if="{{!isReimbursement && !isLendOut && !isBorrowIn}}">
    <view class="section-title" wx:if="{{isDebitCard || !isCreditCard}}">基本信息</view>

    <!-- 备注信息 - 仅信用卡/借记卡显示 -->
    <view class="form-item" wx:if="{{isCreditCard || isDebitCard}}">
      <input class="form-input" placeholder="备注信息" value="{{remark}}" bindinput="onRemarkInput" />
    </view>

    <!-- 基本信息表单项 - 仅在非信用卡/借记卡时显示 -->
    <view class="form-item" wx:if="{{!isCreditCard && !isDebitCard}}">
      <input class="form-input" placeholder="用户名" value="{{accountName}}" bindinput="onAccountNameInput" />
    </view>

    <!-- 银行卡号 - 仅信用卡/借记卡显示 -->
    <view class="form-item" wx:if="{{isCreditCard || isDebitCard}}">
      <input class="form-input" placeholder="银行卡号" value="{{cardNumber}}" bindinput="onCardNumberInput" />
    </view>

    <!-- 信用卡相关资金信息 -->
    <block wx:if="{{isCreditCard}}">
      <!-- 资金相关区域 - 仅信用卡显示 -->
      <view class="section-title">资金</view>

      <view class="form-item">
        <input class="form-input" type="digit" placeholder="信用额度" value="{{creditLimit}}" bindinput="onCreditLimitInput" />
      </view>

      <view class="form-item">
        <input class="form-input" type="digit" placeholder="当前欠款" value="{{currentDebt}}" bindinput="onCurrentDebtInput" />
      </view>

      <view class="form-item">
        <input class="form-input" type="digit" placeholder="剩余额度" value="{{remainingLimit}}" disabled />
      </view>

      <view class="form-desc">
        当前欠款 和 信用额度 输入一个即可自动识别计算
      </view>
    </block>

    <!-- 资金区域 - 所有类型显示 -->
    <block wx:if="{{!isCreditCard}}">
      <view class="section-title" wx:if="{{isDebitCard}}">资金</view>

      <view class="form-item">
        <input class="form-input" type="digit" placeholder="账户余额" value="{{accountBalance}}" bindinput="onAccountBalanceInput" />
        <view class="input-icon">
          <image src="/assets/icons/calculator.png" mode="aspectFit" class="icon-image"></image>
        </view>
      </view>
    </block>

    <!-- 账单/还款日期 - 仅信用卡显示 -->
    <block wx:if="{{isCreditCard}}">
      <view class="section-title">账单/还款日期</view>

      <view class="form-date-item">
        <view class="form-date-label">账单日期</view>
        <view class="form-date-value">{{statementDay}}</view>
      </view>

      <view class="form-date-item">
        <view class="form-date-label">还款日期</view>
        <view class="form-date-value">{{repaymentDay}}</view>
      </view>
    </block>
  </block>

  <!-- 余额同步区域 - 仅普通账户显示 -->
  <block wx:if="{{!isReimbursement && !isLendOut && !isBorrowIn}}">
    <view class="side-bar-section">
      <view class="side-bar" style="background-color: {{themeColor}};"></view>
      <view class="section-title">余额同步</view>
    </view>

    <view class="toggle-item">
      <view class="toggle-left">
        <view class="toggle-title">同时记一笔账单</view>
        <view class="toggle-desc">将金额记一笔对应类型账单</view>
      </view>
      <view class="toggle-right">
        <switch checked="{{syncBalance}}" bindchange="toggleSyncBalance" color="#20cc52" />
      </view>
    </view>

    <view class="toggle-desc-extra">
      如创建资产初始金额不为0并打开同时记一笔账单，将创建一笔调整资产的账单方便查看变动记录。
    </view>
  </block>

  <!-- 自定义分组区域 - 所有类型显示 -->
  <view class="side-bar-section">
    <view class="side-bar" style="background-color: {{themeColor}};"></view>
    <view class="section-title">自定义分组</view>
  </view>

  <view class="group-name-section">
    <view class="group-name-label {{isCustomGroupFocused ? 'focused' : ''}}" style="{{isCustomGroupFocused ? 'color:' + themeColor : ''}}"
      >{{isCustomGroupFocused ? '分组名称' : ''}}</view
    >
    <view class="group-name-input-container">
      <view class="group-name-input-wrapper">
        <view class="label-text" wx:if="{{!isCustomGroupFocused}}">分组名称</view>
        <input
          class="group-name-input {{isCustomGroupFocused ? 'focused' : ''}}"
          placeholder="{{isCustomGroupFocused ? '' : '请输入分组名称'}}"
          value="{{customGroup}}"
          bindinput="onCustomGroupInput"
          bindfocus="onCustomGroupFocus"
          bindblur="onCustomGroupBlur"
          focus="{{isCustomGroupFocused}}"
        />
      </view>
      <view class="group-name-button" bindtap="selectCustomGroup" style="background-color: {{themeColor}}; color: #fff;">选取</view>
    </view>
  </view>

  <!-- 其他设置区域 - 所有类型显示 -->
  <view class="side-bar-section">
    <view class="side-bar" style="background-color: {{themeColor}};"></view>
    <view class="section-title">其他</view>
  </view>

  <view class="toggle-item">
    <view class="toggle-left">
      <view class="toggle-title">在资产页隐藏卡片</view>
      <view class="toggle-desc">隐藏资产可在资产主页底部查看</view>
    </view>
    <view class="toggle-right">
      <switch checked="{{hideAsset}}" bindchange="toggleHideAsset" color="#20cc52" />
    </view>
  </view>

  <view class="toggle-item">
    <view class="toggle-left">
      <view class="toggle-title">计入总资产</view>
      <view class="toggle-desc">是否加入资产计算</view>
    </view>
    <view class="toggle-right">
      <switch checked="{{includeInTotal}}" bindchange="toggleIncludeInTotal" color="#20cc52" />
    </view>
  </view>
</view>

<!-- 保存按钮 - 固定在底部 -->
<view class="save-button" bindtap="saveAccount" style="background-color: {{themeColor}};">保存</view>

<!-- 资产类型选择弹窗 -->
<customPopup visible="{{assetTypePopupVisible}}" title="" position="bottom" bind:close="closeAssetTypePopup" closeButtonPosition="left" maxHeight="55%">
  <view class="asset-type-popup">
    <!-- 顶部标题和标签区域 -->
    <view class="asset-type-header">
      <view class="asset-type-title">资产类型</view>
      <view class="asset-type-tab">资金</view>
    </view>

    <!-- 资产类型列表 -->
    <view class="asset-list">
      <view wx:for="{{assetTypeList[activeTab]}}" wx:key="id" class="asset-item" bindtap="selectType" data-item="{{item}}">
        <view class="asset-item-left">
          <view class="asset-icon-container">
            <image src="{{item.image}}" mode="aspectFit" class="asset-icon-image"></image>
          </view>
          <view class="asset-info">
            <view class="asset-name">{{item.name}}</view>
            <view class="asset-type">{{item.type_text}}</view>
          </view>
        </view>
      </view>
    </view>
  </view>
</customPopup>

<!-- 银行选择弹窗 -->
<customPopup visible="{{bankPopupVisible}}" title="" position="bottom" bind:close="closeBankPopup" closeButtonPosition="left" maxHeight="80vh">
  <view class="bank-selector-popup">
    <!-- 顶部标题区域 -->
    <view class="bank-popup-header">
      <view class="bank-popup-title">选择银行</view>
      <view class="bank-popup-custom" bindtap="navigateToCustomBank">自定义</view>
    </view>

    <!-- 搜索区域 -->
    <view class="bank-search-container">
      <view class="bank-search-box">
        <view class="bank-search-icon">
          <image src="/assets/icons/search.png" mode="aspectFit"></image>
        </view>
        <input class="bank-search-input" placeholder="搜索银行" bindinput="onBankSearchInput" value="{{bankSearchKeyword}}" />
      </view>
    </view>

    <!-- 银行列表区域 -->
    <view class="bank-list-container">
      <scroll-view class="bank-list" scroll-y="{{true}}" scroll-into-view="{{scrollIntoView}}" scroll-with-animation="{{true}}">
        <view
          wx:for="{{bankList}}"
          wx:key="index"
          id="{{item.isInitial && item.initial !== '' ? item.initial : ''}}"
          class="{{item.isInitial ? 'bank-initial' : 'bank-item'}}"
          bindtap="{{!item.isInitial ? 'selectBankItem' : ''}}"
          data-bank="{{item}}"
        >
          <block wx:if="{{item.isInitial}}">
            <text>{{item.initial !== '热门银行' ? item.initial : ''}}</text>
          </block>
          <block wx:else>
            <view class="bank-icon">
              <image src="{{item.icon}}" mode="aspectFit"></image>
            </view>
            <view class="bank-name">{{item.name}}</view>
            <view class="bank-favorite" wx:if="{{item.isFavorite}}">
              <image src="/assets/icons/star.png" mode="aspectFit"></image>
            </view>
          </block>
        </view>
      </scroll-view>

      <!-- 右侧字母索引 -->
      <view class="letter-index">
        <view class="letter-item">↑</view>
        <view wx:for="{{letters}}" wx:key="index" class="letter-item {{currentLetter === item ? 'active' : ''}}" bindtap="onLetterTap" data-letter="{{item}}">
          {{item}}
        </view>
      </view>
    </view>
  </view>
</customPopup>

<!-- 自定义图片名称弹窗 -->
<customPopup visible="{{showCustomPopup}}" title="" position="bottom" round="{{true}}" closeButtonPosition="left" bind:close="onCustomPopupClose" maxHeight="auto">
  <view class="custom-image-popup">
    <view class="custom-image-title">
      自定义图片名称
      <view class="crown-icon">
        <image src="/assets/icons/vip.png" mode="aspectFit"></image>
      </view>
    </view>
    <view class="custom-image-form">
      <view class="form-item">
        <input class="form-input" placeholder="图片链接" value="{{customImageUrl}}" bindinput="onImageUrlInput" />
      </view>
      <view class="form-item">
        <input class="form-input" placeholder="名称" value="{{customImageName}}" bindinput="onImageNameInput" />
      </view>
      <view class="custom-save-button" bindtap="onSaveCustomImage">保存</view>
    </view>
  </view>
</customPopup>

<!-- 自定义分组选择弹窗 -->
<customPopup visible="{{customGroupPopupVisible}}" title="自定义分组选择" position="bottom" bind:close="closeCustomGroupPopup" closeButtonPosition="left" maxHeight="70%">
  <view class="custom-group-popup">
    <view wx:for="{{groupList}}" wx:key="index" class="group-item" bindtap="selectGroup" data-group="{{item}}">
      {{item}}
    </view>
  </view>
</customPopup>
