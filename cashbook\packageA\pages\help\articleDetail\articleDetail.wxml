<!--packageA/pages/help/articleDetail/articleDetail.wxml-->
<view class="article-detail-container">
  <!-- 顶部导航 -->
  <!-- <view class="nav-bar">
    <view class="back-btn" bindtap="goBack">
      <image src="/static/icon/back.png" mode="aspectFit"></image>
    </view>
    <view class="article-title-short">{{article.title || '文章详情'}}</view>
  </view> -->

  <!-- 文章内容 -->
  <view class="article-content" wx:if="{{!loading && article}}">
    <!-- 文章标题 -->
    <view class="article-header">
      <view class="article-title">{{article.title}}</view>
    </view>
    
    <!-- 常规文章正文 -->
    <view class="article-body">
      <rich-text nodes="{{article.content || '暂无内容'}}"></rich-text>
    </view>
  </view>
  
  <!-- 加载中 -->
  <view class="loading-container" wx:if="{{loading}}">
    <view class="loading-icon"></view>
    <text>加载中...</text>
  </view>
  
  <!-- 文章不存在 -->
  <view class="empty-container" wx:if="{{!loading && !article}}">
    <text>文章不存在或已被删除</text>
  </view>
</view> 