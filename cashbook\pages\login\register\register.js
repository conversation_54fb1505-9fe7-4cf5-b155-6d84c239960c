// pages/login/register/register.js
Page({
  data: {
    nickname: '',
    phone: '',
    password: '',
    confirmPassword: '',
    showPassword: false,
    showConfirmPassword: false,
    isAgree: true
  },

  onLoad: function(options) {
    // 页面加载时的逻辑
  },

  // 昵称输入
  onNicknameInput: function(e) {
    this.setData({
      nickname: e.detail.value
    });
  },

  // 手机号输入
  onPhoneInput: function(e) {
    this.setData({
      phone: e.detail.value
    });
  },

  // 密码输入
  onPasswordInput: function(e) {
    this.setData({
      password: e.detail.value
    });
  },

  // 确认密码输入
  onConfirmPasswordInput: function(e) {
    this.setData({
      confirmPassword: e.detail.value
    });
  },

  // 切换密码显示/隐藏
  togglePasswordVisibility: function() {
    this.setData({
      showPassword: !this.data.showPassword
    });
  },

  // 切换确认密码显示/隐藏
  toggleConfirmPasswordVisibility: function() {
    this.setData({
      showConfirmPassword: !this.data.showConfirmPassword
    });
  },

  // 注册
  register: function() {
    if (!this.data.isAgree) {
      wx.showToast({
        title: '请先同意隐私政策',
        icon: 'none'
      });
      return;
    }

    // 表单验证
    if (!this.data.nickname) {
      wx.showToast({
        title: '请输入昵称',
        icon: 'none'
      });
      return;
    }

    if (!this.data.phone) {
      wx.showToast({
        title: '请输入手机号',
        icon: 'none'
      });
      return;
    }

    // 简单的手机号验证
    if (!/^1\d{10}$/.test(this.data.phone)) {
      wx.showToast({
        title: '请输入正确的手机号',
        icon: 'none'
      });
      return;
    }

    if (!this.data.password) {
      wx.showToast({
        title: '请输入密码',
        icon: 'none'
      });
      return;
    }

    if (this.data.password.length < 6) {
      wx.showToast({
        title: '密码长度不能少于6位',
        icon: 'none'
      });
      return;
    }

    if (this.data.password !== this.data.confirmPassword) {
      wx.showToast({
        title: '两次输入的密码不一致',
        icon: 'none'
      });
      return;
    }

    wx.navigateTo({
      url: '/pages/login/verifyCode/verifyCode?nickname=' + encodeURIComponent(this.data.nickname) + '&phone=' + encodeURIComponent(this.data.phone) + '&password=' + encodeURIComponent(this.data.password),
    })
    return

    wx.showLoading({
      title: '注册中...',
    });
    
    // 这里添加注册逻辑，调用后端API
    // 模拟注册成功
    setTimeout(() => {
      wx.hideLoading();
      
      // 注册成功后跳转到登录页面
      wx.showToast({
        title: '注册成功',
        icon: 'success',
        duration: 1500,
        success: () => {
          setTimeout(() => {
            wx.navigateBack({
              delta: 1
            });
          }, 1500);
        }
      });
    }, 1500);
  },

  // 返回上一页
  goBack: function() {
    wx.navigateBack();
  },

  // 隐私政策勾选变化
  checkboxChange: function(e) {
    this.setData({
      isAgree: e.detail.value.length > 0
    });
  },

  // 显示隐私政策
  showPrivacyPolicy: function() {
    wx.navigateTo({
      url: '/pages/login/privacy/privacy',
    });
  }
})