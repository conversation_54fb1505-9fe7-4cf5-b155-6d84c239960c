/* 整体容器 */
.container {
  display: flex;
  flex-direction: column;
  min-height: 100vh;
  background-color: #f5f5f5;
  position: relative;
}

/* 顶部导航 */
.nav-header {
  display: flex;
  align-items: center;
  justify-content: space-between;
  padding: 10px 15px;
  background-color: #ffffff;
  position: sticky;
  top: 0;
  z-index: 100;
  box-shadow: 0 1px 3px rgba(0, 0, 0, 0.05);
}

.nav-back {
  width: 24px;
  height: 24px;
  display: flex;
  align-items: center;
  justify-content: center;
}

.nav-back image {
  width: 20px;
  height: 20px;
}

.nav-title {
  font-size: 16px;
  font-weight: 500;
}

.nav-placeholder {
  width: 24px;
}

/* 内容区域 */
.content {
  flex: 1;
  padding: 20px;
  display: flex;
  flex-direction: column;
  align-items: center;
}

/* 手机号显示 */
.phone-display {
  width: 100%;
  text-align: center;
  margin-top: 40px;
  margin-bottom: 30px;
}

.phone-number {
  font-size: 24px;
  font-weight: 500;
  display: block;
  margin-bottom: 10px;
}

.phone-hint {
  font-size: 14px;
  color: #999;
}

/* 验证码输入框 */
.verification-code {
  display: flex;
  justify-content: space-between;
  width: 80%;
  margin: 20px auto;
}

.code-input-box {
  width: 50px;
  height: 50px;
  background-color: #fff;
  border-radius: 8px;
  display: flex;
  justify-content: center;
  align-items: center;
  box-shadow: 0 1px 3px rgba(0, 0, 0, 0.1);
}

.code-input-box input {
  width: 100%;
  height: 100%;
  text-align: center;
  font-size: 20px;
  font-weight: bold;
}

/* 清除按钮 */
.clear-btn {
  margin-top: 15px;
  text-align: right;
  width: 80%;
}

.clear-btn text {
  color: #a5ddb9;
  font-size: 14px;
}

/* 验证按钮 */
.verify-btn {
  width: 90%;
  height: 45px;
  background-color: #a5ddb9;
  border-radius: 22.5px;
  display: flex;
  justify-content: center;
  align-items: center;
  margin: 40px auto;
  color: #fff;
  font-size: 16px;
  box-shadow: 0 2px 5px rgba(165, 221, 185, 0.3);
}

/* 底部导航栏 */
.bottom-tabbar {
  display: flex;
  justify-content: space-around;
  align-items: center;
  height: 50px;
  background-color: #fff;
  border-top: 1px solid #f0f0f0;
}

.tab-item {
  display: flex;
  justify-content: center;
  align-items: center;
  flex: 1;
}

.tab-item image {
  width: 24px;
  height: 24px;
}