// 银行选择器页面
const app = getApp()
const util = require('../../../../utils/index.js')
import { getBankList } from '../../../../api/account/index'

Page({
  data: {
    // 状态栏高度
    statusBarHeight: 0,

    // 搜索关键词
    searchKeyword: '',

    // 加载状态
    loading: true,

    // 银行列表
    bankList: [],

    // 原始银行列表数据
    originalBankList: [],

    // 字母索引数组
    letters: ['A', 'B', 'C', 'D', 'E', 'F', 'G', 'H', 'I', 'J', 'K', 'L', 'M', 'N', 'O', 'P', 'Q', 'R', 'S', 'T', 'U', 'V', 'W', 'X', 'Y', 'Z', '#'],

    // 当前选中的字母
    currentLetter: '',

    // 滚动到的视图ID
    scrollIntoView: '',

    // 固定头部高度
    headerHeight: 0,

    // 自定义图片弹窗相关
    showCustomPopup: false,
    customImageUrl: '',
    customImageName: '',

    // 导航栏标题
    navTitle: ''
  },

  /**
   * 生命周期函数--监听页面加载
   */
  onLoad: function (options) {
    // 获取系统信息
    const systemInfo = wx.getSystemInfoSync()

    // 获取状态栏高度
    const statusBarHeight = systemInfo.statusBarHeight

    // 计算固定头部的总高度：状态栏 + 导航栏(88rpx) + 搜索框(96rpx)
    // 搜索框高度保持不变，仍为96rpx (48px)
    const headerHeight = statusBarHeight + 44 + 48 // 将rpx转换为px (88/2 和 96/2)

    // 设置状态栏高度和内容区域的padding-top
    this.setData({
      statusBarHeight: statusBarHeight,
      headerHeight: headerHeight
    })

    // 如果有传入的卡类型参数，设置导航栏标题
    if (options && options.type) {
      this.setData({
        navTitle: options.type
      })
    }

    // 加载银行列表数据
    this.loadBankList()
  },

  /**
   * 加载银行列表数据
   */
  loadBankList: function () {
    this.setData({ loading: true })

    // 从API获取银行列表数据
    getBankList()
      .then((res) => {
        if (res && res.code === 1 && res.data) {
          // 处理返回的银行数据
          const bankData = this.formatBankData(res.data)

          // 处理数据，按字母排序并添加索引标题
          const processedData = this.processAndSortBankList(bankData)

          this.setData({
            originalBankList: bankData,
            bankList: processedData,
            loading: false
          })
        } else {
          // 如果API请求失败，使用模拟数据
          this.loadMockBankList()

          wx.showToast({
            title: res.msg || '获取银行列表失败',
            icon: 'none'
          })
        }
      })
      .catch((err) => {
        console.error('获取银行列表失败:', err)

        // 如果API请求失败，使用模拟数据
        this.loadMockBankList()

        wx.showToast({
          title: '获取银行列表失败',
          icon: 'none'
        })
      })
  },

  /**
   * 加载模拟银行列表数据
   */
  loadMockBankList: function () {
    // 模拟银行数据
    const bankData = [
      { id: 1, name: '中国邮政储蓄银行', initial: 'Z', icon: '/static/icon/bank/youzheng.png' },
      { id: 2, name: '中国工商银行', initial: 'Z', icon: '/static/icon/bank/gongshang.png' },
      { id: 3, name: '中国农业银行', initial: 'Z', icon: '/static/icon/bank/nongye.png' },
      { id: 4, name: '中国银行', initial: 'Z', icon: '/static/icon/bank/zhongguo.png' },
      { id: 5, name: '建设银行', initial: 'J', icon: '/static/icon/bank/jianshe.png' },
      { id: 6, name: '交通银行', initial: 'J', icon: '/static/icon/bank/jiaotong.png' },
      { id: 7, name: '招商银行', initial: 'Z', icon: '/static/icon/bank/zhaoshang.png' },
      { id: 8, name: '安顺市商业银行', initial: 'A', icon: '/static/icon/bank/anshun.png' },
      { id: 9, name: '安阳银行', initial: 'A', icon: '/static/icon/bank/anyang.png' },
      { id: 10, name: '澳新银行', initial: 'A', icon: '/static/icon/bank/aoxin.png' },
      { id: 11, name: '澳门商业银行', initial: 'A', icon: '/static/icon/bank/aomen.png' },
      { id: 12, name: '鞍山银行', initial: 'A', icon: '/static/icon/bank/anshan.png' }
    ]

    // 处理数据，按字母排序并添加索引标题
    const processedData = this.processAndSortBankList(bankData)

    this.setData({
      originalBankList: bankData,
      bankList: processedData,
      loading: false
    })
  },

  /**
   * 格式化API返回的银行数据
   */
  formatBankData: function (apiData) {
    if (!apiData) return []

    let result = []

    // 处理按字母分组的数据
    Object.keys(apiData).forEach((key) => {
      // 确保是字母分组且是数组
      if (Array.isArray(apiData[key])) {
        apiData[key].forEach((bank) => {
          result.push({
            id: bank.id,
            name: bank.name,
            initial: bank.initial || key,
            icon: bank.image ? util.getImageUrl(bank.image) : '/static/icon/bank/default.png'
          })
        })
      }
    })

    return result
  },

  /**
   * 获取中文字符串的拼音首字母
   */
  getInitial: function (str) {
    if (!str) return '#'

    // 简单处理：根据首字符直接映射
    // 实际项目中可以使用更完善的拼音转换库
    const firstChar = str.charAt(0)

    // 常见中文字符的首字母映射
    const pinyinMap = {
      中: 'Z',
      国: 'G',
      工: 'G',
      商: 'S',
      农: 'N',
      建: 'J',
      设: 'S',
      交: 'J',
      通: 'T',
      招: 'Z',
      安: 'A',
      顺: 'S',
      阳: 'Y',
      澳: 'A',
      新: 'X',
      门: 'M',
      鞍: 'A',
      山: 'S'
    }

    // 如果是英文字母，直接返回大写
    if (/[a-zA-Z]/.test(firstChar)) {
      return firstChar.toUpperCase()
    }

    // 如果在映射表中，返回对应拼音首字母
    if (pinyinMap[firstChar]) {
      return pinyinMap[firstChar]
    }

    // 其他情况返回#
    return '#'
  },

  /**
   * 处理并排序银行列表
   */
  processAndSortBankList: function (bankList) {
    if (!bankList || !bankList.length) return []

    // 按拼音首字母排序
    bankList.sort((a, b) => {
      if (a.initial < b.initial) return -1
      if (a.initial > b.initial) return 1
      return a.name.localeCompare(b.name, 'zh')
    })

    // 添加字母索引标题
    const result = []
    let currentInitial = ''

    bankList.forEach((bank) => {
      if (bank.initial !== currentInitial) {
        currentInitial = bank.initial
        // 添加字母索引标题
        result.push({
          initial: currentInitial,
          isInitial: true
        })
      }
      // 添加银行项
      result.push({
        ...bank,
        isInitial: false
      })
    })

    return result
  },

  /**
   * 搜索输入事件
   */
  onSearchInput: function (e) {
    const keyword = e.detail.value.trim()

    this.setData({
      searchKeyword: keyword
    })

    if (!keyword) {
      // 如果关键词为空，恢复原始列表
      const processedData = this.processAndSortBankList(this.data.originalBankList)
      this.setData({
        bankList: processedData
      })
      return
    }

    // 根据关键词过滤银行列表
    const filteredList = this.data.originalBankList.filter((bank) => bank.name.indexOf(keyword) !== -1)

    // 处理并排序过滤后的列表
    const processedData = this.processAndSortBankList(filteredList)

    this.setData({
      bankList: processedData
    })
  },

  /**
   * 点击字母索引
   */
  onLetterTap: function (e) {
    const letter = e.currentTarget.dataset.letter

    this.setData({
      currentLetter: letter,
      scrollIntoView: letter
    })
  },

  /**
   * 选择银行
   */
  selectBank: function (e) {
    const bank = e.currentTarget.dataset.bank

    // 将bank对象转换为JSON字符串，并进行URL编码
    const bankStr = encodeURIComponent(JSON.stringify(bank))

    // 返回上一页并传递选中的银行数据
    wx.navigateBack({
      delta: 1,
      success: function () {
        // 通过事件通道发送数据
        const eventChannel = getOpenerEventChannel()
        if (eventChannel) {
          eventChannel.emit('bankSelected', { bank: bank })
        }
      }
    })
  },

  /**
   * 返回上一页
   */
  navigateBack: function () {
    wx.navigateBack()
  },

  /**
   * 跳转到自定义页面
   */
  navigateToCustom: function () {
    // 显示自定义图片弹窗
    this.setData({
      showCustomPopup: true,
      customImageUrl: '',
      customImageName: ''
    })
  },

  /**
   * 关闭自定义图片弹窗
   */
  onCustomPopupClose: function () {
    this.setData({
      showCustomPopup: false
    })
  },

  /**
   * 图片链接输入事件
   */
  onImageUrlInput: function (e) {
    this.setData({
      customImageUrl: e.detail.value
    })
  },

  /**
   * 图片名称输入事件
   */
  onImageNameInput: function (e) {
    this.setData({
      customImageName: e.detail.value
    })
  },

  /**
   * 保存自定义图片
   */
  onSaveCustomImage: function () {
    const { customImageUrl, customImageName } = this.data

    if (!customImageUrl.trim()) {
      wx.showToast({
        title: '请输入图片链接',
        icon: 'none'
      })
      return
    }

    if (!customImageName.trim()) {
      wx.showToast({
        title: '请输入图片名称',
        icon: 'none'
      })
      return
    }

    // 显示加载中
    wx.showLoading({
      title: '保存中...'
    })

    // 构建请求参数
    const params = {
      name: customImageName,
      image: customImageUrl
    }

    // 调用API保存自定义银行
    wx.request({
      url: util.getBaseUrl() + '/api/account/add_bank',
      method: 'POST',
      data: params,
      header: util.getCommonHeader(),
      success: (res) => {
        wx.hideLoading()

        if (res.data && res.data.code === 1) {
          wx.showToast({
            title: res.data.msg || '保存成功',
            icon: 'success'
          })

          // 关闭弹窗
          this.setData({
            showCustomPopup: false
          })

          // 创建一个新的银行对象
          const newBank = {
            id: new Date().getTime(), // 临时ID
            name: customImageName,
            icon: util.getImageUrl(customImageUrl),
            initial: this.getInitial(customImageName),
            isCustom: true
          }

          // 直接选择新创建的银行
          this.selectBank({
            currentTarget: {
              dataset: {
                bank: newBank
              }
            }
          })
        } else {
          wx.showToast({
            title: (res.data && res.data.msg) || '保存失败',
            icon: 'none'
          })
        }
      },
      fail: (err) => {
        wx.hideLoading()
        wx.showToast({
          title: '网络错误，请重试',
          icon: 'none'
        })
        console.error('保存自定义银行失败:', err)
      }
    })
  }
})
